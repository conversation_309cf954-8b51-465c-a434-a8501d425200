import { CreditService } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import {
    addCreditsSchema,
    consumeCreditsSchema,
    creditTransactionResponseSchema,
    errorResponseSchema,
    paginatedResponseSchema,
    transactionIdSchema,
    transactionQuerySchema,
    transferCreditsSchema,
} from "./schemas";

// 创建积分服务实例
const creditService = new CreditService(db);

/**
 * 积分交易管理路由
 */
export const transactionRouter = new Hono()
  // 获取交易历史
  .get(
    "/",
    authMiddleware,
    validator("query", transactionQuerySchema),
    describeRoute({
      tags: ["Credits"],
      summary: "获取积分交易历史",
      description: "获取用户或组织的积分交易历史记录",
      responses: {
        200: {
          description: "交易历史列表",
          content: {
            "application/json": {
              schema: resolver(paginatedResponseSchema(creditTransactionResponseSchema)),
            },
          },
        },
        400: {
          description: "请求参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const query = c.req.valid("query");

        // 确定查询目标
        const targetUserId = query.userId || user.id;
        const targetOrgId = query.organizationId;

        // 权限检查
        if (query.userId && query.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权查看其他用户的交易记录" });
        }

        // 获取账户ID
        let accountId = query.accountId;
        if (!accountId) {
          const account = await creditService.getOrCreateAccount({
            userId: targetUserId,
            organizationId: targetOrgId,
          });
          accountId = account.id;
        }

        // 构建查询参数
        const queryParams = {
          accountId,
          type: query.type,
          startDate: query.startDate ? new Date(query.startDate) : undefined,
          endDate: query.endDate ? new Date(query.endDate) : undefined,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        };

        const result = await creditService.getTransactionHistory(queryParams);

        const response = {
          data: result.data.map(tx => ({
            id: tx.id,
            accountId: tx.accountId,
            type: tx.type,
            amount: tx.amount,
            reason: tx.reason,
            featureId: tx.featureId,
            purchaseId: tx.purchaseId,
            expiresAt: tx.expiresAt?.toISOString() || null,
            metadata: tx.metadata,
            createdAt: tx.createdAt.toISOString(),
          })),
          pagination: result.pagination,
        };

        logger.info("积分交易历史查询成功", {
          userId: targetUserId,
          organizationId: targetOrgId,
          accountId,
          resultCount: result.data.length,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分交易历史失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "获取交易历史失败" });
      }
    }
  )

  // 获取特定交易详情
  .get(
    "/:id",
    authMiddleware,
    validator("param", { id: transactionIdSchema }),
    describeRoute({
      tags: ["Credits"],
      summary: "获取交易详情",
      description: "获取特定积分交易的详细信息",
      responses: {
        200: {
          description: "交易详情",
          content: {
            "application/json": {
              schema: resolver(creditTransactionResponseSchema),
            },
          },
        },
        404: {
          description: "交易不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");

        const transaction = await creditService.getTransaction(id);

        if (!transaction) {
          throw new HTTPException(404, { message: "交易记录不存在" });
        }

        // 权限检查：验证用户是否有权查看此交易
        const account = await creditService.getAccountById(transaction.accountId);
        if (!account) {
          throw new HTTPException(404, { message: "关联账户不存在" });
        }

        if (account.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权查看此交易记录" });
        }

        const response = {
          id: transaction.id,
          accountId: transaction.accountId,
          type: transaction.type,
          amount: transaction.amount,
          reason: transaction.reason,
          featureId: transaction.featureId,
          purchaseId: transaction.purchaseId,
          expiresAt: transaction.expiresAt?.toISOString() || null,
          metadata: transaction.metadata,
          createdAt: transaction.createdAt.toISOString(),
        };

        logger.info("积分交易详情查询成功", {
          transactionId: id,
          userId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分交易详情失败", {
          error: error instanceof Error ? error.message : String(error),
          transactionId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "获取交易详情失败" });
      }
    }
  )

  // 消耗积分
  .post(
    "/consume",
    authMiddleware,
    validator("json", consumeCreditsSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "消耗积分",
      description: "从用户或组织账户中消耗指定数量的积分",
      responses: {
        200: {
          description: "积分消耗成功",
          content: {
            "application/json": {
              schema: resolver(creditTransactionResponseSchema),
            },
          },
        },
        400: {
          description: "余额不足或参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 确定目标账户
        const targetUserId = data.userId || user.id;
        const targetOrgId = data.organizationId;

        // 权限检查
        if (data.userId && data.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权操作其他用户的积分" });
        }

        // 获取账户ID
        let accountId = data.accountId;
        if (!accountId) {
          const account = await creditService.getOrCreateAccount({
            userId: targetUserId,
            organizationId: targetOrgId,
          });
          accountId = account.id;
        }

        const transaction = await creditService.consumeCredits({
          accountId,
          amount: data.amount,
          reason: data.reason,
          featureId: data.featureId,
          metadata: data.metadata,
        });

        const response = {
          id: transaction.id,
          accountId: transaction.accountId,
          type: transaction.type,
          amount: transaction.amount,
          reason: transaction.reason,
          featureId: transaction.featureId,
          purchaseId: transaction.purchaseId,
          expiresAt: transaction.expiresAt?.toISOString() || null,
          metadata: transaction.metadata,
          createdAt: transaction.createdAt.toISOString(),
        };

        logger.info("积分消耗成功", {
          transactionId: transaction.id,
          accountId,
          amount: data.amount,
          reason: data.reason,
          featureId: data.featureId,
          userId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("积分消耗失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是余额不足错误
        if (error instanceof Error && error.message.includes("Insufficient")) {
          throw new HTTPException(400, { message: "积分余额不足" });
        }

        throw new HTTPException(500, { message: "积分消耗失败" });
      }
    }
  )

  // 添加积分
  .post(
    "/add",
    authMiddleware,
    validator("json", addCreditsSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "添加积分",
      description: "向用户或组织账户添加积分",
      responses: {
        200: {
          description: "积分添加成功",
          content: {
            "application/json": {
              schema: resolver(creditTransactionResponseSchema),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 只有管理员或系统可以直接添加积分
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以直接添加积分" });
        }

        // 确定目标账户
        const targetUserId = data.userId || user.id;
        const targetOrgId = data.organizationId;

        // 获取账户ID
        let accountId = data.accountId;
        if (!accountId) {
          const account = await creditService.getOrCreateAccount({
            userId: targetUserId,
            organizationId: targetOrgId,
          });
          accountId = account.id;
        }

        const transaction = await creditService.addCredits({
          accountId,
          amount: data.amount,
          reason: data.reason,
          type: data.type,
          expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
          metadata: data.metadata,
        });

        const response = {
          id: transaction.id,
          accountId: transaction.accountId,
          type: transaction.type,
          amount: transaction.amount,
          reason: transaction.reason,
          featureId: transaction.featureId,
          purchaseId: transaction.purchaseId,
          expiresAt: transaction.expiresAt?.toISOString() || null,
          metadata: transaction.metadata,
          createdAt: transaction.createdAt.toISOString(),
        };

        logger.info("积分添加成功", {
          transactionId: transaction.id,
          accountId,
          amount: data.amount,
          type: data.type,
          reason: data.reason,
          adminUserId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("积分添加失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "积分添加失败" });
      }
    }
  )

  // 积分转账
  .post(
    "/transfer",
    authMiddleware,
    validator("json", transferCreditsSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "积分转账",
      description: "在用户或组织之间转移积分",
      responses: {
        200: {
          description: "转账成功",
          content: {
            "application/json": {
              schema: resolver(z.object({
                fromTransaction: creditTransactionResponseSchema,
                toTransaction: creditTransactionResponseSchema,
              })),
            },
          },
        },
        400: {
          description: "余额不足或参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 确定发送方账户
        const fromUserId = data.fromUserId || user.id;
        const fromOrgId = data.fromOrganizationId;

        // 权限检查：用户只能从自己的账户转出
        if (data.fromUserId && data.fromUserId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权从其他用户账户转出积分" });
        }

        // 获取发送方账户ID
        let fromAccountId = data.fromAccountId;
        if (!fromAccountId) {
          const fromAccount = await creditService.getOrCreateAccount({
            userId: fromUserId,
            organizationId: fromOrgId,
          });
          fromAccountId = fromAccount.id;
        }

        // 获取接收方账户ID
        let toAccountId = data.toAccountId;
        if (!toAccountId) {
          const toAccount = await creditService.getOrCreateAccount({
            userId: data.toUserId,
            organizationId: data.toOrganizationId,
          });
          toAccountId = toAccount.id;
        }

        const result = await creditService.transferCredits({
          fromAccountId,
          toAccountId,
          amount: data.amount,
          reason: data.reason,
          metadata: data.metadata,
        });

        const response = {
          fromTransaction: {
            id: result.fromTransaction.id,
            accountId: result.fromTransaction.accountId,
            type: result.fromTransaction.type,
            amount: result.fromTransaction.amount,
            reason: result.fromTransaction.reason,
            featureId: result.fromTransaction.featureId,
            purchaseId: result.fromTransaction.purchaseId,
            expiresAt: result.fromTransaction.expiresAt?.toISOString() || null,
            metadata: result.fromTransaction.metadata,
            createdAt: result.fromTransaction.createdAt.toISOString(),
          },
          toTransaction: {
            id: result.toTransaction.id,
            accountId: result.toTransaction.accountId,
            type: result.toTransaction.type,
            amount: result.toTransaction.amount,
            reason: result.toTransaction.reason,
            featureId: result.toTransaction.featureId,
            purchaseId: result.toTransaction.purchaseId,
            expiresAt: result.toTransaction.expiresAt?.toISOString() || null,
            metadata: result.toTransaction.metadata,
            createdAt: result.toTransaction.createdAt.toISOString(),
          },
        };

        logger.info("积分转账成功", {
          fromAccountId,
          toAccountId,
          amount: data.amount,
          reason: data.reason,
          fromTransactionId: result.fromTransaction.id,
          toTransactionId: result.toTransaction.id,
          userId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("积分转账失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是余额不足错误
        if (error instanceof Error && error.message.includes("Insufficient")) {
          throw new HTTPException(400, { message: "积分余额不足" });
        }

        throw new HTTPException(500, { message: "积分转账失败" });
      }
    }
  );
