import type { CreditTransactionType, CreditPeriod, CreditExpirationPolicy } from '../types/credit-types';

// ================================
// 功能积分消耗配置
// ================================

export interface FeatureCreditCost {
  featureId: string;
  name: string;
  description: string;
  cost: number;
  category: string;
}

export const FEATURE_CREDIT_COSTS: Record<string, FeatureCreditCost> = {
  AI_CHAT: {
    featureId: 'ai_chat',
    name: 'AI聊天对话',
    description: '与AI助手进行对话',
    cost: 5,
    category: 'ai'
  },
  MAP_EXPORT: {
    featureId: 'map_export',
    name: '地图导出',
    description: '导出高清地图图片',
    cost: 20,
    category: 'export'
  },
  VIDEO_GENERATION: {
    featureId: 'video_generation',
    name: '视频生成',
    description: '生成旅行视频',
    cost: 50,
    category: 'ai'
  },
  PREMIUM_TEMPLATE: {
    featureId: 'premium_template',
    name: '高级模板使用',
    description: '使用高级日记模板',
    cost: 10,
    category: 'template'
  },
  BATCH_OPERATION: {
    featureId: 'batch_operation',
    name: '批量操作',
    description: '批量处理数据',
    cost: 30,
    category: 'operation'
  },
  DATA_ANALYTICS: {
    featureId: 'data_analytics',
    name: '数据分析报告',
    description: '生成详细的数据分析报告',
    cost: 25,
    category: 'analytics'
  },
  CUSTOM_THEME: {
    featureId: 'custom_theme',
    name: '自定义主题',
    description: '使用自定义主题',
    cost: 15,
    category: 'customization'
  },
  ADVANCED_SEARCH: {
    featureId: 'advanced_search',
    name: '高级搜索',
    description: '使用高级搜索功能',
    cost: 8,
    category: 'search'
  },
  DATA_BACKUP: {
    featureId: 'data_backup',
    name: '数据备份',
    description: '备份用户数据',
    cost: 40,
    category: 'backup'
  },
  API_CALL: {
    featureId: 'api_call',
    name: 'API调用',
    description: '第三方API调用',
    cost: 2,
    category: 'api'
  }
};

// ================================
// 订阅计划积分分配配置
// ================================

export interface SubscriptionCreditAllocation {
  planId: string;
  planName: string;
  creditsPerPeriod: number;
  period: CreditPeriod;
  expirationPolicy: CreditExpirationPolicy;
  expirationDays?: number;
}

export const SUBSCRIPTION_CREDIT_ALLOCATIONS: Record<string, SubscriptionCreditAllocation> = {
  FREE: {
    planId: 'free',
    planName: '免费计划',
    creditsPerPeriod: 50,
    period: 'MONTHLY',
    expirationPolicy: 'MONTHLY_RESET'
  },
  BASIC: {
    planId: 'basic',
    planName: '基础计划',
    creditsPerPeriod: 200,
    period: 'MONTHLY',
    expirationPolicy: 'ROLLING_EXPIRATION',
    expirationDays: 90
  },
  PRO: {
    planId: 'pro',
    planName: '专业计划',
    creditsPerPeriod: 500,
    period: 'MONTHLY',
    expirationPolicy: 'ROLLING_EXPIRATION',
    expirationDays: 180
  },
  ENTERPRISE: {
    planId: 'enterprise',
    planName: '企业计划',
    creditsPerPeriod: 2000,
    period: 'MONTHLY',
    expirationPolicy: 'NEVER_EXPIRE'
  }
};

// ================================
// 积分系统配置
// ================================

export interface CreditSystemConfig {
  // 基础配置
  defaultCurrency: string;
  minTransferAmount: number;
  maxTransferAmount: number;
  maxDailyTransfers: number;
  
  // 过期配置
  defaultExpirationDays: number;
  expirationCheckInterval: number; // 小时
  
  // 分配配置
  allocationGracePeriod: number; // 分钟
  maxPendingAllocations: number;
  
  // 安全配置
  maxTransactionAmount: number;
  suspiciousActivityThreshold: number;
  
  // 缓存配置
  balanceCacheTTL: number; // 秒
  transactionCacheTTL: number; // 秒
  
  // 通知配置
  lowBalanceThreshold: number;
  expirationWarningDays: number;
}

export const DEFAULT_CREDIT_CONFIG: CreditSystemConfig = {
  // 基础配置
  defaultCurrency: 'USD',
  minTransferAmount: 1,
  maxTransferAmount: 10000,
  maxDailyTransfers: 10,
  
  // 过期配置
  defaultExpirationDays: 365,
  expirationCheckInterval: 24, // 每24小时检查一次
  
  // 分配配置
  allocationGracePeriod: 30, // 30分钟宽限期
  maxPendingAllocations: 100,
  
  // 安全配置
  maxTransactionAmount: 100000,
  suspiciousActivityThreshold: 50, // 每小时超过50次交易视为可疑
  
  // 缓存配置
  balanceCacheTTL: 300, // 5分钟
  transactionCacheTTL: 600, // 10分钟
  
  // 通知配置
  lowBalanceThreshold: 10,
  expirationWarningDays: 7
};

// ================================
// 工具函数
// ================================

export function getFeatureCreditCost(featureId: string): number {
  const feature = Object.values(FEATURE_CREDIT_COSTS).find(f => f.featureId === featureId);
  return feature?.cost ?? 0;
}

export function getSubscriptionCreditAllocation(planId: string): SubscriptionCreditAllocation | null {
  return SUBSCRIPTION_CREDIT_ALLOCATIONS[planId.toUpperCase()] ?? null;
}

export function isFeatureAvailable(featureId: string): boolean {
  return Object.values(FEATURE_CREDIT_COSTS).some(f => f.featureId === featureId);
}

export function getFeaturesByCategory(category: string): FeatureCreditCost[] {
  return Object.values(FEATURE_CREDIT_COSTS).filter(f => f.category === category);
}

export function calculateNextAllocationDate(
  period: CreditPeriod,
  lastAllocation?: Date
): Date {
  const now = lastAllocation ?? new Date();
  const next = new Date(now);
  
  switch (period) {
    case 'DAILY':
      next.setDate(next.getDate() + 1);
      break;
    case 'WEEKLY':
      next.setDate(next.getDate() + 7);
      break;
    case 'MONTHLY':
      next.setMonth(next.getMonth() + 1);
      break;
    case 'YEARLY':
      next.setFullYear(next.getFullYear() + 1);
      break;
  }
  
  return next;
}

export function calculateExpirationDate(
  policy: CreditExpirationPolicy,
  expirationDays?: number,
  allocationDate?: Date
): Date | null {
  if (policy === 'NEVER_EXPIRE') {
    return null;
  }
  
  const baseDate = allocationDate ?? new Date();
  const expiration = new Date(baseDate);
  
  switch (policy) {
    case 'MONTHLY_RESET':
      expiration.setMonth(expiration.getMonth() + 1);
      expiration.setDate(1);
      expiration.setHours(0, 0, 0, 0);
      break;
    case 'ROLLING_EXPIRATION':
      if (expirationDays) {
        expiration.setDate(expiration.getDate() + expirationDays);
      } else {
        expiration.setDate(expiration.getDate() + DEFAULT_CREDIT_CONFIG.defaultExpirationDays);
      }
      break;
    case 'SUBSCRIPTION_BASED':
      // 基于订阅周期，这里需要根据具体订阅信息计算
      // 暂时使用默认过期天数
      expiration.setDate(expiration.getDate() + (expirationDays ?? DEFAULT_CREDIT_CONFIG.defaultExpirationDays));
      break;
  }
  
  return expiration;
}

// ================================
// 验证函数
// ================================

export function validateCreditAmount(amount: number): boolean {
  return Number.isInteger(amount) && amount > 0 && amount <= DEFAULT_CREDIT_CONFIG.maxTransactionAmount;
}

export function validateTransferAmount(amount: number): boolean {
  return validateCreditAmount(amount) && 
         amount >= DEFAULT_CREDIT_CONFIG.minTransferAmount && 
         amount <= DEFAULT_CREDIT_CONFIG.maxTransferAmount;
}

export function isLowBalance(balance: number): boolean {
  return balance <= DEFAULT_CREDIT_CONFIG.lowBalanceThreshold;
}

export function isExpirationWarningNeeded(expiresAt: Date): boolean {
  if (!expiresAt) return false;
  
  const now = new Date();
  const warningDate = new Date(expiresAt);
  warningDate.setDate(warningDate.getDate() - DEFAULT_CREDIT_CONFIG.expirationWarningDays);
  
  return now >= warningDate && now < expiresAt;
}
