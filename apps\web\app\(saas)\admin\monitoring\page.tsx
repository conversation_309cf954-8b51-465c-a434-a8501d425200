import { Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Activity, AlertTriangle, BarChart3, Database, Globe, Zap } from 'lucide-react';
import { MetricsChart } from './components/MetricsChart';
import { ErrorsList } from './components/ErrorsList';
import { PerformanceMetrics } from './components/PerformanceMetrics';
import { SystemHealth } from './components/SystemHealth';

export default function MonitoringPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">系统监控</h1>
          <p className="text-muted-foreground">
            实时监控应用性能、错误和业务指标
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          系统正常
        </Badge>
      </div>

      {/* 系统健康状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API 响应时间</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">245ms</div>
            <p className="text-xs text-muted-foreground">
              +2% 比昨天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">错误率</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0.12%</div>
            <p className="text-xs text-muted-foreground">
              -0.05% 比昨天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +12% 比昨天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库查询</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89ms</div>
            <p className="text-xs text-muted-foreground">
              -5ms 比昨天
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细监控面板 */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">性能监控</TabsTrigger>
          <TabsTrigger value="errors">错误追踪</TabsTrigger>
          <TabsTrigger value="business">业务指标</TabsTrigger>
          <TabsTrigger value="system">系统状态</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>API 响应时间趋势</CardTitle>
                <CardDescription>
                  过去24小时的API响应时间变化
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<div>加载中...</div>}>
                  <MetricsChart metric="api.response.duration" />
                </Suspense>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>数据库查询性能</CardTitle>
                <CardDescription>
                  数据库查询响应时间分布
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Suspense fallback={<div>加载中...</div>}>
                  <MetricsChart metric="database.query.duration" />
                </Suspense>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>性能指标详情</CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>加载中...</div>}>
                <PerformanceMetrics />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近错误</CardTitle>
              <CardDescription>
                系统中发生的最新错误和异常
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>加载中...</div>}>
                <ErrorsList />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>用户活跃度</CardTitle>
                <CardDescription>
                  每日活跃用户数变化趋势
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MetricsChart metric="user.active.daily" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>功能使用情况</CardTitle>
                <CardDescription>
                  各功能模块的使用频率
                </CardDescription>
              </CardHeader>
              <CardContent>
                <MetricsChart metric="feature.usage" />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>系统健康状态</CardTitle>
              <CardDescription>
                服务器和基础设施状态监控
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>加载中...</div>}>
                <SystemHealth />
              </Suspense>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
