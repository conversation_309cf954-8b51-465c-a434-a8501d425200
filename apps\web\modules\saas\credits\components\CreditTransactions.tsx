"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import {
    ArrowDownCircle,
    ArrowUpCircle,
    Calendar,
    ChevronLeft,
    ChevronRight
} from "lucide-react";
import { useState } from "react";
import { useCreditAccount, useCreditTransactions } from "../hooks/useCredits";

import { cn } from "@ui/lib";

interface CreditTransactionsProps {
  className?: string;
  pageSize?: number;
  showFilters?: boolean;
}

/**
 * 积分交易记录组件
 */
export function CreditTransactions({
  className,
  pageSize = 10,
  showFilters = true,
}: CreditTransactionsProps) {
  const { data: account } = useCreditAccount();
  const [currentPage, setCurrentPage] = useState(1);
  const [typeFilter, setTypeFilter] = useState<string>("all");

  const { data: transactionsData, isLoading, error } = useCreditTransactions(
    account?.id,
    {
      page: currentPage,
      limit: pageSize,
      type: typeFilter === "all" ? undefined : typeFilter,
    }
  );

  if (isLoading) {
    return <CreditTransactionsSkeleton className={className} />;
  }

  if (error || !transactionsData) {
    return (
      <Card className={cn("border-destructive/20", className)}>
        <CardContent className="p-6 text-center">
          <div className="text-destructive">
            <Calendar className="h-8 w-8 mx-auto mb-2" />
            <p>交易记录加载失败</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { transactions, total, page, limit } = transactionsData;
  const totalPages = Math.ceil(total / limit);

  const getTransactionIcon = (type: string) => {
    if (type.startsWith("EARNED_") || type.startsWith("PURCHASED_")) {
      return ArrowUpCircle;
    }
    return ArrowDownCircle;
  };

  const getTransactionColor = (type: string) => {
    if (type.startsWith("EARNED_") || type.startsWith("PURCHASED_")) {
      return "text-green-600";
    }
    return "text-orange-600";
  };

  const getTransactionStatus = (status: string) => {
    const statusMap = {
      COMPLETED: { label: "已完成", status: "success" as const },
      PENDING: { label: "处理中", status: "warning" as const },
      FAILED: { label: "失败", status: "error" as const },
      EXPIRED: { label: "已过期", status: "error" as const },
    };
    return statusMap[status as keyof typeof statusMap] || { label: status, status: "info" as const };
  };

  const formatTransactionType = (type: string) => {
    const typeMap: Record<string, string> = {
      EARNED_SUBSCRIPTION: "订阅赠送",
      EARNED_PURCHASE: "购买获得",
      EARNED_BONUS: "奖励获得",
      EARNED_REFUND: "退款返还",
      CONSUMED_AI_GENERATION: "AI生成",
      CONSUMED_STORAGE_UPLOAD: "存储上传",
      CONSUMED_MAP_GEOCODING: "地图编码",
      CONSUMED_API_CALL: "API调用",
      CONSUMED_FEATURE_USE: "功能使用",
    };
    return typeMap[type] || type;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            积分交易记录
          </CardTitle>
          {showFilters && (
            <div className="flex items-center gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="EARNED_">获得</SelectItem>
                  <SelectItem value="CONSUMED_">消耗</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暂无交易记录</p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => {
              const Icon = getTransactionIcon(transaction.type);
              const colorClass = getTransactionColor(transaction.type);
              const statusInfo = getTransactionStatus(transaction.status);
              const isPositive = transaction.type.startsWith("EARNED_") || 
                                transaction.type.startsWith("PURCHASED_");

              return (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={cn("flex-shrink-0", colorClass)}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="font-medium">
                        {formatTransactionType(transaction.type)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {transaction.reason}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(transaction.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className={cn("font-semibold", colorClass)}>
                      {isPositive ? "+" : "-"}{Math.abs(transaction.amount).toLocaleString()}
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge status={statusInfo.status}>
                        {statusInfo.label}
                      </Badge>
                      {transaction.expiresAt && (
                        <span className="text-xs text-muted-foreground">
                          {new Date(transaction.expiresAt) > new Date() 
                            ? `${Math.ceil((new Date(transaction.expiresAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24))}天后过期`
                            : "已过期"
                          }
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-muted-foreground">
              显示 {(page - 1) * limit + 1} - {Math.min(page * limit, total)} 条，共 {total} 条
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(page - 1)}
                disabled={page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                上一页
              </Button>
              <span className="text-sm">
                {page} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(page + 1)}
                disabled={page >= totalPages}
              >
                下一页
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * 积分交易记录骨架屏
 */
function CreditTransactionsSkeleton({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-9 w-24" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div
              key={i}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Skeleton className="h-5 w-5 rounded" />
                <div>
                  <Skeleton className="h-4 w-24 mb-1" />
                  <Skeleton className="h-3 w-32 mb-1" />
                  <Skeleton className="h-3 w-28" />
                </div>
              </div>
              <div className="text-right">
                <Skeleton className="h-4 w-16 mb-1" />
                <Skeleton className="h-5 w-12" />
              </div>
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between mt-6">
          <Skeleton className="h-4 w-32" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
