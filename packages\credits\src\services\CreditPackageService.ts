import type { PrismaClient } from '@repo/database';
import type {
  CreditPackage,
  CreateCreditPackageInput,
  PackageStatus,
  PaginatedResponse
} from '../types/credit-types';
import {
  CreditErrors,
  type CreditError
} from '../types/credit-errors';
import {
  createPaginatedResponse,
  calculateOffset
} from '../utils/credit-helpers';
import { CreditService } from './CreditService';

/**
 * 积分包服务
 * 负责积分包的管理、购买处理和库存管理
 */
export class CreditPackageService {
  private creditService: CreditService;

  constructor(private readonly db: PrismaClient) {
    this.creditService = new CreditService(db);
  }

  // ================================
  // 积分包管理
  // ================================

  /**
   * 创建积分包
   */
  async createPackage(input: CreateCreditPackageInput): Promise<CreditPackage> {
    try {
      // 验证输入
      if (input.creditAmount <= 0) {
        throw CreditErrors.validation('creditAmount', input.creditAmount, 'Credit amount must be positive');
      }

      if (input.price < 0) {
        throw CreditErrors.validation('price', input.price, 'Price cannot be negative');
      }

      if (!input.productIds || Object.keys(input.productIds).length === 0) {
        throw CreditErrors.validation('productIds', input.productIds, 'At least one product ID is required');
      }

      const packageData = await this.db.creditPackage.create({
        data: {
          name: input.name,
          description: input.description,
          creditAmount: input.creditAmount,
          price: input.price,
          currency: input.currency || 'USD',
          productIds: input.productIds,
          validityDays: input.validityDays,
          status: 'ACTIVE',
          featured: input.featured || false,
          sortOrder: input.sortOrder || 0
        }
      });

      return packageData as CreditPackage;
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('createPackage', error as Error);
    }
  }

  /**
   * 获取积分包列表
   */
  async getPackages(
    status?: PackageStatus,
    page: number = 1,
    limit: number = 20
  ): Promise<PaginatedResponse<CreditPackage>> {
    try {
      const offset = calculateOffset(page, limit);
      const where: any = {};
      
      if (status) {
        where.status = status;
      }

      const [packages, total] = await Promise.all([
        this.db.creditPackage.findMany({
          where,
          orderBy: [
            { featured: 'desc' },
            { sortOrder: 'asc' },
            { createdAt: 'desc' }
          ],
          skip: offset,
          take: limit
        }),
        this.db.creditPackage.count({ where })
      ]);

      return createPaginatedResponse(
        packages as CreditPackage[],
        total,
        page,
        limit
      );
    } catch (error) {
      throw CreditErrors.database('getPackages', error as Error);
    }
  }

  /**
   * 获取活跃的积分包
   */
  async getActivePackages(): Promise<CreditPackage[]> {
    try {
      const packages = await this.db.creditPackage.findMany({
        where: { status: 'ACTIVE' },
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { price: 'asc' }
        ]
      });

      return packages as CreditPackage[];
    } catch (error) {
      throw CreditErrors.database('getActivePackages', error as Error);
    }
  }

  /**
   * 获取推荐积分包
   */
  async getFeaturedPackages(): Promise<CreditPackage[]> {
    try {
      const packages = await this.db.creditPackage.findMany({
        where: { 
          status: 'ACTIVE',
          featured: true
        },
        orderBy: [
          { sortOrder: 'asc' },
          { price: 'asc' }
        ]
      });

      return packages as CreditPackage[];
    } catch (error) {
      throw CreditErrors.database('getFeaturedPackages', error as Error);
    }
  }

  /**
   * 根据ID获取积分包
   */
  async getPackage(packageId: string): Promise<CreditPackage | null> {
    try {
      const packageData = await this.db.creditPackage.findUnique({
        where: { id: packageId }
      });

      return packageData as CreditPackage | null;
    } catch (error) {
      throw CreditErrors.database('getPackage', error as Error);
    }
  }

  /**
   * 根据产品ID获取积分包
   */
  async getPackageByProductId(provider: string, productId: string): Promise<CreditPackage | null> {
    try {
      const packages = await this.db.creditPackage.findMany({
        where: { status: 'ACTIVE' }
      });

      // 在应用层过滤，因为Prisma不支持JSON字段的复杂查询
      const matchingPackage = packages.find(pkg => {
        const productIds = pkg.productIds as Record<string, string>;
        return productIds[provider] === productId;
      });

      return matchingPackage as CreditPackage | null;
    } catch (error) {
      throw CreditErrors.database('getPackageByProductId', error as Error);
    }
  }

  /**
   * 更新积分包
   */
  async updatePackage(
    packageId: string,
    updates: Partial<CreateCreditPackageInput>
  ): Promise<CreditPackage> {
    try {
      const packageData = await this.db.creditPackage.update({
        where: { id: packageId },
        data: {
          ...updates,
          updatedAt: new Date()
        }
      });

      return packageData as CreditPackage;
    } catch (error) {
      if (error.code === 'P2025') {
        throw CreditErrors.packageNotFound(packageId);
      }
      throw CreditErrors.database('updatePackage', error as Error);
    }
  }

  /**
   * 更新积分包状态
   */
  async updatePackageStatus(
    packageId: string,
    status: PackageStatus
  ): Promise<CreditPackage> {
    try {
      const packageData = await this.db.creditPackage.update({
        where: { id: packageId },
        data: { 
          status,
          updatedAt: new Date()
        }
      });

      return packageData as CreditPackage;
    } catch (error) {
      if (error.code === 'P2025') {
        throw CreditErrors.packageNotFound(packageId);
      }
      throw CreditErrors.database('updatePackageStatus', error as Error);
    }
  }

  /**
   * 删除积分包
   */
  async deletePackage(packageId: string): Promise<void> {
    try {
      await this.db.creditPackage.delete({
        where: { id: packageId }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw CreditErrors.packageNotFound(packageId);
      }
      throw CreditErrors.database('deletePackage', error as Error);
    }
  }

  // ================================
  // 积分包购买处理
  // ================================

  /**
   * 处理积分包购买
   */
  async processPurchase(
    packageId: string,
    accountId: string,
    purchaseId: string,
    metadata?: Record<string, any>
  ): Promise<{
    package: CreditPackage;
    transaction: any;
  }> {
    try {
      return await this.db.$transaction(async (tx) => {
        // 获取积分包信息
        const packageData = await tx.creditPackage.findUnique({
          where: { id: packageId }
        });

        if (!packageData) {
          throw CreditErrors.packageNotFound(packageId);
        }

        if (packageData.status !== 'ACTIVE') {
          throw CreditErrors.packageInactive(packageId);
        }

        // 验证账户存在
        const account = await tx.creditAccount.findUnique({
          where: { id: accountId }
        });

        if (!account) {
          throw CreditErrors.accountNotFound(accountId);
        }

        if (account.status !== 'ACTIVE') {
          throw CreditErrors.accountSuspended(accountId);
        }

        // 计算过期时间
        let expiresAt: Date | null = null;
        if (packageData.validityDays) {
          expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + packageData.validityDays);
        }

        // 添加积分
        const transaction = await this.creditService.addCredits({
          accountId,
          amount: packageData.creditAmount,
          type: 'EARNED_PURCHASE',
          reason: `Credit package purchase: ${packageData.name}`,
          description: `Purchased ${packageData.creditAmount} credits from ${packageData.name} package`,
          metadata: {
            packageId,
            packageName: packageData.name,
            price: packageData.price,
            currency: packageData.currency,
            validityDays: packageData.validityDays,
            ...metadata
          },
          purchaseId,
          expiresAt
        });

        return {
          package: packageData as CreditPackage,
          transaction
        };
      });
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('processPurchase', error as Error);
    }
  }

  /**
   * 处理积分包退款
   */
  async processRefund(
    purchaseId: string,
    accountId: string,
    refundAmount?: number,
    reason?: string
  ): Promise<any> {
    try {
      return await this.db.$transaction(async (tx) => {
        // 查找原始购买交易
        const originalTransaction = await tx.creditTransaction.findFirst({
          where: {
            purchaseId,
            accountId,
            type: 'EARNED_PURCHASE'
          }
        });

        if (!originalTransaction) {
          throw new CreditError(
            'TRANSACTION_NOT_FOUND',
            `Purchase transaction not found: ${purchaseId}`,
            { purchaseId, accountId }
          );
        }

        // 计算退款积分数量
        const refundCredits = refundAmount || originalTransaction.amount;

        // 检查账户余额是否足够扣除
        const account = await tx.creditAccount.findUnique({
          where: { id: accountId }
        });

        if (!account) {
          throw CreditErrors.accountNotFound(accountId);
        }

        if (account.currentBalance < refundCredits) {
          throw CreditErrors.insufficientBalance(
            accountId,
            refundCredits,
            account.currentBalance
          );
        }

        // 扣除积分
        const refundTransaction = await this.creditService.createTransaction({
          accountId,
          type: 'REFUNDED',
          amount: -refundCredits,
          reason: reason || `Refund for purchase ${purchaseId}`,
          description: `Refunded ${refundCredits} credits for purchase ${purchaseId}`,
          metadata: {
            originalTransactionId: originalTransaction.id,
            originalPurchaseId: purchaseId,
            refundType: 'package_refund'
          },
          referenceId: purchaseId
        });

        return refundTransaction;
      });
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('processRefund', error as Error);
    }
  }

  // ================================
  // 统计和分析
  // ================================

  /**
   * 获取积分包销售统计
   */
  async getPackageSalesStats(
    packageId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalSales: number;
    totalRevenue: number;
    totalCreditsIssued: number;
    averageOrderValue: number;
  }> {
    try {
      const where: any = {
        type: 'EARNED_PURCHASE'
      };

      if (packageId) {
        where.metadata = {
          path: ['packageId'],
          equals: packageId
        };
      }

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = startDate;
        if (endDate) where.createdAt.lte = endDate;
      }

      const transactions = await this.db.creditTransaction.findMany({
        where,
        select: {
          amount: true,
          metadata: true
        }
      });

      const totalSales = transactions.length;
      const totalCreditsIssued = transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const totalRevenue = transactions.reduce((sum, tx) => {
        const metadata = tx.metadata as any;
        return sum + (metadata?.price || 0);
      }, 0);
      const averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0;

      return {
        totalSales,
        totalRevenue,
        totalCreditsIssued,
        averageOrderValue
      };
    } catch (error) {
      throw CreditErrors.database('getPackageSalesStats', error as Error);
    }
  }

  /**
   * 获取最受欢迎的积分包
   */
  async getPopularPackages(limit: number = 5): Promise<Array<{
    package: CreditPackage;
    salesCount: number;
    totalRevenue: number;
  }>> {
    try {
      // 获取所有积分包
      const packages = await this.db.creditPackage.findMany({
        where: { status: 'ACTIVE' }
      });

      // 获取每个包的销售统计
      const packageStats = await Promise.all(
        packages.map(async (pkg) => {
          const stats = await this.getPackageSalesStats(pkg.id);
          return {
            package: pkg as CreditPackage,
            salesCount: stats.totalSales,
            totalRevenue: stats.totalRevenue
          };
        })
      );

      // 按销售数量排序
      return packageStats
        .sort((a, b) => b.salesCount - a.salesCount)
        .slice(0, limit);
    } catch (error) {
      throw CreditErrors.database('getPopularPackages', error as Error);
    }
  }
}
