import * as Sentry from '@sentry/nextjs';
import type { ErrorContext } from './types';

// Sentry 配置
export const initSentry = () => {
  if (!process.env.SENTRY_DSN) {
    console.warn('SENTRY_DSN not configured, skipping Sentry initialization');
    return;
  }

  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV || 'development',
    
    // 性能监控采样率
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 性能分析采样率
    profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    
    // 集成配置
    integrations: [
      // 自动捕获未处理的Promise拒绝
      new Sentry.Integrations.Http({ tracing: true }),
    ],
    
    // 过滤敏感信息
    beforeSend(event) {
      // 移除敏感数据
      if (event.request?.headers) {
        delete event.request.headers.authorization;
        delete event.request.headers.cookie;
      }
      return event;
    },
    
    // 忽略特定错误
    ignoreErrors: [
      'ResizeObserver loop limit exceeded',
      'Non-Error promise rejection captured',
      'ChunkLoadError',
    ],
  });
};

// 错误捕获工具函数
export const captureError = (error: Error, context?: ErrorContext) => {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('error_context', context);
      if (context.userId) scope.setUser({ id: context.userId });
      if (context.organizationId) scope.setTag('organization_id', context.organizationId);
      if (context.requestId) scope.setTag('request_id', context.requestId);
    }
    Sentry.captureException(error);
  });
};

// 性能监控
export const startTransaction = (name: string, op: string) => {
  return Sentry.startTransaction({ name, op });
};

// 添加面包屑
export const addBreadcrumb = (message: string, category: string, level: Sentry.SeverityLevel = 'info') => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    timestamp: Date.now() / 1000,
  });
};

// API 错误处理中间件
export const withSentryErrorHandling = <T extends (...args: any[]) => any>(
  handler: T,
  context?: Partial<ErrorContext>
): T => {
  return ((...args: Parameters<T>) => {
    try {
      const result = handler(...args);
      
      // 处理异步函数
      if (result instanceof Promise) {
        return result.catch((error) => {
          captureError(error, context);
          throw error;
        });
      }
      
      return result;
    } catch (error) {
      captureError(error as Error, context);
      throw error;
    }
  }) as T;
};
