// Next.js 15 Instrumentation API
// 这个文件会在服务器启动时自动执行

export async function register() {
  // 只在 Node.js 环境中运行
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // 动态导入避免在 Edge Runtime 中执行
    const { initSentry } = await import('@repo/monitoring/sentry');
    const { logger } = await import('@repo/monitoring/logger');
    
    // 初始化 Sentry
    initSentry();
    
    // 记录服务启动
    logger.info('MapMoment application instrumentation initialized');
    
    // 注册全局错误处理
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', error);
      process.exit(1);
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', new Error(String(reason)));
    });
  }
}
