import { describe, it, expect } from 'vitest';
import {
  CreditError,
  CreditAccountError,
  CreditBalanceError,
  CreditTransactionError,
  CreditAllocationError,
  CreditPackageError,
  CreditTransferError,
  CreditValidationError,
  CreditSystemError,
  CreditErrors,
  isCreditError,
  getCreditErrorType,
  formatCreditError,
  handleCreditError
} from '../../src/types/credit-errors';

describe('credit-errors', () => {
  describe('CreditError base class', () => {
    it('should create error with correct properties', () => {
      const error = new CreditError('Test error', 'TEST_ERROR', { detail: 'test' });

      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.details).toEqual({ detail: 'test' });
      expect(error.timestamp).toBeInstanceOf(Date);
      expect(error.name).toBe('CreditError');
      expect(error instanceof Error).toBe(true);
    });

    it('should create error without details', () => {
      const error = new CreditError('Test error', 'TEST_ERROR');

      expect(error.details).toBeUndefined();
    });
  });

  describe('Specific error classes', () => {
    it('should create CreditAccountError correctly', () => {
      const error = new CreditAccountError('Account not found', 'ACCOUNT_NOT_FOUND');

      expect(error.name).toBe('CreditAccountError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditAccountError).toBe(true);
    });

    it('should create CreditBalanceError correctly', () => {
      const error = new CreditBalanceError('Insufficient balance', 'INSUFFICIENT_BALANCE');

      expect(error.name).toBe('CreditBalanceError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditBalanceError).toBe(true);
    });

    it('should create CreditTransactionError correctly', () => {
      const error = new CreditTransactionError('Transaction failed', 'TRANSACTION_FAILED');

      expect(error.name).toBe('CreditTransactionError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditTransactionError).toBe(true);
    });

    it('should create CreditAllocationError correctly', () => {
      const error = new CreditAllocationError('Allocation failed', 'ALLOCATION_FAILED');

      expect(error.name).toBe('CreditAllocationError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditAllocationError).toBe(true);
    });

    it('should create CreditPackageError correctly', () => {
      const error = new CreditPackageError('Package not found', 'PACKAGE_NOT_FOUND');

      expect(error.name).toBe('CreditPackageError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditPackageError).toBe(true);
    });

    it('should create CreditTransferError correctly', () => {
      const error = new CreditTransferError('Transfer failed', 'TRANSFER_FAILED');

      expect(error.name).toBe('CreditTransferError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditTransferError).toBe(true);
    });

    it('should create CreditValidationError correctly', () => {
      const error = new CreditValidationError('Validation failed', 'VALIDATION_FAILED');

      expect(error.name).toBe('CreditValidationError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditValidationError).toBe(true);
    });

    it('should create CreditSystemError correctly', () => {
      const error = new CreditSystemError('System error', 'SYSTEM_ERROR');

      expect(error.name).toBe('CreditSystemError');
      expect(error instanceof CreditError).toBe(true);
      expect(error instanceof CreditSystemError).toBe(true);
    });
  });

  describe('CreditErrors factory', () => {
    it('should create account errors', () => {
      const notFound = CreditErrors.accountNotFound('user-123');
      expect(notFound).toBeInstanceOf(CreditAccountError);
      expect(notFound.code).toBe('ACCOUNT_NOT_FOUND');
      expect(notFound.details?.userId).toBe('user-123');

      const alreadyExists = CreditErrors.accountAlreadyExists('user-123');
      expect(alreadyExists).toBeInstanceOf(CreditAccountError);
      expect(alreadyExists.code).toBe('ACCOUNT_ALREADY_EXISTS');

      const suspended = CreditErrors.accountSuspended('account-123');
      expect(suspended).toBeInstanceOf(CreditAccountError);
      expect(suspended.code).toBe('ACCOUNT_SUSPENDED');
    });

    it('should create balance errors', () => {
      const insufficient = CreditErrors.insufficientBalance(100, 50);
      expect(insufficient).toBeInstanceOf(CreditBalanceError);
      expect(insufficient.code).toBe('INSUFFICIENT_BALANCE');
      expect(insufficient.details?.required).toBe(100);
      expect(insufficient.details?.available).toBe(50);

      const invalidAmount = CreditErrors.invalidAmount(-10);
      expect(invalidAmount).toBeInstanceOf(CreditBalanceError);
      expect(invalidAmount.code).toBe('INVALID_AMOUNT');
      expect(invalidAmount.details?.amount).toBe(-10);
    });

    it('should create transaction errors', () => {
      const failed = CreditErrors.transactionFailed('tx-123', 'Database error');
      expect(failed).toBeInstanceOf(CreditTransactionError);
      expect(failed.code).toBe('TRANSACTION_FAILED');
      expect(failed.details?.transactionId).toBe('tx-123');

      const notFound = CreditErrors.transactionNotFound('tx-123');
      expect(notFound).toBeInstanceOf(CreditTransactionError);
      expect(notFound.code).toBe('TRANSACTION_NOT_FOUND');
    });

    it('should create allocation errors', () => {
      const failed = CreditErrors.allocationFailed('allocation-123', 'Quota exceeded');
      expect(failed).toBeInstanceOf(CreditAllocationError);
      expect(failed.code).toBe('ALLOCATION_FAILED');

      const notFound = CreditErrors.allocationNotFound('allocation-123');
      expect(notFound).toBeInstanceOf(CreditAllocationError);
      expect(notFound.code).toBe('ALLOCATION_NOT_FOUND');

      const alreadyExists = CreditErrors.allocationAlreadyExists('account-123', 'basic');
      expect(alreadyExists).toBeInstanceOf(CreditAllocationError);
      expect(alreadyExists.code).toBe('ALLOCATION_ALREADY_EXISTS');
    });

    it('should create package errors', () => {
      const notFound = CreditErrors.packageNotFound('package-123');
      expect(notFound).toBeInstanceOf(CreditPackageError);
      expect(notFound.code).toBe('PACKAGE_NOT_FOUND');

      const inactive = CreditErrors.packageInactive('package-123');
      expect(inactive).toBeInstanceOf(CreditPackageError);
      expect(inactive.code).toBe('PACKAGE_INACTIVE');

      const purchaseFailed = CreditErrors.packagePurchaseFailed('package-123', 'Payment failed');
      expect(purchaseFailed).toBeInstanceOf(CreditPackageError);
      expect(purchaseFailed.code).toBe('PACKAGE_PURCHASE_FAILED');
    });

    it('should create transfer errors', () => {
      const sameAccount = CreditErrors.transferToSameAccount('account-123');
      expect(sameAccount).toBeInstanceOf(CreditTransferError);
      expect(sameAccount.code).toBe('TRANSFER_TO_SAME_ACCOUNT');

      const limitExceeded = CreditErrors.transferLimitExceeded(1000, 500);
      expect(limitExceeded).toBeInstanceOf(CreditTransferError);
      expect(limitExceeded.code).toBe('TRANSFER_LIMIT_EXCEEDED');

      const failed = CreditErrors.transferFailed('from-123', 'to-456', 'Network error');
      expect(failed).toBeInstanceOf(CreditTransferError);
      expect(failed.code).toBe('TRANSFER_FAILED');
    });

    it('should create validation errors', () => {
      const invalidInput = CreditErrors.invalidInput('amount', 'Must be positive');
      expect(invalidInput).toBeInstanceOf(CreditValidationError);
      expect(invalidInput.code).toBe('INVALID_INPUT');

      const missingField = CreditErrors.missingRequiredField('userId');
      expect(missingField).toBeInstanceOf(CreditValidationError);
      expect(missingField.code).toBe('MISSING_REQUIRED_FIELD');

      const invalidFormat = CreditErrors.invalidFormat('accountId', 'account-123', 'Must start with acc_');
      expect(invalidFormat).toBeInstanceOf(CreditValidationError);
      expect(invalidFormat.code).toBe('INVALID_FORMAT');
    });

    it('should create system errors', () => {
      const dbError = CreditErrors.databaseError('Connection timeout');
      expect(dbError).toBeInstanceOf(CreditSystemError);
      expect(dbError.code).toBe('DATABASE_ERROR');

      const configError = CreditErrors.configurationError('Missing API key');
      expect(configError).toBeInstanceOf(CreditSystemError);
      expect(configError.code).toBe('CONFIGURATION_ERROR');

      const internalError = CreditErrors.internalError('Unexpected error');
      expect(internalError).toBeInstanceOf(CreditSystemError);
      expect(internalError.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('Utility functions', () => {
    describe('isCreditError', () => {
      it('should identify credit errors correctly', () => {
        const creditError = new CreditError('Test', 'TEST');
        const accountError = new CreditAccountError('Test', 'TEST');
        const regularError = new Error('Regular error');

        expect(isCreditError(creditError)).toBe(true);
        expect(isCreditError(accountError)).toBe(true);
        expect(isCreditError(regularError)).toBe(false);
        expect(isCreditError(null)).toBe(false);
        expect(isCreditError(undefined)).toBe(false);
        expect(isCreditError('string')).toBe(false);
      });
    });

    describe('getCreditErrorType', () => {
      it('should return correct error types', () => {
        expect(getCreditErrorType(new CreditError('', ''))).toBe('CreditError');
        expect(getCreditErrorType(new CreditAccountError('', ''))).toBe('CreditAccountError');
        expect(getCreditErrorType(new CreditBalanceError('', ''))).toBe('CreditBalanceError');
        expect(getCreditErrorType(new CreditTransactionError('', ''))).toBe('CreditTransactionError');
        expect(getCreditErrorType(new CreditAllocationError('', ''))).toBe('CreditAllocationError');
        expect(getCreditErrorType(new CreditPackageError('', ''))).toBe('CreditPackageError');
        expect(getCreditErrorType(new CreditTransferError('', ''))).toBe('CreditTransferError');
        expect(getCreditErrorType(new CreditValidationError('', ''))).toBe('CreditValidationError');
        expect(getCreditErrorType(new CreditSystemError('', ''))).toBe('CreditSystemError');
        expect(getCreditErrorType(new Error(''))).toBe('Error');
      });
    });

    describe('formatCreditError', () => {
      it('should format credit errors with details', () => {
        const error = new CreditAccountError('Account not found', 'ACCOUNT_NOT_FOUND', {
          userId: 'user-123'
        });

        const formatted = formatCreditError(error);

        expect(formatted).toContain('CreditAccountError');
        expect(formatted).toContain('ACCOUNT_NOT_FOUND');
        expect(formatted).toContain('Account not found');
        expect(formatted).toContain('user-123');
      });

      it('should format credit errors without details', () => {
        const error = new CreditError('Simple error', 'SIMPLE_ERROR');

        const formatted = formatCreditError(error);

        expect(formatted).toContain('CreditError');
        expect(formatted).toContain('SIMPLE_ERROR');
        expect(formatted).toContain('Simple error');
        expect(formatted).not.toContain('Details:');
      });

      it('should format regular errors', () => {
        const error = new Error('Regular error');

        const formatted = formatCreditError(error);

        expect(formatted).toContain('Error');
        expect(formatted).toContain('Regular error');
      });
    });

    describe('handleCreditError', () => {
      it('should convert credit errors to response format', () => {
        const error = new CreditAccountError('Account not found', 'ACCOUNT_NOT_FOUND', {
          userId: 'user-123'
        });

        const response = handleCreditError(error);

        expect(response).toEqual({
          success: false,
          error: {
            type: 'CreditAccountError',
            code: 'ACCOUNT_NOT_FOUND',
            message: 'Account not found',
            details: { userId: 'user-123' },
            timestamp: error.timestamp
          }
        });
      });

      it('should convert regular errors to response format', () => {
        const error = new Error('Regular error');

        const response = handleCreditError(error);

        expect(response).toEqual({
          success: false,
          error: {
            type: 'Error',
            code: 'UNKNOWN_ERROR',
            message: 'Regular error',
            details: undefined,
            timestamp: expect.any(Date)
          }
        });
      });
    });
  });
});
