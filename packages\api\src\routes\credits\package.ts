import { CreditPackageService, CreditService } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import {
    createPackageSchema,
    creditPackageResponseSchema,
    errorResponseSchema,
    packageIdSchema,
    packageQuerySchema,
    paginatedResponseSchema,
    purchasePackageSchema,
    refundPackageSchema,
    updatePackageSchema,
} from "./schemas";

// 创建服务实例
const creditService = new CreditService(db);
const packageService = new CreditPackageService(db, creditService);

/**
 * 积分包管理路由
 */
export const packageRouter = new Hono()
  // 获取积分包列表
  .get(
    "/",
    validator("query", packageQuerySchema),
    describeRoute({
      tags: ["Credits"],
      summary: "获取积分包列表",
      description: "获取可用的积分包列表，支持筛选和排序",
      responses: {
        200: {
          description: "积分包列表",
          content: {
            "application/json": {
              schema: resolver(paginatedResponseSchema(creditPackageResponseSchema)),
            },
          },
        },
        400: {
          description: "请求参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const query = c.req.valid("query");

        // 构建查询参数
        const queryParams = {
          isActive: query.isActive,
          isFeatured: query.isFeatured,
          minCredits: query.minCredits,
          maxCredits: query.maxCredits,
          minPrice: query.minPrice,
          maxPrice: query.maxPrice,
          currency: query.currency,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        };

        const result = await packageService.getPackages(queryParams);

        const response = {
          data: result.data.map(pkg => ({
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            credits: pkg.credits,
            price: pkg.price,
            currency: pkg.currency,
            validityDays: pkg.validityDays,
            isActive: pkg.isActive,
            isFeatured: pkg.isFeatured,
            salesCount: pkg.salesCount || 0,
            totalRevenue: pkg.totalRevenue || 0,
            createdAt: pkg.createdAt.toISOString(),
            updatedAt: pkg.updatedAt.toISOString(),
          })),
          pagination: result.pagination,
        };

        logger.info("积分包列表查询成功", {
          resultCount: result.data.length,
          filters: queryParams,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分包列表失败", {
          error: error instanceof Error ? error.message : String(error),
        });

        throw new HTTPException(500, { message: "获取积分包列表失败" });
      }
    }
  )

  // 获取积分包详情
  .get(
    "/:id",
    validator("param", { id: packageIdSchema }),
    describeRoute({
      tags: ["Credits"],
      summary: "获取积分包详情",
      description: "获取指定积分包的详细信息",
      responses: {
        200: {
          description: "积分包详情",
          content: {
            "application/json": {
              schema: resolver(creditPackageResponseSchema),
            },
          },
        },
        404: {
          description: "积分包不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const { id } = c.req.valid("param");

        const pkg = await packageService.getPackage(id);

        if (!pkg) {
          throw new HTTPException(404, { message: "积分包不存在" });
        }

        const response = {
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          credits: pkg.credits,
          price: pkg.price,
          currency: pkg.currency,
          validityDays: pkg.validityDays,
          isActive: pkg.isActive,
          isFeatured: pkg.isFeatured,
          salesCount: pkg.salesCount || 0,
          totalRevenue: pkg.totalRevenue || 0,
          createdAt: pkg.createdAt.toISOString(),
          updatedAt: pkg.updatedAt.toISOString(),
        };

        logger.info("积分包详情查询成功", {
          packageId: id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分包详情失败", {
          error: error instanceof Error ? error.message : String(error),
          packageId: c.req.param("id"),
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "获取积分包详情失败" });
      }
    }
  )

  // 购买积分包
  .post(
    "/:id/purchase",
    authMiddleware,
    validator("param", { id: packageIdSchema }),
    validator("json", purchasePackageSchema.omit({ packageId: true })),
    describeRoute({
      tags: ["Credits"],
      summary: "购买积分包",
      description: "购买指定的积分包并添加积分到账户",
      responses: {
        200: {
          description: "购买成功",
          content: {
            "application/json": {
              schema: resolver(z.object({
                transaction: z.object({
                  id: z.string(),
                  amount: z.number(),
                  purchaseId: z.string(),
                  createdAt: z.string(),
                }),
                package: creditPackageResponseSchema,
              })),
            },
          },
        },
        404: {
          description: "积分包不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        400: {
          description: "积分包不可用或参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");
        const data = c.req.valid("json");

        // 确定目标用户
        const targetUserId = data.userId || user.id;
        const targetOrgId = data.organizationId;

        // 权限检查
        if (data.userId && data.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权为其他用户购买积分包" });
        }

        // 生成购买ID
        const purchaseId = `purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const transaction = await packageService.processPurchase({
          packageId: id,
          userId: targetUserId,
          organizationId: targetOrgId,
          purchaseId,
          paymentProvider: data.paymentProvider,
          paymentMethodId: data.paymentMethodId,
          metadata: data.metadata,
        });

        // 获取积分包信息
        const pkg = await packageService.getPackage(id);

        const response = {
          transaction: {
            id: transaction.id,
            amount: transaction.amount,
            purchaseId: transaction.purchaseId!,
            createdAt: transaction.createdAt.toISOString(),
          },
          package: {
            id: pkg!.id,
            name: pkg!.name,
            description: pkg!.description,
            credits: pkg!.credits,
            price: pkg!.price,
            currency: pkg!.currency,
            validityDays: pkg!.validityDays,
            isActive: pkg!.isActive,
            isFeatured: pkg!.isFeatured,
            salesCount: pkg!.salesCount || 0,
            totalRevenue: pkg!.totalRevenue || 0,
            createdAt: pkg!.createdAt.toISOString(),
            updatedAt: pkg!.updatedAt.toISOString(),
          },
        };

        logger.info("积分包购买成功", {
          packageId: id,
          purchaseId,
          transactionId: transaction.id,
          userId: targetUserId,
          organizationId: targetOrgId,
          amount: transaction.amount,
          paymentProvider: data.paymentProvider,
        });

        return c.json(response);
      } catch (error) {
        logger.error("购买积分包失败", {
          error: error instanceof Error ? error.message : String(error),
          packageId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是积分包不可用的错误
        if (error instanceof Error && error.message.includes("not available")) {
          throw new HTTPException(400, { message: "积分包当前不可用" });
        }

        throw new HTTPException(500, { message: "购买积分包失败" });
      }
    }
  )

  // 创建积分包（管理员）
  .post(
    "/",
    authMiddleware,
    validator("json", createPackageSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "创建积分包",
      description: "创建新的积分包（仅管理员）",
      responses: {
        201: {
          description: "积分包创建成功",
          content: {
            "application/json": {
              schema: resolver(creditPackageResponseSchema),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 只有管理员可以创建积分包
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以创建积分包" });
        }

        const pkg = await packageService.createPackage({
          name: data.name,
          description: data.description,
          credits: data.credits,
          price: data.price,
          currency: data.currency,
          validityDays: data.validityDays,
          isActive: data.isActive,
          isFeatured: data.isFeatured,
        });

        const response = {
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          credits: pkg.credits,
          price: pkg.price,
          currency: pkg.currency,
          validityDays: pkg.validityDays,
          isActive: pkg.isActive,
          isFeatured: pkg.isFeatured,
          salesCount: pkg.salesCount || 0,
          totalRevenue: pkg.totalRevenue || 0,
          createdAt: pkg.createdAt.toISOString(),
          updatedAt: pkg.updatedAt.toISOString(),
        };

        logger.info("积分包创建成功", {
          packageId: pkg.id,
          name: data.name,
          credits: data.credits,
          price: data.price,
          adminUserId: user.id,
        });

        return c.json(response, 201);
      } catch (error) {
        logger.error("创建积分包失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "创建积分包失败" });
      }
    }
  )

  // 更新积分包（管理员）
  .put(
    "/:id",
    authMiddleware,
    validator("param", { id: packageIdSchema }),
    validator("json", updatePackageSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "更新积分包",
      description: "更新指定的积分包（仅管理员）",
      responses: {
        200: {
          description: "积分包更新成功",
          content: {
            "application/json": {
              schema: resolver(creditPackageResponseSchema),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        404: {
          description: "积分包不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");
        const data = c.req.valid("json");

        // 只有管理员可以更新积分包
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以更新积分包" });
        }

        const updatedPackage = await packageService.updatePackage(id, {
          name: data.name,
          description: data.description,
          credits: data.credits,
          price: data.price,
          currency: data.currency,
          validityDays: data.validityDays,
          isActive: data.isActive,
          isFeatured: data.isFeatured,
        });

        const response = {
          id: updatedPackage.id,
          name: updatedPackage.name,
          description: updatedPackage.description,
          credits: updatedPackage.credits,
          price: updatedPackage.price,
          currency: updatedPackage.currency,
          validityDays: updatedPackage.validityDays,
          isActive: updatedPackage.isActive,
          isFeatured: updatedPackage.isFeatured,
          salesCount: updatedPackage.salesCount || 0,
          totalRevenue: updatedPackage.totalRevenue || 0,
          createdAt: updatedPackage.createdAt.toISOString(),
          updatedAt: updatedPackage.updatedAt.toISOString(),
        };

        logger.info("积分包更新成功", {
          packageId: id,
          changes: data,
          adminUserId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("更新积分包失败", {
          error: error instanceof Error ? error.message : String(error),
          packageId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是积分包不存在的错误
        if (error instanceof Error && error.message.includes("not found")) {
          throw new HTTPException(404, { message: "积分包不存在" });
        }

        throw new HTTPException(500, { message: "更新积分包失败" });
      }
    }
  )

  // 退款积分包购买（管理员）
  .post(
    "/:id/refund",
    authMiddleware,
    validator("param", { id: packageIdSchema }),
    validator("json", refundPackageSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "退款积分包购买",
      description: "退款指定的积分包购买并扣除相应积分（仅管理员）",
      responses: {
        200: {
          description: "退款成功",
          content: {
            "application/json": {
              schema: resolver(z.object({
                success: z.boolean(),
                refundTransaction: z.object({
                  id: z.string(),
                  amount: z.number(),
                  createdAt: z.string(),
                }).nullable(),
                message: z.string(),
              })),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        404: {
          description: "购买记录不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");
        const data = c.req.valid("json");

        // 只有管理员可以执行退款
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以执行退款" });
        }

        const result = await packageService.processRefund({
          packageId: id,
          purchaseId: data.purchaseId,
          reason: data.reason,
          refundAmount: data.refundAmount,
          metadata: data.metadata,
        });

        const response = {
          success: result.success,
          refundTransaction: result.refundTransaction ? {
            id: result.refundTransaction.id,
            amount: result.refundTransaction.amount,
            createdAt: result.refundTransaction.createdAt.toISOString(),
          } : null,
          message: result.success ? "退款处理成功" : "退款处理失败",
        };

        logger.info("积分包退款处理", {
          packageId: id,
          purchaseId: data.purchaseId,
          success: result.success,
          refundAmount: data.refundAmount,
          reason: data.reason,
          adminUserId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("处理积分包退款失败", {
          error: error instanceof Error ? error.message : String(error),
          packageId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是购买记录不存在的错误
        if (error instanceof Error && error.message.includes("not found")) {
          throw new HTTPException(404, { message: "购买记录不存在" });
        }

        throw new HTTPException(500, { message: "处理退款失败" });
      }
    }
  );
