# 积分系统 API 层实现

## 概述

本文档描述了积分系统的 API 层实现，基于 Hono 框架构建，提供完整的 RESTful API 接口用于积分管理。

## 架构设计

### 路由结构

```
/api/credits/
├── account/          # 积分账户管理
├── transactions/     # 积分交易管理  
├── allocations/      # 积分分配管理
├── packages/         # 积分包管理
└── admin/           # 管理员功能
```

### 文件组织

```
packages/api/src/routes/credits/
├── router.ts         # 主路由文件
├── schemas.ts        # Zod 验证模式
├── account.ts        # 账户管理路由
├── transaction.ts    # 交易管理路由
├── allocation.ts     # 分配管理路由
├── package.ts        # 积分包管理路由
├── admin.ts          # 管理员路由
├── __tests__/        # API 测试
│   └── api.test.ts
└── README.md         # 本文档
```

## API 端点

### 积分账户管理 (`/account`)

#### `GET /account`
- **功能**: 获取积分账户信息
- **权限**: 需要认证，用户只能查看自己的账户
- **参数**: 
  - `userId` (可选): 用户ID
  - `organizationId` (可选): 组织ID
  - `includeTransactions` (可选): 是否包含交易历史
- **响应**: 账户详情和余额信息

#### `POST /account`
- **功能**: 创建积分账户
- **权限**: 需要认证，管理员可为其他用户创建
- **参数**: 
  - `userId` (可选): 用户ID
  - `organizationId` (可选): 组织ID
  - `initialBalance` (可选): 初始余额
- **响应**: 创建的账户信息

#### `PUT /account/status`
- **功能**: 更新账户状态
- **权限**: 仅管理员
- **参数**: 
  - `status`: 账户状态 (ACTIVE/SUSPENDED/FROZEN)
  - `reason` (可选): 更新原因
- **响应**: 更新后的账户信息

### 积分交易管理 (`/transactions`)

#### `GET /transactions`
- **功能**: 获取交易历史
- **权限**: 需要认证，用户只能查看自己的交易
- **参数**: 
  - `type` (可选): 交易类型筛选
  - `startDate` (可选): 开始日期
  - `endDate` (可选): 结束日期
  - `page`, `limit`: 分页参数
- **响应**: 分页的交易历史列表

#### `GET /transactions/:id`
- **功能**: 获取特定交易详情
- **权限**: 需要认证，用户只能查看自己的交易
- **响应**: 交易详细信息

#### `POST /transactions/consume`
- **功能**: 消耗积分
- **权限**: 需要认证
- **参数**: 
  - `amount`: 消耗数量
  - `reason`: 消耗原因
  - `featureId`: 功能ID
  - `metadata` (可选): 元数据
- **响应**: 消耗交易记录

#### `POST /transactions/add`
- **功能**: 添加积分
- **权限**: 仅管理员
- **参数**: 
  - `amount`: 添加数量
  - `reason`: 添加原因
  - `type`: 交易类型
  - `expiresAt` (可选): 过期时间
- **响应**: 添加交易记录

#### `POST /transactions/transfer`
- **功能**: 积分转账
- **权限**: 需要认证，用户只能从自己账户转出
- **参数**: 
  - `fromAccountId`: 发送方账户ID
  - `toAccountId`: 接收方账户ID
  - `amount`: 转账数量
  - `reason`: 转账原因
- **响应**: 转账交易记录对

### 积分分配管理 (`/allocations`)

#### `GET /allocations`
- **功能**: 获取分配规则列表
- **权限**: 需要认证，用户只能查看自己的规则
- **参数**: 
  - `status` (可选): 状态筛选
  - `subscriptionPlan` (可选): 订阅计划筛选
- **响应**: 分页的分配规则列表

#### `POST /allocations`
- **功能**: 创建分配规则
- **权限**: 需要认证
- **参数**: 
  - `subscriptionPlan`: 订阅计划
  - `customAmount` (可选): 自定义数量
  - `customInterval` (可选): 自定义间隔
- **响应**: 创建的分配规则

#### `PUT /allocations/:id`
- **功能**: 更新分配规则
- **权限**: 需要认证，用户只能更新自己的规则
- **参数**: 
  - `status` (可选): 状态
  - `customAmount` (可选): 自定义数量
- **响应**: 更新后的分配规则

#### `POST /allocations/:id/execute`
- **功能**: 手动执行分配
- **权限**: 需要认证，用户只能执行自己的规则
- **响应**: 执行结果和交易记录

#### `POST /allocations/execute-batch`
- **功能**: 批量执行到期分配
- **权限**: 仅管理员
- **响应**: 批量执行统计结果

### 积分包管理 (`/packages`)

#### `GET /packages`
- **功能**: 获取积分包列表
- **权限**: 公开访问
- **参数**: 
  - `isActive` (可选): 是否激活
  - `isFeatured` (可选): 是否推荐
  - `minCredits`, `maxCredits` (可选): 积分范围
  - `minPrice`, `maxPrice` (可选): 价格范围
- **响应**: 分页的积分包列表

#### `GET /packages/:id`
- **功能**: 获取积分包详情
- **权限**: 公开访问
- **响应**: 积分包详细信息

#### `POST /packages/:id/purchase`
- **功能**: 购买积分包
- **权限**: 需要认证
- **参数**: 
  - `paymentProvider`: 支付提供商
  - `paymentMethodId`: 支付方式ID
  - `metadata` (可选): 元数据
- **响应**: 购买交易记录和积分包信息

#### `POST /packages` (管理员)
- **功能**: 创建积分包
- **权限**: 仅管理员
- **参数**: 积分包完整信息
- **响应**: 创建的积分包

#### `PUT /packages/:id` (管理员)
- **功能**: 更新积分包
- **权限**: 仅管理员
- **参数**: 更新的积分包信息
- **响应**: 更新后的积分包

#### `POST /packages/:id/refund` (管理员)
- **功能**: 退款积分包购买
- **权限**: 仅管理员
- **参数**: 
  - `purchaseId`: 购买ID
  - `reason`: 退款原因
  - `refundAmount` (可选): 退款金额
- **响应**: 退款处理结果

### 管理员功能 (`/admin`)

#### `GET /admin/accounts`
- **功能**: 获取所有积分账户
- **权限**: 仅管理员
- **参数**: 
  - `status` (可选): 状态筛选
  - `minBalance`, `maxBalance` (可选): 余额范围
  - `hasOrganization` (可选): 是否有组织
- **响应**: 分页的账户列表

#### `GET /admin/analytics`
- **功能**: 获取积分系统分析数据
- **权限**: 仅管理员
- **参数**: 
  - `startDate` (可选): 开始日期
  - `endDate` (可选): 结束日期
- **响应**: 完整的系统统计数据

#### `POST /admin/maintenance/cleanup-expired`
- **功能**: 清理过期积分
- **权限**: 仅管理员
- **响应**: 清理统计结果

#### `POST /admin/maintenance/recalculate-balances`
- **功能**: 重新计算账户余额
- **权限**: 仅管理员
- **响应**: 重新计算统计结果

## 中间件

### 认证中间件 (`authMiddleware`)
- 验证用户身份
- 设置用户上下文信息

### 管理员中间件 (`adminMiddleware`)
- 验证管理员权限
- 限制管理员专用功能访问

### 积分中间件 (`creditMiddleware`)
- 验证功能访问所需积分
- 可选择自动消耗积分
- 支持用户和组织级别的积分检查

## 验证模式

所有 API 端点都使用 Zod 进行输入验证，包括：

- 请求参数验证
- 请求体验证
- 查询参数验证
- 路径参数验证

## 错误处理

- 使用 HTTPException 进行标准化错误响应
- 详细的错误日志记录
- 用户友好的错误消息
- 适当的 HTTP 状态码

## OpenAPI 文档

所有端点都包含完整的 OpenAPI 文档，支持：

- 自动生成 API 文档
- 交互式 API 测试界面
- 类型安全的客户端生成

## 测试

包含完整的 API 测试套件：

- 单元测试覆盖所有路由
- Mock 服务依赖
- 权限验证测试
- 错误场景测试

## 使用示例

### 获取账户余额
```typescript
const response = await fetch('/api/credits/account', {
  headers: { Authorization: 'Bearer <token>' }
});
const account = await response.json();
```

### 消耗积分
```typescript
const response = await fetch('/api/credits/transactions/consume', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    Authorization: 'Bearer <token>' 
  },
  body: JSON.stringify({
    amount: 10,
    reason: 'AI 聊天使用',
    featureId: 'ai_chat'
  })
});
```

### 购买积分包
```typescript
const response = await fetch('/api/credits/packages/pkg-123/purchase', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    Authorization: 'Bearer <token>' 
  },
  body: JSON.stringify({
    paymentProvider: 'stripe',
    paymentMethodId: 'pm_123'
  })
});
```

## 下一步

API 层实现完成后，下一步将进行：

1. **Phase 4**: 配置层集成 - 与现有支付系统集成
2. **Phase 5**: 前端组件层 - React hooks 和 UI 组件
3. **Phase 6**: 演示页面 - 类似存储测试页面的积分系统演示
