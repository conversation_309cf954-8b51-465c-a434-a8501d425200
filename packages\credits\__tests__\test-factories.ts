import type {
  CreditAccount,
  CreditTransaction,
  CreditAllocation,
  CreditPackage,
  CreditTransactionType,
  CreditPeriod,
  CreditExpirationPolicy
} from '../src/types/credit-types';

// ================================
// 测试数据工厂函数
// ================================

export const TestFactories = {
  // 创建测试用积分账户
  createCreditAccount(overrides: Partial<CreditAccount> = {}): CreditAccount {
    return {
      id: 'account-123',
      userId: 'user-123',
      organizationId: null,
      currentBalance: 1000,
      totalEarned: 1500,
      totalSpent: 500,
      status: 'ACTIVE',
      lastActivityAt: new Date('2024-01-15T10:00:00Z'),
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-15T10:00:00Z'),
      ...overrides
    };
  },

  // 创建测试用积分交易
  createCreditTransaction(overrides: Partial<CreditTransaction> = {}): CreditTransaction {
    return {
      id: 'tx-123',
      accountId: 'account-123',
      type: 'EARNED_PURCHASE' as CreditTransactionType,
      amount: 100,
      balanceBefore: 900,
      balanceAfter: 1000,
      reason: 'Test transaction',
      description: 'Test credit transaction',
      metadata: {},
      featureId: null,
      purchaseId: null,
      referenceId: null,
      expiresAt: null,
      expiredAt: null,
      userId: 'user-123',
      organizationId: null,
      createdAt: new Date('2024-01-15T10:00:00Z'),
      updatedAt: new Date('2024-01-15T10:00:00Z'),
      ...overrides
    };
  },

  // 创建测试用积分分配
  createCreditAllocation(overrides: Partial<CreditAllocation> = {}): CreditAllocation {
    return {
      id: 'allocation-123',
      accountId: 'account-123',
      planId: 'basic',
      creditsPerPeriod: 200,
      period: 'MONTHLY' as CreditPeriod,
      status: 'ACTIVE',
      lastAllocationAt: new Date('2024-01-01T00:00:00Z'),
      nextAllocationAt: new Date('2024-02-01T00:00:00Z'),
      expirationPolicy: 'ROLLING_EXPIRATION' as CreditExpirationPolicy,
      expirationDays: 90,
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      ...overrides
    };
  },

  // 创建测试用积分包
  createCreditPackage(overrides: Partial<CreditPackage> = {}): CreditPackage {
    return {
      id: 'package-123',
      name: 'Basic Credit Package',
      description: 'A basic credit package for testing',
      creditAmount: 500,
      price: 9.99,
      currency: 'USD',
      productIds: {
        stripe: 'price_123',
        lemonsqueezy: 'variant_456'
      },
      validityDays: 365,
      status: 'ACTIVE',
      featured: false,
      sortOrder: 0,
      createdAt: new Date('2024-01-01T00:00:00Z'),
      updatedAt: new Date('2024-01-01T00:00:00Z'),
      ...overrides
    };
  },

  // 创建多个测试交易记录
  createTransactionList(count: number, baseOverrides: Partial<CreditTransaction> = {}): CreditTransaction[] {
    return Array.from({ length: count }, (_, index) => 
      this.createCreditTransaction({
        id: `tx-${index + 1}`,
        amount: (index % 2 === 0) ? 100 : -50, // 交替正负金额
        createdAt: new Date(`2024-01-${String(index + 1).padStart(2, '0')}T10:00:00Z`),
        ...baseOverrides
      })
    );
  },

  // 创建过期的积分交易
  createExpiredTransaction(overrides: Partial<CreditTransaction> = {}): CreditTransaction {
    return this.createCreditTransaction({
      id: 'tx-expired',
      expiresAt: new Date('2024-01-01T00:00:00Z'), // 已过期
      expiredAt: new Date('2024-01-01T00:00:00Z'),
      ...overrides
    });
  },

  // 创建即将过期的积分交易
  createExpiringSoonTransaction(overrides: Partial<CreditTransaction> = {}): CreditTransaction {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return this.createCreditTransaction({
      id: 'tx-expiring-soon',
      expiresAt: tomorrow,
      ...overrides
    });
  },

  // 创建组织账户
  createOrganizationAccount(overrides: Partial<CreditAccount> = {}): CreditAccount {
    return this.createCreditAccount({
      id: 'org-account-123',
      userId: null,
      organizationId: 'org-123',
      currentBalance: 5000,
      totalEarned: 10000,
      totalSpent: 5000,
      ...overrides
    });
  },

  // 创建不同类型的交易
  createTransactionsByType(): Record<CreditTransactionType, CreditTransaction> {
    const types: CreditTransactionType[] = [
      'EARNED_SUBSCRIPTION',
      'EARNED_PURCHASE', 
      'EARNED_BONUS',
      'EARNED_REFERRAL',
      'EARNED_SIGNIN',
      'SPENT_FEATURE',
      'SPENT_TRANSFER',
      'RECEIVED_TRANSFER',
      'EXPIRED',
      'REFUNDED',
      'ADJUSTMENT'
    ];

    const transactions: Record<CreditTransactionType, CreditTransaction> = {} as any;
    
    types.forEach((type, index) => {
      transactions[type] = this.createCreditTransaction({
        id: `tx-${type.toLowerCase()}`,
        type,
        amount: type.startsWith('SPENT_') || type === 'EXPIRED' ? -100 : 100
      });
    });

    return transactions;
  },

  // 创建不同状态的积分包
  createPackagesByStatus() {
    return {
      active: this.createCreditPackage({
        id: 'package-active',
        status: 'ACTIVE'
      }),
      inactive: this.createCreditPackage({
        id: 'package-inactive', 
        status: 'INACTIVE'
      }),
      archived: this.createCreditPackage({
        id: 'package-archived',
        status: 'ARCHIVED'
      })
    };
  },

  // 创建推荐积分包
  createFeaturedPackage(overrides: Partial<CreditPackage> = {}): CreditPackage {
    return this.createCreditPackage({
      id: 'package-featured',
      name: 'Featured Package',
      featured: true,
      sortOrder: 1,
      ...overrides
    });
  },

  // 创建分页响应数据
  createPaginatedResponse<T>(items: T[], total: number, page: number = 1, limit: number = 20) {
    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  },

  // 创建错误场景的 Prisma 错误
  createPrismaError(code: string, message: string = 'Database error') {
    const error = new Error(message) as any;
    error.code = code;
    return error;
  },

  // 创建测试用的元数据
  createMetadata(overrides: Record<string, any> = {}) {
    return {
      source: 'test',
      version: '1.0',
      timestamp: new Date().toISOString(),
      ...overrides
    };
  }
};

// ================================
// 测试工具函数
// ================================

export const TestUtils = {
  // 创建未来日期
  createFutureDate(daysFromNow: number = 30): Date {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date;
  },

  // 创建过去日期
  createPastDate(daysAgo: number = 30): Date {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date;
  },

  // 等待指定毫秒
  async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 创建随机字符串
  randomString(length: number = 10): string {
    return Math.random().toString(36).substring(2, 2 + length);
  },

  // 创建随机数字
  randomNumber(min: number = 1, max: number = 1000): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
};
