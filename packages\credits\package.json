{"name": "@repo/credits", "version": "0.0.0", "main": "./index.ts", "types": "./**/*.tsx", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:run": "vitest run", "test:services": "vitest run __tests__/services", "test:utils": "vitest run __tests__/utils", "test:integration": "vitest run __tests__/integration"}, "dependencies": {"@repo/database": "workspace:*", "@repo/config": "workspace:*", "zod": "^3.24.2"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "@types/node": "22.14.0", "vitest": "^2.1.8", "@vitest/coverage-v8": "^2.1.8"}}