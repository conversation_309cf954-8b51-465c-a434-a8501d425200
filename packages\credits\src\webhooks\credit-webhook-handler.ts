import { type PrismaClient } from "@repo/database";
import { logger } from "@repo/logs";
import { PaymentIntegrationService } from "../services/PaymentIntegrationService";
import { findCreditPackageByProductId, type PaymentProvider } from "../config/payment-integration";

/**
 * 积分系统 Webhook 处理器
 * 扩展现有的支付系统 webhook 处理，添加积分相关的逻辑
 */
export class CreditWebhookHandler {
  private paymentIntegrationService: PaymentIntegrationService;

  constructor(private db: PrismaClient) {
    this.paymentIntegrationService = new PaymentIntegrationService(db);
  }

  /**
   * 处理支付完成事件
   * 检查是否为积分包购买，如果是则处理积分分配
   */
  async handlePaymentCompleted(params: {
    provider: PaymentProvider;
    productId: string;
    customerId: string;
    purchaseId: string;
    userId?: string;
    organizationId?: string;
    metadata?: Record<string, any>;
  }): Promise<boolean> {
    const { provider, productId, customerId, purchaseId, userId, organizationId, metadata } = params;

    try {
      // 检查是否为积分包产品
      const packageConfig = findCreditPackageByProductId(productId, provider);
      if (!packageConfig) {
        // 不是积分包产品，返回 false 表示未处理
        return false;
      }

      logger.info("检测到积分包购买", {
        packageId: packageConfig.packageId,
        productId,
        provider,
        purchaseId,
        userId,
        organizationId,
      });

      // 处理积分包购买
      await this.paymentIntegrationService.handleCreditPackagePurchase({
        productId,
        provider,
        customerId,
        purchaseId,
        userId,
        organizationId,
        metadata,
      });

      return true;
    } catch (error) {
      logger.error("处理积分包购买失败", {
        error: error instanceof Error ? error.message : String(error),
        provider,
        productId,
        purchaseId,
        userId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * 处理订阅创建事件
   */
  async handleSubscriptionCreated(params: {
    subscriptionId: string;
    planId: string;
    customerId: string;
    userId?: string;
    organizationId?: string;
    metadata?: Record<string, any>;
  }): Promise<boolean> {
    const { subscriptionId, planId, customerId, userId, organizationId, metadata } = params;

    try {
      logger.info("处理订阅创建事件", {
        subscriptionId,
        planId,
        userId,
        organizationId,
      });

      // 处理订阅积分分配
      const result = await this.paymentIntegrationService.handleSubscriptionCreated({
        subscriptionId,
        planId,
        customerId,
        userId,
        organizationId,
        metadata,
      });

      // 如果创建了分配规则或立即分配，返回 true
      return !!(result.allocation || result.immediateTransaction);
    } catch (error) {
      logger.error("处理订阅创建失败", {
        error: error instanceof Error ? error.message : String(error),
        subscriptionId,
        planId,
        userId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * 处理订阅取消事件
   */
  async handleSubscriptionCancelled(params: {
    subscriptionId: string;
    userId?: string;
    organizationId?: string;
    metadata?: Record<string, any>;
  }): Promise<boolean> {
    const { subscriptionId, userId, organizationId, metadata } = params;

    try {
      logger.info("处理订阅取消事件", {
        subscriptionId,
        userId,
        organizationId,
      });

      // 处理订阅取消
      const result = await this.paymentIntegrationService.handleSubscriptionCancelled({
        subscriptionId,
        userId,
        organizationId,
        metadata,
      });

      // 如果停用了分配规则，返回 true
      return result.deactivatedAllocations.length > 0;
    } catch (error) {
      logger.error("处理订阅取消失败", {
        error: error instanceof Error ? error.message : String(error),
        subscriptionId,
        userId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * 处理订阅更新事件（计划变更）
   */
  async handleSubscriptionUpdated(params: {
    subscriptionId: string;
    oldPlanId: string;
    newPlanId: string;
    userId?: string;
    organizationId?: string;
    metadata?: Record<string, any>;
  }): Promise<boolean> {
    const { subscriptionId, oldPlanId, newPlanId, userId, organizationId, metadata } = params;

    try {
      logger.info("处理订阅更新事件", {
        subscriptionId,
        oldPlanId,
        newPlanId,
        userId,
        organizationId,
      });

      // 处理订阅计划变更
      const result = await this.paymentIntegrationService.handleSubscriptionUpdated({
        subscriptionId,
        oldPlanId,
        newPlanId,
        userId,
        organizationId,
        metadata,
      });

      // 如果有任何变更，返回 true
      return !!(
        result.deactivatedAllocations.length > 0 ||
        result.newAllocation ||
        result.adjustmentTransaction
      );
    } catch (error) {
      logger.error("处理订阅更新失败", {
        error: error instanceof Error ? error.message : String(error),
        subscriptionId,
        oldPlanId,
        newPlanId,
        userId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * 处理退款事件
   */
  async handleRefund(params: {
    purchaseId: string;
    refundAmount?: number;
    reason: string;
    userId?: string;
    organizationId?: string;
    metadata?: Record<string, any>;
  }): Promise<boolean> {
    const { purchaseId, refundAmount, reason, userId, organizationId, metadata } = params;

    try {
      logger.info("处理退款事件", {
        purchaseId,
        refundAmount,
        reason,
        userId,
        organizationId,
      });

      // 处理退款
      const result = await this.paymentIntegrationService.handleRefund({
        purchaseId,
        refundAmount,
        reason,
        userId,
        organizationId,
        metadata,
      });

      // 如果处理了积分退款，返回 true
      return !!result.refundTransaction;
    } catch (error) {
      logger.error("处理退款失败", {
        error: error instanceof Error ? error.message : String(error),
        purchaseId,
        refundAmount,
        userId,
        organizationId,
      });
      throw error;
    }
  }

  /**
   * 从 Stripe 元数据中提取用户/组织信息
   */
  private extractEntityFromStripeMetadata(metadata: any): {
    userId?: string;
    organizationId?: string;
  } {
    return {
      userId: metadata?.user_id || undefined,
      organizationId: metadata?.organization_id || undefined,
    };
  }

  /**
   * 从 LemonSqueezy 自定义数据中提取用户/组织信息
   */
  private extractEntityFromLemonSqueezyCustomData(customData: any): {
    userId?: string;
    organizationId?: string;
  } {
    return {
      userId: customData?.user_id || undefined,
      organizationId: customData?.organization_id || undefined,
    };
  }

  /**
   * 从 Chargebee 自定义字段中提取用户/组织信息
   */
  private extractEntityFromChargebeeCustomFields(subscription: any): {
    userId?: string;
    organizationId?: string;
  } {
    return {
      userId: subscription?.cf_user_id || undefined,
      organizationId: subscription?.cf_organization_id || undefined,
    };
  }

  /**
   * 从 Polar 元数据中提取用户/组织信息
   */
  private extractEntityFromPolarMetadata(metadata: any): {
    userId?: string;
    organizationId?: string;
  } {
    return {
      userId: metadata?.user_id || undefined,
      organizationId: metadata?.organization_id || undefined,
    };
  }

  /**
   * 从 Creem 元数据中提取用户/组织信息
   */
  private extractEntityFromCreemMetadata(metadata: any): {
    userId?: string;
    organizationId?: string;
  } {
    return {
      userId: metadata?.user_id || undefined,
      organizationId: metadata?.organization_id || undefined,
    };
  }

  /**
   * 根据支付提供商提取实体信息的统一方法
   */
  extractEntityInfo(provider: PaymentProvider, data: any): {
    userId?: string;
    organizationId?: string;
  } {
    switch (provider) {
      case "stripe":
        return this.extractEntityFromStripeMetadata(data.metadata);
      case "lemonsqueezy":
        return this.extractEntityFromLemonSqueezyCustomData(data.custom_data);
      case "chargebee":
        return this.extractEntityFromChargebeeCustomFields(data.subscription);
      case "polar":
        return this.extractEntityFromPolarMetadata(data.metadata);
      case "creem":
        return this.extractEntityFromCreemMetadata(data.metadata);
      default:
        logger.warn("未知的支付提供商", { provider });
        return {};
    }
  }
}

/**
 * 创建积分系统 webhook 处理器实例
 */
export function createCreditWebhookHandler(db: PrismaClient): CreditWebhookHandler {
  return new CreditWebhookHandler(db);
}
