import { createCreditWebhookHandler } from "@repo/credits";
import { db } from "@repo/database";
import { webhookHandler as paymentsWebhookHandler } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";

// 创建积分系统 webhook 处理器
const creditWebhookHandler = createCreditWebhookHandler(db);

export const webhooksRouter = new Hono()
	.post(
		"/payments",
		describeRoute({
			tags: ["Webhooks"],
			summary: "Handle payments webhook",
		}),
		async (c) => {
			// 先调用原有的支付 webhook 处理器
			const response = await paymentsWebhookHandler(c.req.raw);

			// 如果支付处理成功，尝试处理积分相关逻辑
			if (response.status === 204) {
				try {
					// 这里可以添加积分系统的后处理逻辑
					// 注意：由于原始请求体已被消费，这里需要从数据库查询最新的购买记录
					// 具体的积分处理逻辑将在各个支付提供商的 webhook 处理器中集成
				} catch (error) {
					// 积分处理失败不应影响支付处理的成功响应
					console.error("积分系统后处理失败:", error);
				}
			}

			return response;
		},
	)
	.post(
		"/credits",
		describeRoute({
			tags: ["Webhooks"],
			summary: "Handle credits webhook",
			description: "Handle credit system specific webhooks",
		}),
		async (c) => {
			// 积分系统专用的 webhook 端点
			// 可用于处理积分包购买、分配等专门的事件
			return new Response("Credits webhook endpoint", { status: 200 });
		},
	);
