import type { PrismaClient } from '@repo/database';
import type {
  CreditAllocation,
  CreateCreditAllocationInput,
  CreditPeriod,
  CreditExpirationPolicy
} from '../types/credit-types';
import {
  CreditErrors,
  type CreditError
} from '../types/credit-errors';
import {
  calculateNextAllocationDate,
  calculateExpirationDate,
  getSubscriptionCreditAllocation
} from '../config/credit-config';
import { CreditService } from './CreditService';

/**
 * 积分分配服务
 * 负责管理订阅计划的积分分配规则和定时分配任务
 */
export class CreditAllocationService {
  private creditService: CreditService;

  constructor(private readonly db: PrismaClient) {
    this.creditService = new CreditService(db);
  }

  // ================================
  // 分配规则管理
  // ================================

  /**
   * 创建积分分配规则
   */
  async createAllocation(input: CreateCreditAllocationInput): Promise<CreditAllocation> {
    try {
      // 验证账户是否存在
      const account = await this.db.creditAccount.findUnique({
        where: { id: input.accountId }
      });

      if (!account) {
        throw CreditErrors.accountNotFound(input.accountId);
      }

      // 检查是否已存在相同计划的分配规则
      const existingAllocation = await this.db.creditAllocation.findFirst({
        where: {
          accountId: input.accountId,
          planId: input.planId,
          status: 'ACTIVE'
        }
      });

      if (existingAllocation) {
        throw new CreditError(
          'ALLOCATION_ALREADY_EXISTS',
          `Active allocation already exists for plan ${input.planId}`,
          { accountId: input.accountId, planId: input.planId }
        );
      }

      // 计算下次分配时间
      const nextAllocationAt = calculateNextAllocationDate(input.period);

      // 创建分配规则
      const allocation = await this.db.creditAllocation.create({
        data: {
          accountId: input.accountId,
          planId: input.planId,
          creditsPerPeriod: input.creditsPerPeriod,
          period: input.period,
          status: 'ACTIVE',
          nextAllocationAt,
          expirationPolicy: input.expirationPolicy,
          expirationDays: input.expirationDays
        }
      });

      return allocation as CreditAllocation;
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('createAllocation', error as Error);
    }
  }

  /**
   * 基于订阅计划创建分配规则
   */
  async createAllocationFromSubscription(
    accountId: string,
    planId: string
  ): Promise<CreditAllocation> {
    const planConfig = getSubscriptionCreditAllocation(planId);
    
    if (!planConfig) {
      throw CreditErrors.validation('planId', planId, 'Unknown subscription plan');
    }

    return this.createAllocation({
      accountId,
      planId: planConfig.planId,
      creditsPerPeriod: planConfig.creditsPerPeriod,
      period: planConfig.period,
      expirationPolicy: planConfig.expirationPolicy,
      expirationDays: planConfig.expirationDays
    });
  }

  /**
   * 获取账户的分配规则
   */
  async getAllocations(accountId: string): Promise<CreditAllocation[]> {
    try {
      const allocations = await this.db.creditAllocation.findMany({
        where: { accountId },
        orderBy: { createdAt: 'desc' }
      });

      return allocations as CreditAllocation[];
    } catch (error) {
      throw CreditErrors.database('getAllocations', error as Error);
    }
  }

  /**
   * 获取活跃的分配规则
   */
  async getActiveAllocations(accountId?: string): Promise<CreditAllocation[]> {
    try {
      const where: any = { status: 'ACTIVE' };
      if (accountId) {
        where.accountId = accountId;
      }

      const allocations = await this.db.creditAllocation.findMany({
        where,
        orderBy: { nextAllocationAt: 'asc' }
      });

      return allocations as CreditAllocation[];
    } catch (error) {
      throw CreditErrors.database('getActiveAllocations', error as Error);
    }
  }

  /**
   * 更新分配规则状态
   */
  async updateAllocationStatus(
    allocationId: string,
    status: 'ACTIVE' | 'PAUSED' | 'CANCELLED'
  ): Promise<CreditAllocation> {
    try {
      const allocation = await this.db.creditAllocation.update({
        where: { id: allocationId },
        data: { 
          status,
          updatedAt: new Date()
        }
      });

      return allocation as CreditAllocation;
    } catch (error) {
      if (error.code === 'P2025') {
        throw CreditErrors.allocationNotFound(allocationId);
      }
      throw CreditErrors.database('updateAllocationStatus', error as Error);
    }
  }

  /**
   * 删除分配规则
   */
  async deleteAllocation(allocationId: string): Promise<void> {
    try {
      await this.db.creditAllocation.delete({
        where: { id: allocationId }
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw CreditErrors.allocationNotFound(allocationId);
      }
      throw CreditErrors.database('deleteAllocation', error as Error);
    }
  }

  // ================================
  // 积分分配执行
  // ================================

  /**
   * 执行单个分配规则
   */
  async executeAllocation(allocationId: string): Promise<{
    allocation: CreditAllocation;
    transaction: any;
  }> {
    try {
      return await this.db.$transaction(async (tx) => {
        // 获取分配规则
        const allocation = await tx.creditAllocation.findUnique({
          where: { id: allocationId }
        });

        if (!allocation) {
          throw CreditErrors.allocationNotFound(allocationId);
        }

        if (allocation.status !== 'ACTIVE') {
          throw new CreditError(
            'ALLOCATION_SUSPENDED',
            `Allocation is not active: ${allocationId}`,
            { allocationId, status: allocation.status }
          );
        }

        // 检查是否到了分配时间
        const now = new Date();
        if (allocation.nextAllocationAt > now) {
          throw new CreditError(
            'ALLOCATION_NOT_DUE',
            `Allocation is not due yet: ${allocationId}`,
            { 
              allocationId, 
              nextAllocationAt: allocation.nextAllocationAt,
              currentTime: now 
            }
          );
        }

        // 计算过期时间
        const expiresAt = calculateExpirationDate(
          allocation.expirationPolicy as CreditExpirationPolicy,
          allocation.expirationDays || undefined,
          now
        );

        // 添加积分
        const transaction = await this.creditService.addCredits({
          accountId: allocation.accountId,
          amount: allocation.creditsPerPeriod,
          type: 'EARNED_SUBSCRIPTION',
          reason: `Subscription allocation for plan ${allocation.planId}`,
          description: `${allocation.creditsPerPeriod} credits allocated for ${allocation.period.toLowerCase()} period`,
          metadata: {
            allocationId: allocation.id,
            planId: allocation.planId,
            period: allocation.period,
            expirationPolicy: allocation.expirationPolicy
          },
          expiresAt
        });

        // 更新分配规则的下次分配时间
        const nextAllocationAt = calculateNextAllocationDate(
          allocation.period as CreditPeriod,
          now
        );

        const updatedAllocation = await tx.creditAllocation.update({
          where: { id: allocationId },
          data: {
            lastAllocationAt: now,
            nextAllocationAt,
            updatedAt: now
          }
        });

        return {
          allocation: updatedAllocation as CreditAllocation,
          transaction
        };
      });
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('executeAllocation', error as Error);
    }
  }

  /**
   * 执行所有到期的分配规则
   */
  async executeAllDueAllocations(): Promise<{
    successful: number;
    failed: number;
    results: Array<{
      allocationId: string;
      success: boolean;
      error?: string;
    }>;
  }> {
    const now = new Date();
    const results: Array<{
      allocationId: string;
      success: boolean;
      error?: string;
    }> = [];

    try {
      // 获取所有到期的分配规则
      const dueAllocations = await this.db.creditAllocation.findMany({
        where: {
          status: 'ACTIVE',
          nextAllocationAt: {
            lte: now
          }
        },
        orderBy: { nextAllocationAt: 'asc' }
      });

      // 逐个执行分配
      for (const allocation of dueAllocations) {
        try {
          await this.executeAllocation(allocation.id);
          results.push({
            allocationId: allocation.id,
            success: true
          });
        } catch (error) {
          results.push({
            allocationId: allocation.id,
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      return { successful, failed, results };
    } catch (error) {
      throw CreditErrors.database('executeAllDueAllocations', error as Error);
    }
  }

  /**
   * 获取下次分配时间
   */
  async getNextAllocationTime(accountId?: string): Promise<Date | null> {
    try {
      const where: any = { status: 'ACTIVE' };
      if (accountId) {
        where.accountId = accountId;
      }

      const allocation = await this.db.creditAllocation.findFirst({
        where,
        orderBy: { nextAllocationAt: 'asc' }
      });

      return allocation?.nextAllocationAt || null;
    } catch (error) {
      throw CreditErrors.database('getNextAllocationTime', error as Error);
    }
  }

  /**
   * 暂停账户的所有分配规则
   */
  async pauseAccountAllocations(accountId: string): Promise<number> {
    try {
      const result = await this.db.creditAllocation.updateMany({
        where: {
          accountId,
          status: 'ACTIVE'
        },
        data: {
          status: 'PAUSED',
          updatedAt: new Date()
        }
      });

      return result.count;
    } catch (error) {
      throw CreditErrors.database('pauseAccountAllocations', error as Error);
    }
  }

  /**
   * 恢复账户的所有分配规则
   */
  async resumeAccountAllocations(accountId: string): Promise<number> {
    try {
      const result = await this.db.creditAllocation.updateMany({
        where: {
          accountId,
          status: 'PAUSED'
        },
        data: {
          status: 'ACTIVE',
          updatedAt: new Date()
        }
      });

      return result.count;
    } catch (error) {
      throw CreditErrors.database('resumeAccountAllocations', error as Error);
    }
  }
}
