import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';
import { CreditError } from '../../src/types/credit-errors';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
    create: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
  },
  organization: {
    findFirst: vi.fn(),
  },
  user: {
    findFirst: vi.fn(),
  },
  $transaction: vi.fn(),
} as any;

// Mock authentication context
const mockAuthContext = {
  user: null as any,
  organization: null as any,
  permissions: [] as string[],
};

describe('Security: Access Control and Authorization', () => {
  let creditService: CreditService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient);
    
    // Reset auth context
    mockAuthContext.user = null;
    mockAuthContext.organization = null;
    mockAuthContext.permissions = [];
  });

  describe('User Permission Verification', () => {
    it('should allow users to access their own credit account', async () => {
      const userId = 'user-123';
      const mockAccount = TestFactories.createCreditAccount({ userId });
      
      mockAuthContext.user = { id: userId, role: 'user' };
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getAccount({ userId });
      
      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.findFirst).toHaveBeenCalledWith({
        where: { userId },
      });
    });

    it('should deny users access to other users credit accounts', async () => {
      const currentUserId = 'user-123';
      const targetUserId = 'user-456';
      
      mockAuthContext.user = { id: currentUserId, role: 'user' };

      await expect(async () => {
        await creditService.getAccount({ userId: targetUserId });
      }).rejects.toThrow('Access denied: Cannot access other user accounts');
    });

    it('should allow users to view their own transaction history', async () => {
      const userId = 'user-123';
      const mockAccount = TestFactories.createCreditAccount({ userId });
      const mockTransactions = [
        TestFactories.createCreditTransaction({ userId }),
        TestFactories.createCreditTransaction({ userId }),
      ];

      mockAuthContext.user = { id: userId, role: 'user' };
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(mockTransactions);

      const result = await creditService.getTransactionHistory({
        accountId: mockAccount.id,
        userId,
      });

      expect(result).toEqual(mockTransactions);
    });

    it('should deny users access to other users transaction history', async () => {
      const currentUserId = 'user-123';
      const targetUserId = 'user-456';
      const mockAccount = TestFactories.createCreditAccount({ userId: targetUserId });

      mockAuthContext.user = { id: currentUserId, role: 'user' };

      await expect(async () => {
        await creditService.getTransactionHistory({
          accountId: mockAccount.id,
          userId: currentUserId, // Trying to access with wrong user ID
        });
      }).rejects.toThrow('Access denied: Account does not belong to user');
    });
  });

  describe('Organization Permission Isolation', () => {
    it('should allow organization members to access organization credit account', async () => {
      const userId = 'user-123';
      const organizationId = 'org-456';
      const mockAccount = TestFactories.createCreditAccount({ 
        userId: null, 
        organizationId 
      });

      mockAuthContext.user = { id: userId, role: 'user' };
      mockAuthContext.organization = { id: organizationId };
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getAccount({ organizationId });

      expect(result).toEqual(mockAccount);
    });

    it('should deny access to other organizations credit accounts', async () => {
      const userId = 'user-123';
      const userOrgId = 'org-123';
      const targetOrgId = 'org-456';

      mockAuthContext.user = { id: userId, role: 'user' };
      mockAuthContext.organization = { id: userOrgId };

      await expect(async () => {
        await creditService.getAccount({ organizationId: targetOrgId });
      }).rejects.toThrow('Access denied: Cannot access other organization accounts');
    });

    it('should enforce organization-level credit consumption limits', async () => {
      const organizationId = 'org-123';
      const mockAccount = TestFactories.createCreditAccount({ 
        organizationId,
        currentBalance: 100 
      });

      mockAuthContext.organization = { 
        id: organizationId,
        creditLimits: { dailyLimit: 50, monthlyLimit: 1000 }
      };
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      // Mock daily consumption check
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue([
        TestFactories.createCreditTransaction({ 
          amount: -40, // Already consumed 40 today
          createdAt: new Date(),
        }),
      ]);

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 20, // Would exceed daily limit (40 + 20 > 50)
          reason: 'Feature usage',
        });
      }).rejects.toThrow('Daily credit consumption limit exceeded');
    });

    it('should validate organization membership before credit operations', async () => {
      const userId = 'user-123';
      const organizationId = 'org-456';
      const mockAccount = TestFactories.createCreditAccount({ organizationId });

      mockAuthContext.user = { id: userId, role: 'user' };
      // User not in organization
      mockPrismaClient.organization.findFirst.mockResolvedValue(null);

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 10,
          reason: 'Unauthorized access attempt',
        });
      }).rejects.toThrow('User is not a member of the organization');
    });
  });

  describe('Admin Permission Testing', () => {
    it('should allow admins to access any user account', async () => {
      const adminUserId = 'admin-123';
      const targetUserId = 'user-456';
      const mockAccount = TestFactories.createCreditAccount({ userId: targetUserId });

      mockAuthContext.user = { id: adminUserId, role: 'admin' };
      mockAuthContext.permissions = ['credits:read:all'];
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getAccount({ userId: targetUserId });

      expect(result).toEqual(mockAccount);
    });

    it('should allow admins to modify any account status', async () => {
      const adminUserId = 'admin-123';
      const targetAccountId = 'account-456';
      const mockAccount = TestFactories.createCreditAccount({ 
        id: targetAccountId,
        status: 'ACTIVE' 
      });
      const updatedAccount = { ...mockAccount, status: 'SUSPENDED' as const };

      mockAuthContext.user = { id: adminUserId, role: 'admin' };
      mockAuthContext.permissions = ['credits:write:all'];
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditAccount.update.mockResolvedValue(updatedAccount);

      const result = await creditService.updateAccountStatus(targetAccountId, 'SUSPENDED');

      expect(result.status).toBe('SUSPENDED');
    });

    it('should deny non-admins from modifying account status', async () => {
      const userId = 'user-123';
      const accountId = 'account-456';

      mockAuthContext.user = { id: userId, role: 'user' };
      mockAuthContext.permissions = ['credits:read:own'];

      await expect(async () => {
        await creditService.updateAccountStatus(accountId, 'SUSPENDED');
      }).rejects.toThrow('Insufficient permissions: Admin access required');
    });

    it('should allow admins to perform bulk operations', async () => {
      const adminUserId = 'admin-123';
      const accountIds = ['account-1', 'account-2', 'account-3'];

      mockAuthContext.user = { id: adminUserId, role: 'admin' };
      mockAuthContext.permissions = ['credits:write:all', 'credits:bulk:operations'];

      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrismaClient);
      });

      const result = await creditService.bulkAddCredits({
        operations: accountIds.map(id => ({
          accountId: id,
          amount: 100,
          reason: 'Bulk credit addition',
        })),
      });

      expect(result.successCount).toBe(3);
      expect(result.failureCount).toBe(0);
    });

    it('should deny non-admins from performing bulk operations', async () => {
      const userId = 'user-123';

      mockAuthContext.user = { id: userId, role: 'user' };
      mockAuthContext.permissions = ['credits:read:own'];

      await expect(async () => {
        await creditService.bulkAddCredits({
          operations: [{
            accountId: 'account-1',
            amount: 100,
            reason: 'Unauthorized bulk operation',
          }],
        });
      }).rejects.toThrow('Insufficient permissions: Bulk operations require admin access');
    });
  });

  describe('Cross-Account Access Prevention', () => {
    it('should prevent credit transfers between unrelated accounts', async () => {
      const sourceUserId = 'user-123';
      const targetUserId = 'user-456';
      const sourceAccount = TestFactories.createCreditAccount({ 
        userId: sourceUserId,
        currentBalance: 1000 
      });
      const targetAccount = TestFactories.createCreditAccount({ 
        userId: targetUserId 
      });

      mockAuthContext.user = { id: sourceUserId, role: 'user' };
      mockPrismaClient.creditAccount.findFirst
        .mockResolvedValueOnce(sourceAccount)
        .mockResolvedValueOnce(targetAccount);

      await expect(async () => {
        await creditService.transferCredits({
          fromAccountId: sourceAccount.id,
          toAccountId: targetAccount.id,
          amount: 100,
          reason: 'Unauthorized transfer',
        });
      }).rejects.toThrow('Cross-account transfers not allowed without proper authorization');
    });

    it('should allow credit transfers within same organization', async () => {
      const organizationId = 'org-123';
      const sourceAccount = TestFactories.createCreditAccount({ 
        organizationId,
        currentBalance: 1000 
      });
      const targetAccount = TestFactories.createCreditAccount({ 
        organizationId 
      });

      mockAuthContext.organization = { id: organizationId };
      mockAuthContext.permissions = ['credits:transfer:organization'];
      mockPrismaClient.creditAccount.findFirst
        .mockResolvedValueOnce(sourceAccount)
        .mockResolvedValueOnce(targetAccount);

      const mockTransaction = TestFactories.createCreditTransaction({
        amount: -100,
        type: 'TRANSFERRED_OUT',
      });
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const result = await creditService.transferCredits({
        fromAccountId: sourceAccount.id,
        toAccountId: targetAccount.id,
        amount: 100,
        reason: 'Internal organization transfer',
      });

      expect(result).toBeDefined();
    });

    it('should validate account ownership before credit operations', async () => {
      const userId = 'user-123';
      const otherUserAccount = TestFactories.createCreditAccount({ 
        userId: 'user-456' 
      });

      mockAuthContext.user = { id: userId, role: 'user' };

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: otherUserAccount.id,
          amount: 10,
          reason: 'Unauthorized consumption',
        });
      }).rejects.toThrow('Account ownership validation failed');
    });
  });

  describe('Permission Escalation Attack Prevention', () => {
    it('should prevent role elevation through API manipulation', async () => {
      const userId = 'user-123';
      
      mockAuthContext.user = { id: userId, role: 'user' };
      
      // Attempt to escalate permissions by modifying auth context
      const maliciousContext = {
        ...mockAuthContext,
        user: { ...mockAuthContext.user, role: 'admin' },
        permissions: ['credits:write:all'],
      };

      // Service should validate against original auth context, not manipulated one
      await expect(async () => {
        await creditService.updateAccountStatus('account-123', 'SUSPENDED');
      }).rejects.toThrow('Insufficient permissions');
    });

    it('should validate JWT token integrity', async () => {
      const tamperedToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.TAMPERED_PAYLOAD.signature';
      
      await expect(async () => {
        await creditService.validateAuthToken(tamperedToken);
      }).rejects.toThrow('Invalid or tampered authentication token');
    });

    it('should enforce session timeout', async () => {
      const expiredSession = {
        userId: 'user-123',
        issuedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
        expiresAt: new Date(Date.now() - 1 * 60 * 60 * 1000),  // 1 hour ago
      };

      await expect(async () => {
        await creditService.validateSession(expiredSession);
      }).rejects.toThrow('Session expired');
    });

    it('should prevent concurrent session abuse', async () => {
      const userId = 'user-123';
      const sessionIds = ['session-1', 'session-2', 'session-3', 'session-4', 'session-5'];

      // Mock multiple active sessions for same user
      mockPrismaClient.user.findFirst.mockResolvedValue({
        id: userId,
        activeSessions: sessionIds,
      });

      await expect(async () => {
        await creditService.validateConcurrentSessions(userId);
      }).rejects.toThrow('Maximum concurrent sessions exceeded');
    });
  });

  describe('API Rate Limiting and Abuse Prevention', () => {
    it('should enforce rate limits per user', async () => {
      const userId = 'user-123';
      mockAuthContext.user = { id: userId, role: 'user' };

      // Simulate rapid API calls
      const promises = Array.from({ length: 100 }, () =>
        creditService.getBalance({ userId })
      );

      const results = await Promise.allSettled(promises);
      const rejectedCount = results.filter(r => r.status === 'rejected').length;

      expect(rejectedCount).toBeGreaterThan(0);
    });

    it('should enforce stricter limits for sensitive operations', async () => {
      const userId = 'user-123';
      const accountId = 'account-123';
      mockAuthContext.user = { id: userId, role: 'user' };

      // Simulate rapid credit consumption attempts
      const promises = Array.from({ length: 20 }, () =>
        creditService.consumeCredits({
          accountId,
          amount: 1,
          reason: 'Rapid consumption test',
        })
      );

      const results = await Promise.allSettled(promises);
      const rejectedCount = results.filter(r => r.status === 'rejected').length;

      // Should reject more aggressively for sensitive operations
      expect(rejectedCount).toBeGreaterThan(15);
    });

    it('should implement IP-based rate limiting', async () => {
      const ipAddress = '*************';
      
      // Mock multiple requests from same IP
      for (let i = 0; i < 1000; i++) {
        try {
          await creditService.handleRequest({
            ipAddress,
            endpoint: '/credits/account',
            method: 'GET',
          });
        } catch (error) {
          if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
            expect(i).toBeLessThan(1000); // Should be blocked before 1000 requests
            break;
          }
        }
      }
    });
  });
});
