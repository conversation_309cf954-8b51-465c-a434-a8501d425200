import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';
import { CreditError } from '../../src/types/credit-errors';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    update: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findMany: vi.fn(),
  },
} as any;

describe('Unit Tests: Credit Calculation Logic', () => {
  let creditService: CreditService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient);
  });

  describe('Zero Credit Handling', () => {
    it('should handle zero credit additions correctly', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 0,
          reason: 'Zero credit test',
        });
      }).rejects.toThrow('Invalid credit amount: must be greater than 0');
    });

    it('should handle zero credit consumption correctly', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 0,
          reason: 'Zero consumption test',
        });
      }).rejects.toThrow('Invalid credit amount: must be greater than 0');
    });

    it('should allow accounts with zero balance', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 0,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const balance = await creditService.getBalance({ accountId: mockAccount.id });

      expect(balance.currentBalance).toBe(0);
      expect(balance.availableBalance).toBe(0);
    });
  });

  describe('Negative Credit Protection', () => {
    it('should prevent negative credit additions', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: -100,
          reason: 'Negative addition test',
        });
      }).rejects.toThrow('Invalid credit amount: cannot be negative');
    });

    it('should prevent negative credit consumption', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: -50,
          reason: 'Negative consumption test',
        });
      }).rejects.toThrow('Invalid credit amount: cannot be negative');
    });

    it('should prevent balance from going negative', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 100,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 150, // More than available balance
          reason: 'Overdraft test',
        });
      }).rejects.toThrow('Insufficient credits: balance would become negative');
    });

    it('should handle edge case of exact balance consumption', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 100,
      });
      const mockTransaction = TestFactories.createCreditTransaction({
        amount: -100,
        balanceAfter: 0,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      mockPrismaClient.creditAccount.update.mockResolvedValue({
        ...mockAccount,
        currentBalance: 0,
      });

      const result = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100, // Exact balance
        reason: 'Exact balance consumption',
      });

      expect(result.balanceAfter).toBe(0);
    });
  });

  describe('Maximum Credit Limits', () => {
    it('should enforce maximum credit limit per account', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: *********, // Near maximum
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: ********00, // Would exceed maximum
          reason: 'Maximum limit test',
        });
      }).rejects.toThrow('Credit amount exceeds maximum allowed value');
    });

    it('should enforce maximum single transaction limit', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: ********, // Exceeds single transaction limit
          reason: 'Large transaction test',
        });
      }).rejects.toThrow('Single transaction amount exceeds maximum limit');
    });

    it('should calculate maximum safe integer correctly', () => {
      const maxSafeCredit = creditService.getMaximumCreditAmount();
      
      expect(maxSafeCredit).toBeLessThanOrEqual(Number.MAX_SAFE_INTEGER);
      expect(maxSafeCredit).toBeGreaterThan(0);
      expect(Number.isInteger(maxSafeCredit)).toBe(true);
    });

    it('should handle maximum balance calculations', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: Number.MAX_SAFE_INTEGER - 1000,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 2000, // Would exceed MAX_SAFE_INTEGER
          reason: 'Overflow test',
        });
      }).rejects.toThrow('Operation would cause integer overflow');
    });
  });

  describe('Precision and Decimal Handling', () => {
    it('should handle decimal credit amounts correctly', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000.50,
      });
      const mockTransaction = TestFactories.createCreditTransaction({
        amount: 99.75,
        balanceAfter: 1100.25,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const result = await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 99.75,
        reason: 'Decimal amount test',
      });

      expect(result.amount).toBe(99.75);
      expect(result.balanceAfter).toBe(1100.25);
    });

    it('should enforce decimal precision limits', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 100.*********, // Too many decimal places
          reason: 'Precision test',
        });
      }).rejects.toThrow('Credit amount has too many decimal places');
    });

    it('should round calculations correctly', () => {
      const result1 = creditService.roundCreditAmount(100.126); // Should round to 100.13
      const result2 = creditService.roundCreditAmount(100.124); // Should round to 100.12
      const result3 = creditService.roundCreditAmount(100.125); // Should round to 100.13 (banker's rounding)

      expect(result1).toBe(100.13);
      expect(result2).toBe(100.12);
      expect(result3).toBe(100.12); // Banker's rounding to even
    });

    it('should handle floating point precision issues', () => {
      const amount1 = 0.1;
      const amount2 = 0.2;
      const expected = 0.3;

      const result = creditService.addCreditAmounts(amount1, amount2);

      expect(result).toBe(expected);
      expect(result).not.toBe(amount1 + amount2); // Direct addition would be 0.*****************
    });
  });

  describe('Credit Calculation Edge Cases', () => {
    it('should handle very small credit amounts', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });
      const mockTransaction = TestFactories.createCreditTransaction({
        amount: 0.01,
        balanceAfter: 1000.01,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const result = await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 0.01, // Minimum credit amount
        reason: 'Minimum amount test',
      });

      expect(result.amount).toBe(0.01);
    });

    it('should calculate percentage-based credits correctly', () => {
      const baseAmount = 1000;
      const percentage = 15.5; // 15.5%

      const result = creditService.calculatePercentageCredits(baseAmount, percentage);

      expect(result).toBe(155); // 1000 * 0.155
    });

    it('should handle bonus credit calculations', () => {
      const purchaseAmount = 1000;
      const bonusPercentage = 10; // 10% bonus

      const result = creditService.calculateBonusCredits(purchaseAmount, bonusPercentage);

      expect(result.baseCredits).toBe(1000);
      expect(result.bonusCredits).toBe(100);
      expect(result.totalCredits).toBe(1100);
    });

    it('should calculate tiered pricing correctly', () => {
      const tiers = [
        { min: 0, max: 100, rate: 1.0 },
        { min: 101, max: 500, rate: 0.95 },
        { min: 501, max: Infinity, rate: 0.90 },
      ];

      const result1 = creditService.calculateTieredCredits(50, tiers);
      const result2 = creditService.calculateTieredCredits(300, tiers);
      const result3 = creditService.calculateTieredCredits(1000, tiers);

      expect(result1).toBe(50); // 50 * 1.0
      expect(result2).toBe(285); // 300 * 0.95
      expect(result3).toBe(900); // 1000 * 0.90
    });
  });

  describe('Balance Calculation Integrity', () => {
    it('should maintain balance consistency across operations', async () => {
      const initialBalance = 1000;
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: initialBalance,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const operations = [
        { type: 'add', amount: 500 },
        { type: 'consume', amount: 200 },
        { type: 'add', amount: 100 },
        { type: 'consume', amount: 300 },
      ];

      let expectedBalance = initialBalance;
      for (const op of operations) {
        if (op.type === 'add') {
          expectedBalance += op.amount;
        } else {
          expectedBalance -= op.amount;
        }
      }

      const finalBalance = creditService.calculateFinalBalance(initialBalance, operations);

      expect(finalBalance).toBe(expectedBalance);
      expect(finalBalance).toBe(1100); // 1000 + 500 - 200 + 100 - 300
    });

    it('should detect balance calculation errors', () => {
      const transactions = [
        { amount: 1000, balanceBefore: 0, balanceAfter: 1000 },
        { amount: -200, balanceBefore: 1000, balanceAfter: 800 },
        { amount: 500, balanceBefore: 800, balanceAfter: 1400 }, // Incorrect balance
      ];

      const isValid = creditService.validateTransactionChain(transactions);

      expect(isValid).toBe(false);
    });

    it('should recalculate balance from transaction history', async () => {
      const transactions = [
        TestFactories.createCreditTransaction({ amount: 1000, type: 'EARNED_PURCHASE' }),
        TestFactories.createCreditTransaction({ amount: -200, type: 'CONSUMED_FEATURE' }),
        TestFactories.createCreditTransaction({ amount: 500, type: 'EARNED_BONUS' }),
        TestFactories.createCreditTransaction({ amount: -100, type: 'CONSUMED_FEATURE' }),
      ];

      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(transactions);

      const recalculatedBalance = await creditService.recalculateBalanceFromHistory('account-123');

      expect(recalculatedBalance).toBe(1200); // 1000 - 200 + 500 - 100
    });

    it('should handle concurrent balance updates correctly', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      // Simulate concurrent operations
      const operations = [
        creditService.addCredits({ accountId: mockAccount.id, amount: 100, reason: 'Op 1' }),
        creditService.consumeCredits({ accountId: mockAccount.id, amount: 50, reason: 'Op 2' }),
        creditService.addCredits({ accountId: mockAccount.id, amount: 200, reason: 'Op 3' }),
      ];

      // Mock database transaction to ensure atomicity
      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrismaClient);
      });

      const results = await Promise.allSettled(operations);
      const successCount = results.filter(r => r.status === 'fulfilled').length;

      // All operations should succeed due to proper locking
      expect(successCount).toBe(3);
    });
  });

  describe('Credit Expiration Calculations', () => {
    it('should calculate credit expiration dates correctly', () => {
      const baseDate = new Date('2024-01-01T00:00:00Z');
      const validityDays = 30;

      const expirationDate = creditService.calculateExpirationDate(baseDate, validityDays);

      expect(expirationDate).toEqual(new Date('2024-01-31T00:00:00Z'));
    });

    it('should handle leap year calculations', () => {
      const baseDate = new Date('2024-02-01T00:00:00Z'); // 2024 is a leap year
      const validityDays = 28;

      const expirationDate = creditService.calculateExpirationDate(baseDate, validityDays);

      expect(expirationDate).toEqual(new Date('2024-02-29T00:00:00Z'));
    });

    it('should calculate remaining credit validity', () => {
      const currentDate = new Date('2024-01-15T00:00:00Z');
      const expirationDate = new Date('2024-01-31T00:00:00Z');

      const remainingDays = creditService.calculateRemainingValidity(currentDate, expirationDate);

      expect(remainingDays).toBe(16);
    });

    it('should identify expired credits', () => {
      const currentDate = new Date('2024-02-01T00:00:00Z');
      const expirationDate = new Date('2024-01-31T00:00:00Z');

      const isExpired = creditService.isCreditExpired(currentDate, expirationDate);

      expect(isExpired).toBe(true);
    });
  });
});
