import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    updateMany: vi.fn()
  },
  creditTransaction: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    update: vi.fn(),
    delete: vi.fn()
  },
  $transaction: vi.fn()
} as any;

describe('CreditService', () => {
  let creditService: CreditService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient);
  });

  describe('createAccount', () => {
    it('should create account successfully for user', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAccount.create.mockResolvedValue(mockAccount);

      const result = await creditService.createAccount({
        userId: 'user-123',
        initialBalance: 100
      });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.findFirst).toHaveBeenCalledWith({
        where: {
          OR: [
            { userId: 'user-123' },
            { organizationId: undefined }
          ]
        }
      });
      expect(mockPrismaClient.creditAccount.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-123',
          organizationId: undefined,
          currentBalance: 100,
          totalEarned: 100,
          status: 'ACTIVE'
        }
      });
    });

    it('should create account successfully for organization', async () => {
      const mockAccount = TestFactories.createOrganizationAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAccount.create.mockResolvedValue(mockAccount);

      const result = await creditService.createAccount({
        organizationId: 'org-123',
        initialBalance: 0
      });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.create).toHaveBeenCalledWith({
        data: {
          userId: undefined,
          organizationId: 'org-123',
          currentBalance: 0,
          totalEarned: 0,
          status: 'ACTIVE'
        }
      });
    });

    it('should throw error when both userId and organizationId provided', async () => {
      await expect(creditService.createAccount({
        userId: 'user-123',
        organizationId: 'org-123'
      })).rejects.toThrow('Cannot specify both userId and organizationId');
    });

    it('should throw error when neither userId nor organizationId provided', async () => {
      await expect(creditService.createAccount({})).rejects.toThrow(
        'Must specify either userId or organizationId'
      );
    });

    it('should throw error when account already exists', async () => {
      const existingAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(existingAccount);

      await expect(creditService.createAccount({
        userId: 'user-123'
      })).rejects.toThrow('Credit account already exists');
    });

    it('should handle database errors', async () => {
      mockPrismaClient.creditAccount.findFirst.mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(creditService.createAccount({
        userId: 'user-123'
      })).rejects.toThrow('Database operation failed');
    });
  });

  describe('getAccount', () => {
    it('should return account when found by userId', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getAccount({ userId: 'user-123' });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.findFirst).toHaveBeenCalledWith({
        where: {
          OR: [
            { userId: 'user-123' },
            { organizationId: undefined }
          ]
        }
      });
    });

    it('should return account when found by organizationId', async () => {
      const mockAccount = TestFactories.createOrganizationAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getAccount({ organizationId: 'org-123' });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.findFirst).toHaveBeenCalledWith({
        where: {
          OR: [
            { userId: undefined },
            { organizationId: 'org-123' }
          ]
        }
      });
    });

    it('should return null when account not found', async () => {
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);

      const result = await creditService.getAccount({ userId: 'user-123' });

      expect(result).toBeNull();
    });

    it('should throw error when neither userId nor organizationId provided', async () => {
      await expect(creditService.getAccount({})).rejects.toThrow(
        'Must specify either userId or organizationId'
      );
    });
  });

  describe('getOrCreateAccount', () => {
    it('should return existing account when found', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getOrCreateAccount({ userId: 'user-123' });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.create).not.toHaveBeenCalled();
    });

    it('should create new account when not found', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAccount.create.mockResolvedValue(mockAccount);

      const result = await creditService.getOrCreateAccount({ userId: 'user-123' });

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-123',
          organizationId: undefined,
          currentBalance: 0,
          totalEarned: 0,
          status: 'ACTIVE'
        }
      });
    });
  });

  describe('updateAccountStatus', () => {
    it('should update account status successfully', async () => {
      const mockAccount = TestFactories.createCreditAccount({ status: 'SUSPENDED' });
      mockPrismaClient.creditAccount.update.mockResolvedValue(mockAccount);

      const result = await creditService.updateAccountStatus('account-123', 'SUSPENDED');

      expect(result).toEqual(mockAccount);
      expect(mockPrismaClient.creditAccount.update).toHaveBeenCalledWith({
        where: { id: 'account-123' },
        data: { 
          status: 'SUSPENDED',
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should throw error for invalid account ID', async () => {
      await expect(creditService.updateAccountStatus('', 'SUSPENDED')).rejects.toThrow(
        'Invalid account ID format'
      );
    });

    it('should handle database errors', async () => {
      mockPrismaClient.creditAccount.update.mockRejectedValue(
        TestFactories.createPrismaError('P2025', 'Record not found')
      );

      await expect(creditService.updateAccountStatus('account-123', 'SUSPENDED'))
        .rejects.toThrow('Database operation failed');
    });
  });

  describe('getBalance', () => {
    it('should return current balance when account exists', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 500 });
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.getBalance({ userId: 'user-123' });

      expect(result).toBe(500);
    });

    it('should return 0 when account does not exist', async () => {
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);

      const result = await creditService.getBalance({ userId: 'user-123' });

      expect(result).toBe(0);
    });
  });

  describe('hasEnoughCredits', () => {
    it('should return true when balance is sufficient', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 500 });
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.hasEnoughCredits({ userId: 'user-123' }, 300);

      expect(result).toBe(true);
    });

    it('should return false when balance is insufficient', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 100 });
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const result = await creditService.hasEnoughCredits({ userId: 'user-123' }, 300);

      expect(result).toBe(false);
    });

    it('should return false for invalid amount', async () => {
      const result = await creditService.hasEnoughCredits({ userId: 'user-123' }, -100);

      expect(result).toBe(false);
    });

    it('should return false when account does not exist', async () => {
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);

      const result = await creditService.hasEnoughCredits({ userId: 'user-123' }, 100);

      expect(result).toBe(false);
    });
  });

  describe('createTransaction', () => {
    it('should create transaction successfully', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 500 });
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return callback({
          creditAccount: {
            findUnique: vi.fn().mockResolvedValue(mockAccount),
            update: vi.fn().mockResolvedValue({ ...mockAccount, currentBalance: 600 })
          },
          creditTransaction: {
            create: vi.fn().mockResolvedValue(mockTransaction)
          }
        });
      });

      const result = await creditService.createTransaction({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Test transaction'
      });

      expect(result).toEqual(mockTransaction);
    });

    it('should throw error for invalid account ID', async () => {
      await expect(creditService.createTransaction({
        accountId: '',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Test'
      })).rejects.toThrow('Invalid account ID format');
    });

    it('should throw error for invalid amount', async () => {
      await expect(creditService.createTransaction({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: -1,
        reason: 'Test'
      })).rejects.toThrow('Amount must be a positive integer');
    });

    it('should throw error when account not found', async () => {
      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return callback({
          creditAccount: {
            findUnique: vi.fn().mockResolvedValue(null)
          }
        });
      });

      await expect(creditService.createTransaction({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Test'
      })).rejects.toThrow('Credit account not found');
    });

    it('should throw error when account is suspended', async () => {
      const suspendedAccount = TestFactories.createCreditAccount({ status: 'SUSPENDED' });

      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return callback({
          creditAccount: {
            findUnique: vi.fn().mockResolvedValue(suspendedAccount)
          }
        });
      });

      await expect(creditService.createTransaction({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Test'
      })).rejects.toThrow('Credit account is suspended');
    });

    it('should throw error for insufficient balance on negative transaction', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 50 });

      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return callback({
          creditAccount: {
            findUnique: vi.fn().mockResolvedValue(mockAccount)
          }
        });
      });

      await expect(creditService.createTransaction({
        accountId: 'account-123',
        type: 'SPENT_FEATURE',
        amount: -100,
        reason: 'Test'
      })).rejects.toThrow('Insufficient credit balance');
    });
  });

  describe('consumeCredits', () => {
    it('should consume credits successfully', async () => {
      const mockTransaction = TestFactories.createCreditTransaction({
        type: 'SPENT_FEATURE',
        amount: -50
      });

      vi.spyOn(creditService, 'createTransaction').mockResolvedValue(mockTransaction);

      const result = await creditService.consumeCredits({
        accountId: 'account-123',
        amount: 50,
        reason: 'Feature usage',
        featureId: 'ai_chat'
      });

      expect(result).toEqual(mockTransaction);
      expect(creditService.createTransaction).toHaveBeenCalledWith({
        accountId: 'account-123',
        type: 'SPENT_FEATURE',
        amount: -50,
        reason: 'Feature usage',
        description: undefined,
        metadata: undefined,
        featureId: 'ai_chat'
      });
    });
  });

  describe('addCredits', () => {
    it('should add credits successfully', async () => {
      const mockTransaction = TestFactories.createCreditTransaction({
        type: 'EARNED_PURCHASE',
        amount: 100
      });

      vi.spyOn(creditService, 'createTransaction').mockResolvedValue(mockTransaction);

      const result = await creditService.addCredits({
        accountId: 'account-123',
        amount: 100,
        type: 'EARNED_PURCHASE',
        reason: 'Package purchase'
      });

      expect(result).toEqual(mockTransaction);
      expect(creditService.createTransaction).toHaveBeenCalledWith({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Package purchase',
        description: undefined,
        metadata: undefined,
        expiresAt: undefined,
        purchaseId: undefined,
        referenceId: undefined
      });
    });
  });
});
