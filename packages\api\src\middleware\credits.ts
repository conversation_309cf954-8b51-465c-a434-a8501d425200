import { CreditService, CREDIT_CONFIG } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";

// 创建积分服务实例
const creditService = new CreditService(db);

/**
 * 积分验证中间件选项
 */
interface CreditMiddlewareOptions {
  /** 功能ID */
  featureId: string;
  /** 所需积分数量 */
  requiredCredits?: number;
  /** 是否自动消耗积分 */
  autoConsume?: boolean;
  /** 错误消息 */
  errorMessage?: string;
  /** 是否允许组织级别的积分 */
  allowOrganizationCredits?: boolean;
}

/**
 * 积分验证中间件
 * 验证用户是否有足够的积分来访问特定功能
 */
export function creditMiddleware(options: CreditMiddlewareOptions) {
  return async (c: Context, next: Next) => {
    try {
      const user = c.get("user");
      if (!user) {
        throw new HTTPException(401, { message: "用户未认证" });
      }

      const {
        featureId,
        requiredCredits,
        autoConsume = false,
        errorMessage = "积分不足",
        allowOrganizationCredits = true,
      } = options;

      // 从配置中获取功能消耗规则
      const featureConfig = CREDIT_CONFIG.features[featureId];
      if (!featureConfig) {
        logger.warn("未找到功能配置", { featureId });
        // 如果没有配置，允许访问
        return await next();
      }

      // 确定所需积分数量
      const creditsNeeded = requiredCredits ?? featureConfig.creditsPerUse;

      // 获取用户的积分账户
      const userAccount = await creditService.getOrCreateAccount({
        userId: user.id,
      });

      let hasEnoughCredits = false;
      let accountToUse = userAccount;

      // 检查用户账户余额
      const userBalance = await creditService.getBalance({ userId: user.id });
      if (userBalance >= creditsNeeded) {
        hasEnoughCredits = true;
        accountToUse = userAccount;
      }

      // 如果用户积分不足且允许使用组织积分，检查组织账户
      if (!hasEnoughCredits && allowOrganizationCredits && user.organizationId) {
        const orgAccount = await creditService.getOrCreateAccount({
          organizationId: user.organizationId,
        });

        const orgBalance = await creditService.getBalance({
          organizationId: user.organizationId,
        });

        if (orgBalance >= creditsNeeded) {
          hasEnoughCredits = true;
          accountToUse = orgAccount;
        }
      }

      // 如果积分不足，返回错误
      if (!hasEnoughCredits) {
        logger.warn("积分不足", {
          userId: user.id,
          organizationId: user.organizationId,
          featureId,
          creditsNeeded,
          userBalance,
          orgBalance: user.organizationId ? await creditService.getBalance({
            organizationId: user.organizationId,
          }) : 0,
        });

        throw new HTTPException(402, { message: errorMessage });
      }

      // 如果需要自动消耗积分
      if (autoConsume) {
        try {
          const transaction = await creditService.consumeCredits({
            accountId: accountToUse.id,
            amount: creditsNeeded,
            reason: `使用功能: ${featureId}`,
            featureId,
          });

          // 将交易信息添加到上下文中
          c.set("creditTransaction", transaction);

          logger.info("积分自动消耗成功", {
            userId: user.id,
            organizationId: user.organizationId,
            featureId,
            amount: creditsNeeded,
            transactionId: transaction.id,
            accountId: accountToUse.id,
          });
        } catch (error) {
          logger.error("积分自动消耗失败", {
            error: error instanceof Error ? error.message : String(error),
            userId: user.id,
            featureId,
            amount: creditsNeeded,
          });

          throw new HTTPException(500, { message: "积分消耗失败" });
        }
      }

      // 将积分信息添加到上下文中
      c.set("creditInfo", {
        featureId,
        creditsNeeded,
        accountUsed: accountToUse,
        hasEnoughCredits,
        autoConsumed: autoConsume,
      });

      return await next();
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }

      logger.error("积分中间件错误", {
        error: error instanceof Error ? error.message : String(error),
        featureId: options.featureId,
        userId: c.get("user")?.id,
      });

      throw new HTTPException(500, { message: "积分验证失败" });
    }
  };
}

/**
 * 创建特定功能的积分中间件
 */
export function createFeatureCreditMiddleware(
  featureId: string,
  options: Omit<CreditMiddlewareOptions, "featureId"> = {}
) {
  return creditMiddleware({
    featureId,
    ...options,
  });
}

/**
 * 常用功能的积分中间件
 */
export const creditMiddlewares = {
  // AI 功能
  aiChat: createFeatureCreditMiddleware("ai_chat", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用AI聊天功能",
  }),

  aiImageGeneration: createFeatureCreditMiddleware("ai_image_generation", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用AI图片生成功能",
  }),

  aiTextAnalysis: createFeatureCreditMiddleware("ai_text_analysis", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用AI文本分析功能",
  }),

  // 存储功能
  fileUpload: createFeatureCreditMiddleware("file_upload", {
    autoConsume: true,
    errorMessage: "积分不足，无法上传文件",
  }),

  // 地图功能
  mapGeocoding: createFeatureCreditMiddleware("map_geocoding", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用地图地理编码功能",
  }),

  mapRouting: createFeatureCreditMiddleware("map_routing", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用地图路线规划功能",
  }),

  // API 调用
  apiCall: createFeatureCreditMiddleware("api_call", {
    autoConsume: true,
    errorMessage: "积分不足，无法调用API",
  }),

  // 导出功能
  dataExport: createFeatureCreditMiddleware("data_export", {
    autoConsume: true,
    errorMessage: "积分不足，无法导出数据",
  }),

  // 高级功能
  advancedAnalytics: createFeatureCreditMiddleware("advanced_analytics", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用高级分析功能",
  }),

  customIntegration: createFeatureCreditMiddleware("custom_integration", {
    autoConsume: true,
    errorMessage: "积分不足，无法使用自定义集成功能",
  }),
};

/**
 * 积分检查中间件（不消耗积分，仅检查）
 */
export function creditCheckMiddleware(
  featureId: string,
  options: Omit<CreditMiddlewareOptions, "featureId" | "autoConsume"> = {}
) {
  return creditMiddleware({
    featureId,
    autoConsume: false,
    ...options,
  });
}

/**
 * 批量积分消耗中间件
 * 用于需要消耗多个功能积分的场景
 */
export function batchCreditMiddleware(features: Array<{
  featureId: string;
  requiredCredits?: number;
}>) {
  return async (c: Context, next: Next) => {
    const user = c.get("user");
    if (!user) {
      throw new HTTPException(401, { message: "用户未认证" });
    }

    // 计算总所需积分
    let totalCreditsNeeded = 0;
    const featureDetails: Array<{
      featureId: string;
      creditsNeeded: number;
    }> = [];

    for (const feature of features) {
      const featureConfig = CREDIT_CONFIG.features[feature.featureId];
      const creditsNeeded = feature.requiredCredits ?? featureConfig?.creditsPerUse ?? 0;
      
      totalCreditsNeeded += creditsNeeded;
      featureDetails.push({
        featureId: feature.featureId,
        creditsNeeded,
      });
    }

    // 检查积分余额
    const userBalance = await creditService.getBalance({ userId: user.id });
    let orgBalance = 0;
    
    if (user.organizationId) {
      orgBalance = await creditService.getBalance({
        organizationId: user.organizationId,
      });
    }

    if (userBalance + orgBalance < totalCreditsNeeded) {
      throw new HTTPException(402, { 
        message: `积分不足，需要 ${totalCreditsNeeded} 积分，当前可用 ${userBalance + orgBalance} 积分` 
      });
    }

    // 将批量积分信息添加到上下文
    c.set("batchCreditInfo", {
      features: featureDetails,
      totalCreditsNeeded,
      userBalance,
      orgBalance,
    });

    return await next();
  };
}
