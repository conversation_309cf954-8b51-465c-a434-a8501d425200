import type { PrismaClient } from '@repo/database';
import type {
  CreditAccount,
  CreditTransaction,
  CreateCreditAccountInput,
  CreateCreditTransactionInput,
  CreditBalanceQuery,
  CreditConsumptionInput,
  CreditAdditionInput,
  CreditTransferInput,
  CreditTransactionQuery,
  PaginatedResponse
} from '../types/credit-types';
import {
  CreditErrors,
  type CreditError
} from '../types/credit-errors';
import {
  calculateNewBalance,
  hasEnoughCredits,
  createPaginatedResponse,
  calculateOffset,
  isValidAccountId,
  isValidCreditAmount
} from '../utils/credit-helpers';

/**
 * 积分系统核心服务
 * 负责积分账户管理、积分交易、余额查询等核心功能
 */
export class CreditService {
  constructor(private readonly db: PrismaClient) {}

  // ================================
  // 积分账户管理
  // ================================

  /**
   * 创建积分账户
   */
  async createAccount(input: CreateCreditAccountInput): Promise<CreditAccount> {
    try {
      // 验证输入
      if (input.userId && input.organizationId) {
        throw CreditErrors.validation('account', input, 'Cannot specify both userId and organizationId');
      }
      
      if (!input.userId && !input.organizationId) {
        throw CreditErrors.validation('account', input, 'Must specify either userId or organizationId');
      }

      // 检查账户是否已存在
      const existingAccount = await this.db.creditAccount.findFirst({
        where: {
          OR: [
            { userId: input.userId },
            { organizationId: input.organizationId }
          ]
        }
      });

      if (existingAccount) {
        throw new CreditErrors.accountAlreadyExists(
          input.userId || input.organizationId || 'unknown'
        );
      }

      // 创建账户
      const account = await this.db.creditAccount.create({
        data: {
          userId: input.userId,
          organizationId: input.organizationId,
          currentBalance: input.initialBalance || 0,
          totalEarned: input.initialBalance || 0,
          status: input.status || 'ACTIVE'
        }
      });

      // 如果有初始余额，创建初始交易记录
      if (input.initialBalance && input.initialBalance > 0) {
        await this.createTransaction({
          accountId: account.id,
          type: 'ADJUSTMENT',
          amount: input.initialBalance,
          reason: 'Initial balance allocation'
        });
      }

      return account as CreditAccount;
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('createAccount', error as Error);
    }
  }

  /**
   * 获取积分账户
   */
  async getAccount(query: CreditBalanceQuery): Promise<CreditAccount | null> {
    try {
      if (!query.userId && !query.organizationId) {
        throw CreditErrors.validation('query', query, 'Must specify either userId or organizationId');
      }

      const account = await this.db.creditAccount.findFirst({
        where: {
          OR: [
            { userId: query.userId },
            { organizationId: query.organizationId }
          ]
        }
      });

      return account as CreditAccount | null;
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('getAccount', error as Error);
    }
  }

  /**
   * 获取或创建积分账户
   */
  async getOrCreateAccount(query: CreditBalanceQuery): Promise<CreditAccount> {
    let account = await this.getAccount(query);
    
    if (!account) {
      account = await this.createAccount({
        userId: query.userId,
        organizationId: query.organizationId,
        initialBalance: 0
      });
    }
    
    return account;
  }

  /**
   * 更新账户状态
   */
  async updateAccountStatus(
    accountId: string, 
    status: 'ACTIVE' | 'SUSPENDED' | 'CLOSED'
  ): Promise<CreditAccount> {
    try {
      if (!isValidAccountId(accountId)) {
        throw CreditErrors.validation('accountId', accountId, 'Invalid account ID format');
      }

      const account = await this.db.creditAccount.update({
        where: { id: accountId },
        data: { 
          status,
          updatedAt: new Date()
        }
      });

      return account as CreditAccount;
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('updateAccountStatus', error as Error);
    }
  }

  // ================================
  // 积分余额查询
  // ================================

  /**
   * 获取积分余额
   */
  async getBalance(query: CreditBalanceQuery): Promise<number> {
    const account = await this.getAccount(query);
    return account?.currentBalance || 0;
  }

  /**
   * 检查积分是否足够
   */
  async hasEnoughCredits(query: CreditBalanceQuery, requiredAmount: number): Promise<boolean> {
    if (!isValidCreditAmount(requiredAmount)) {
      return false;
    }

    const balance = await this.getBalance(query);
    return balance >= requiredAmount;
  }

  // ================================
  // 积分交易管理
  // ================================

  /**
   * 创建积分交易记录
   */
  async createTransaction(input: CreateCreditTransactionInput): Promise<CreditTransaction> {
    try {
      if (!isValidAccountId(input.accountId)) {
        throw CreditErrors.validation('accountId', input.accountId, 'Invalid account ID format');
      }

      if (!isValidCreditAmount(Math.abs(input.amount))) {
        throw CreditErrors.invalidAmount(input.amount, 'Amount must be a positive integer');
      }

      return await this.db.$transaction(async (tx) => {
        // 获取账户信息
        const account = await tx.creditAccount.findUnique({
          where: { id: input.accountId }
        });

        if (!account) {
          throw CreditErrors.accountNotFound(input.accountId);
        }

        if (account.status !== 'ACTIVE') {
          throw CreditErrors.accountSuspended(input.accountId);
        }

        // 计算新余额
        const balanceBefore = account.currentBalance;
        const balanceAfter = calculateNewBalance(balanceBefore, input.amount);

        // 如果是消耗操作，检查余额是否足够
        if (input.amount < 0 && balanceAfter < 0) {
          throw CreditErrors.insufficientBalance(
            input.accountId,
            Math.abs(input.amount),
            balanceBefore
          );
        }

        // 创建交易记录
        const transaction = await tx.creditTransaction.create({
          data: {
            accountId: input.accountId,
            type: input.type,
            amount: input.amount,
            balanceBefore,
            balanceAfter,
            reason: input.reason,
            description: input.description,
            metadata: input.metadata,
            featureId: input.featureId,
            purchaseId: input.purchaseId,
            referenceId: input.referenceId,
            expiresAt: input.expiresAt,
            userId: account.userId,
            organizationId: account.organizationId
          }
        });

        // 更新账户余额和统计
        const updateData: any = {
          currentBalance: balanceAfter,
          lastActivityAt: new Date(),
          updatedAt: new Date()
        };

        if (input.amount > 0) {
          updateData.totalEarned = account.totalEarned + input.amount;
        } else {
          updateData.totalSpent = account.totalSpent + Math.abs(input.amount);
        }

        await tx.creditAccount.update({
          where: { id: input.accountId },
          data: updateData
        });

        return transaction as CreditTransaction;
      });
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('createTransaction', error as Error);
    }
  }

  /**
   * 消耗积分
   */
  async consumeCredits(input: CreditConsumptionInput): Promise<CreditTransaction> {
    return this.createTransaction({
      accountId: input.accountId,
      type: 'SPENT_FEATURE',
      amount: -Math.abs(input.amount), // 确保是负数
      reason: input.reason,
      description: input.description,
      metadata: input.metadata,
      featureId: input.featureId
    });
  }

  /**
   * 增加积分
   */
  async addCredits(input: CreditAdditionInput): Promise<CreditTransaction> {
    return this.createTransaction({
      accountId: input.accountId,
      type: input.type,
      amount: Math.abs(input.amount), // 确保是正数
      reason: input.reason,
      description: input.description,
      metadata: input.metadata,
      expiresAt: input.expiresAt,
      purchaseId: input.purchaseId,
      referenceId: input.referenceId
    });
  }

  /**
   * 转移积分
   */
  async transferCredits(input: CreditTransferInput): Promise<{
    fromTransaction: CreditTransaction;
    toTransaction: CreditTransaction;
  }> {
    try {
      if (input.fromAccountId === input.toAccountId) {
        throw CreditErrors.transferToSameAccount(input.fromAccountId);
      }

      if (!isValidCreditAmount(input.amount)) {
        throw CreditErrors.invalidAmount(input.amount, 'Invalid transfer amount');
      }

      return await this.db.$transaction(async (tx) => {
        // 检查发送方账户
        const fromAccount = await tx.creditAccount.findUnique({
          where: { id: input.fromAccountId }
        });

        if (!fromAccount) {
          throw CreditErrors.accountNotFound(input.fromAccountId);
        }

        if (fromAccount.status !== 'ACTIVE') {
          throw CreditErrors.accountSuspended(input.fromAccountId);
        }

        if (fromAccount.currentBalance < input.amount) {
          throw CreditErrors.insufficientBalance(
            input.fromAccountId,
            input.amount,
            fromAccount.currentBalance
          );
        }

        // 检查接收方账户
        const toAccount = await tx.creditAccount.findUnique({
          where: { id: input.toAccountId }
        });

        if (!toAccount) {
          throw CreditErrors.accountNotFound(input.toAccountId);
        }

        if (toAccount.status !== 'ACTIVE') {
          throw CreditErrors.accountSuspended(input.toAccountId);
        }

        // 创建转出交易
        const fromTransaction = await this.createTransaction({
          accountId: input.fromAccountId,
          type: 'SPENT_TRANSFER',
          amount: -input.amount,
          reason: input.reason,
          description: input.description,
          metadata: { ...input.metadata, toAccountId: input.toAccountId }
        });

        // 创建转入交易
        const toTransaction = await this.createTransaction({
          accountId: input.toAccountId,
          type: 'RECEIVED_TRANSFER',
          amount: input.amount,
          reason: input.reason,
          description: input.description,
          metadata: { ...input.metadata, fromAccountId: input.fromAccountId }
        });

        return { fromTransaction, toTransaction };
      });
    } catch (error) {
      if (error instanceof CreditError) {
        throw error;
      }
      throw CreditErrors.database('transferCredits', error as Error);
    }
  }

  // ================================
  // 交易记录查询
  // ================================

  /**
   * 查询交易记录
   */
  async getTransactions(query: CreditTransactionQuery): Promise<PaginatedResponse<CreditTransaction>> {
    try {
      const offset = calculateOffset(query.page || 1, query.limit || 20);
      
      const where: any = {};
      
      if (query.accountId) where.accountId = query.accountId;
      if (query.userId) where.userId = query.userId;
      if (query.organizationId) where.organizationId = query.organizationId;
      if (query.type) where.type = query.type;
      if (query.featureId) where.featureId = query.featureId;
      
      if (query.startDate || query.endDate) {
        where.createdAt = {};
        if (query.startDate) where.createdAt.gte = query.startDate;
        if (query.endDate) where.createdAt.lte = query.endDate;
      }

      const [transactions, total] = await Promise.all([
        this.db.creditTransaction.findMany({
          where,
          orderBy: {
            createdAt: query.sortOrder || 'desc'
          },
          skip: offset,
          take: query.limit || 20
        }),
        this.db.creditTransaction.count({ where })
      ]);

      return createPaginatedResponse(
        transactions as CreditTransaction[],
        total,
        query.page || 1,
        query.limit || 20
      );
    } catch (error) {
      throw CreditErrors.database('getTransactions', error as Error);
    }
  }

  /**
   * 获取单个交易记录
   */
  async getTransaction(transactionId: string): Promise<CreditTransaction | null> {
    try {
      const transaction = await this.db.creditTransaction.findUnique({
        where: { id: transactionId }
      });

      return transaction as CreditTransaction | null;
    } catch (error) {
      throw CreditErrors.database('getTransaction', error as Error);
    }
  }
}
