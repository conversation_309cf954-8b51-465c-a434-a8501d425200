// Sentry 客户端配置
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  
  // 调整采样率以控制成本
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // 会话重放（可选，会增加成本）
  replaysSessionSampleRate: 0.01, // 1% 的会话
  replaysOnErrorSampleRate: 0.1,  // 10% 的错误会话
  
  integrations: [
    new Sentry.Replay({
      // 遮罩敏感信息
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],
  
  // 过滤敏感信息
  beforeSend(event) {
    // 移除 URL 中的敏感参数
    if (event.request?.url) {
      const url = new URL(event.request.url);
      url.searchParams.delete('token');
      url.searchParams.delete('key');
      event.request.url = url.toString();
    }
    return event;
  },
  
  // 忽略常见的非关键错误
  ignoreErrors: [
    'ResizeObserver loop limit exceeded',
    'Non-Error promise rejection captured',
    'ChunkLoadError',
    'Loading chunk',
    'Network request failed',
  ],
});
