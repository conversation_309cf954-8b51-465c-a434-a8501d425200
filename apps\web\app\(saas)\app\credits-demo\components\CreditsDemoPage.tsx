"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
    Tabs,
    Ta<PERSON>Content,
    TabsList,
    TabsTrigger,
} from "@ui/components/tabs";
import {
    <PERSON><PERSON><PERSON><PERSON>gle,
    BarChart3,
    CheckCircle,
    Coins,
    Settings
} from "lucide-react";
import { useState } from "react";

// 导入积分系统组件
import {
    CreditBalance,
    CreditDashboard,
    CreditGuard,
    CreditPackages,
    CreditTransactions,
    useCheckFeatureCredits,
    useConsumeCredits,
    useCreditBalance,
} from "../../../modules/saas/credits";

// 导入管理员面板

export function CreditsDemoPage() {
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const [demoResults, setDemoResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setDemoResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearResults = () => {
    setDemoResults([]);
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* 页面标题 */}
      <div className="text-center space-y-4">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
          <Coins className="w-8 h-8 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
            积分系统演示
          </h1>
          <p className="text-gray-600 mt-2">
            测试积分系统的各项功能和组件
          </p>
        </div>
      </div>

      {/* 系统状态检查 */}
      <SystemStatusCard />

      {/* 功能演示面板 */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            功能演示
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="dashboard" className="w-full">
            <TabsList className="grid w-full grid-cols-7">
              <TabsTrigger value="dashboard">仪表板</TabsTrigger>
              <TabsTrigger value="balance">余额显示</TabsTrigger>
              <TabsTrigger value="packages">积分包</TabsTrigger>
              <TabsTrigger value="transactions">交易记录</TabsTrigger>
              <TabsTrigger value="guard">访问控制</TabsTrigger>
              <TabsTrigger value="testing">功能测试</TabsTrigger>
              <TabsTrigger value="admin">管理员</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-4 mt-6">
              <DashboardDemo />
            </TabsContent>

            <TabsContent value="balance" className="space-y-4 mt-6">
              <BalanceDemo />
            </TabsContent>

            <TabsContent value="packages" className="space-y-4 mt-6">
              <PackagesDemo />
            </TabsContent>

            <TabsContent value="transactions" className="space-y-4 mt-6">
              <TransactionsDemo />
            </TabsContent>

            <TabsContent value="guard" className="space-y-4 mt-6">
              <GuardDemo />
            </TabsContent>

            <TabsContent value="testing" className="space-y-4 mt-6">
              <FunctionalTestingPanel
                onResult={addResult}
                results={demoResults}
                onClear={clearResults}
              />
            </TabsContent>

            <TabsContent value="admin" className="space-y-4 mt-6">
              <AdminPanel onResult={addResult} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 系统状态检查卡片
 */
function SystemStatusCard() {
  const { balance, isLoading, error, hasAccount } = useCreditBalance();

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          系统状态
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-2">
            {hasAccount ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            )}
            <span className="text-sm">
              积分账户: {hasAccount ? "已创建" : "未创建"}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {!isLoading ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            )}
            <span className="text-sm">
              API连接: {!isLoading ? "正常" : "加载中"}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {!error ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm">
              服务状态: {!error ? "正常" : "异常"}
            </span>
          </div>
        </div>
        
        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">错误信息: {error.message}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * 仪表板演示
 */
function DashboardDemo() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">积分仪表板 - 完整模式</h3>
        <CreditDashboard variant="full" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-4">积分仪表板 - 紧凑模式</h3>
        <CreditDashboard variant="compact" />
      </div>
    </div>
  );
}

/**
 * 余额显示演示
 */
function BalanceDemo() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <h4 className="font-medium mb-2">默认模式</h4>
          <CreditBalance variant="default" />
        </div>
        
        <div>
          <h4 className="font-medium mb-2">紧凑模式</h4>
          <CreditBalance variant="compact" />
        </div>
        
        <div>
          <h4 className="font-medium mb-2">详细模式</h4>
          <CreditBalance variant="detailed" showDetails={true} />
        </div>
      </div>
    </div>
  );
}

/**
 * 积分包演示
 */
function PackagesDemo() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">积分包购买界面</h3>
        <CreditPackages 
          variant="grid"
          showDescription={true}
          onPurchaseSuccess={() => {
            alert("积分包购买成功！（演示模式）");
          }}
        />
      </div>
    </div>
  );
}

/**
 * 交易记录演示
 */
function TransactionsDemo() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">积分交易记录</h3>
        <CreditTransactions 
          pageSize={10}
          showFilters={true}
        />
      </div>
    </div>
  );
}

/**
 * 访问控制演示
 */
function GuardDemo() {
  const [showProtectedContent, setShowProtectedContent] = useState(false);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">积分访问控制</h3>
        
        <div className="space-y-4">
          <Button 
            onClick={() => setShowProtectedContent(!showProtectedContent)}
            variant="outline"
          >
            {showProtectedContent ? "隐藏" : "显示"}受保护内容
          </Button>
          
          {showProtectedContent && (
            <CreditGuard
              requiredCredits={50}
              featureName="AI内容生成"
              featureDescription="使用AI生成旅行日记内容，需要消耗50积分"
              showPurchaseOptions={true}
            >
              <Card className="border-green-200 bg-green-50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-800">功能已解锁</span>
                  </div>
                  <p className="text-green-700">
                    恭喜！您有足够的积分使用AI内容生成功能。
                  </p>
                </CardContent>
              </Card>
            </CreditGuard>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * 功能测试面板
 */
interface FunctionalTestingPanelProps {
  onResult: (message: string) => void;
  results: string[];
  onClear: () => void;
}

function FunctionalTestingPanel({ onResult, results, onClear }: FunctionalTestingPanelProps) {
  const { checkCredits } = useCheckFeatureCredits();
  const consumeCredits = useConsumeCredits();

  const testCreditCheck = () => {
    const result = checkCredits(10);
    onResult(`积分检查 (10积分): ${result.canUse ? "通过" : "失败"} - ${result.reason || "无原因"}`);
  };

  const testCreditConsumption = async () => {
    try {
      onResult("开始测试积分消耗...");
      await consumeCredits.mutateAsync({
        amount: 5,
        featureType: "AI_GENERATION",
        description: "演示测试消耗",
      });
      onResult("积分消耗测试成功 - 消耗5积分");
    } catch (error) {
      onResult(`积分消耗测试失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">功能测试</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Button onClick={testCreditCheck} variant="outline">
            测试积分检查
          </Button>
          
          <Button 
            onClick={testCreditConsumption}
            variant="outline"
            disabled={consumeCredits.isPending}
          >
            {consumeCredits.isPending ? "测试中..." : "测试积分消耗"}
          </Button>
        </div>
      </div>

      {/* 测试结果 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>测试结果</CardTitle>
          <Button onClick={onClear} variant="outline" size="sm">
            清空
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {results.length === 0 ? (
              <p className="text-gray-500 text-sm">暂无测试结果</p>
            ) : (
              results.map((result, index) => (
                <div key={index} className="text-sm font-mono bg-gray-50 p-2 rounded">
                  {result}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
