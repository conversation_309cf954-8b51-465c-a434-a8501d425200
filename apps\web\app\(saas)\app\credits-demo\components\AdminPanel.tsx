"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Badge } from "@ui/components/badge";
import { 
  Settings, 
  Package, 
  Users, 
  BarChart3,
  RefreshCw,
  Save,
  Plus,
  Trash2
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@ui/components/tabs";

interface AdminPanelProps {
  onResult: (message: string) => void;
}

export function AdminPanel({ onResult }: AdminPanelProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6 text-primary" />
        <h2 className="text-2xl font-bold">积分系统管理</h2>
        <Badge status="warning">管理员</Badge>
      </div>

      <Tabs defaultValue="packages" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="packages">积分包管理</TabsTrigger>
          <TabsTrigger value="config">系统配置</TabsTrigger>
          <TabsTrigger value="users">用户管理</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="packages" className="space-y-4 mt-6">
          <PackageManagement onResult={onResult} />
        </TabsContent>

        <TabsContent value="config" className="space-y-4 mt-6">
          <SystemConfiguration onResult={onResult} />
        </TabsContent>

        <TabsContent value="users" className="space-y-4 mt-6">
          <UserManagement onResult={onResult} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4 mt-6">
          <Analytics onResult={onResult} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

/**
 * 积分包管理
 */
function PackageManagement({ onResult }: { onResult: (message: string) => void }) {
  const [packages, setPackages] = useState([
    {
      id: "starter",
      name: "入门包",
      creditAmount: 100,
      price: 9.99,
      status: "ACTIVE",
      description: "适合新用户的入门积分包"
    },
    {
      id: "standard",
      name: "标准包", 
      creditAmount: 500,
      price: 39.99,
      status: "ACTIVE",
      description: "最受欢迎的积分包选择"
    },
    {
      id: "premium",
      name: "高级包",
      creditAmount: 1000,
      price: 69.99,
      status: "ACTIVE", 
      description: "大量积分，适合重度用户"
    }
  ]);

  const [newPackage, setNewPackage] = useState({
    name: "",
    creditAmount: 0,
    price: 0,
    description: ""
  });

  const addPackage = () => {
    if (!newPackage.name || newPackage.creditAmount <= 0 || newPackage.price <= 0) {
      onResult("请填写完整的积分包信息");
      return;
    }

    const pkg = {
      id: `package-${Date.now()}`,
      ...newPackage,
      status: "ACTIVE" as const
    };

    setPackages(prev => [...prev, pkg]);
    setNewPackage({ name: "", creditAmount: 0, price: 0, description: "" });
    onResult(`成功添加积分包: ${pkg.name}`);
  };

  const removePackage = (id: string) => {
    setPackages(prev => prev.filter(pkg => pkg.id !== id));
    onResult(`成功删除积分包`);
  };

  const togglePackageStatus = (id: string) => {
    setPackages(prev => prev.map(pkg => 
      pkg.id === id 
        ? { ...pkg, status: pkg.status === "ACTIVE" ? "INACTIVE" : "ACTIVE" as const }
        : pkg
    ));
    onResult(`积分包状态已更新`);
  };

  return (
    <div className="space-y-6">
      {/* 现有积分包 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            现有积分包
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {packages.map((pkg) => (
              <div key={pkg.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium">{pkg.name}</h4>
                    <Badge status={pkg.status === "ACTIVE" ? "success" : "error"}>
                      {pkg.status === "ACTIVE" ? "启用" : "禁用"}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{pkg.description}</p>
                  <div className="flex items-center gap-4 text-sm">
                    <span>积分: {pkg.creditAmount.toLocaleString()}</span>
                    <span>价格: ¥{pkg.price.toFixed(2)}</span>
                    <span>单价: ¥{(pkg.price / pkg.creditAmount).toFixed(4)}/积分</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => togglePackageStatus(pkg.id)}
                  >
                    {pkg.status === "ACTIVE" ? "禁用" : "启用"}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => removePackage(pkg.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 添加新积分包 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            添加新积分包
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="package-name">包名称</Label>
              <Input
                id="package-name"
                value={newPackage.name}
                onChange={(e) => setNewPackage(prev => ({ ...prev, name: e.target.value }))}
                placeholder="例如: 超值包"
              />
            </div>
            
            <div>
              <Label htmlFor="package-credits">积分数量</Label>
              <Input
                id="package-credits"
                type="number"
                value={newPackage.creditAmount || ""}
                onChange={(e) => setNewPackage(prev => ({ ...prev, creditAmount: parseInt(e.target.value) || 0 }))}
                placeholder="例如: 1000"
              />
            </div>
            
            <div>
              <Label htmlFor="package-price">价格 (¥)</Label>
              <Input
                id="package-price"
                type="number"
                step="0.01"
                value={newPackage.price || ""}
                onChange={(e) => setNewPackage(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                placeholder="例如: 99.99"
              />
            </div>
            
            <div>
              <Label htmlFor="package-description">描述</Label>
              <Input
                id="package-description"
                value={newPackage.description}
                onChange={(e) => setNewPackage(prev => ({ ...prev, description: e.target.value }))}
                placeholder="积分包描述"
              />
            </div>
          </div>
          
          <div className="mt-4">
            <Button onClick={addPackage} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              添加积分包
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 系统配置
 */
function SystemConfiguration({ onResult }: { onResult: (message: string) => void }) {
  const [config, setConfig] = useState({
    defaultCreditAllocation: 50,
    maxCreditBalance: 10000,
    creditExpirationDays: 365,
    featureConsumption: {
      AI_GENERATION: 10,
      STORAGE_UPLOAD: 1,
      MAP_GEOCODING: 2,
      API_CALL: 1
    }
  });

  const saveConfig = () => {
    onResult("系统配置已保存（演示模式）");
  };

  const resetConfig = () => {
    setConfig({
      defaultCreditAllocation: 50,
      maxCreditBalance: 10000,
      creditExpirationDays: 365,
      featureConsumption: {
        AI_GENERATION: 10,
        STORAGE_UPLOAD: 1,
        MAP_GEOCODING: 2,
        API_CALL: 1
      }
    });
    onResult("系统配置已重置");
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>基础配置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="default-credits">默认积分分配</Label>
              <Input
                id="default-credits"
                type="number"
                value={config.defaultCreditAllocation}
                onChange={(e) => setConfig(prev => ({ 
                  ...prev, 
                  defaultCreditAllocation: parseInt(e.target.value) || 0 
                }))}
              />
            </div>
            
            <div>
              <Label htmlFor="max-balance">最大积分余额</Label>
              <Input
                id="max-balance"
                type="number"
                value={config.maxCreditBalance}
                onChange={(e) => setConfig(prev => ({ 
                  ...prev, 
                  maxCreditBalance: parseInt(e.target.value) || 0 
                }))}
              />
            </div>
            
            <div>
              <Label htmlFor="expiration-days">积分过期天数</Label>
              <Input
                id="expiration-days"
                type="number"
                value={config.creditExpirationDays}
                onChange={(e) => setConfig(prev => ({ 
                  ...prev, 
                  creditExpirationDays: parseInt(e.target.value) || 0 
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>功能消耗配置</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(config.featureConsumption).map(([feature, cost]) => (
              <div key={feature}>
                <Label htmlFor={`feature-${feature}`}>
                  {feature.replace(/_/g, ' ')}
                </Label>
                <Input
                  id={`feature-${feature}`}
                  type="number"
                  value={cost}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    featureConsumption: {
                      ...prev.featureConsumption,
                      [feature]: parseInt(e.target.value) || 0
                    }
                  }))}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-2">
        <Button onClick={saveConfig} className="flex-1">
          <Save className="h-4 w-4 mr-2" />
          保存配置
        </Button>
        <Button onClick={resetConfig} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          重置
        </Button>
      </div>
    </div>
  );
}

/**
 * 用户管理
 */
function UserManagement({ onResult }: { onResult: (message: string) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          用户积分管理
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-4">用户积分管理功能（演示版本）</p>
        <div className="space-y-2">
          <Button variant="outline" onClick={() => onResult("查询用户积分余额（演示）")}>
            查询用户积分
          </Button>
          <Button variant="outline" onClick={() => onResult("手动调整用户积分（演示）")}>
            调整用户积分
          </Button>
          <Button variant="outline" onClick={() => onResult("批量积分操作（演示）")}>
            批量操作
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 数据分析
 */
function Analytics({ onResult }: { onResult: (message: string) => void }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          数据分析
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-4">积分系统数据分析（演示版本）</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800">总积分发放</h4>
            <p className="text-2xl font-bold text-blue-600">1,234,567</p>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-800">总积分消耗</h4>
            <p className="text-2xl font-bold text-green-600">987,654</p>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <h4 className="font-medium text-purple-800">活跃用户</h4>
            <p className="text-2xl font-bold text-purple-600">2,345</p>
          </div>
          <div className="p-4 bg-orange-50 rounded-lg">
            <h4 className="font-medium text-orange-800">积分包销售</h4>
            <p className="text-2xl font-bold text-orange-600">¥45,678</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
