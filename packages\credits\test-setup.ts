import { vi } from 'vitest';

// Mock Prisma Client
vi.mock('@repo/database', () => {
  const mockPrismaClient = {
    creditAccount: {
      create: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      updateMany: vi.fn()
    },
    creditTransaction: {
      create: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    creditAllocation: {
      create: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      updateMany: vi.fn()
    },
    creditPackage: {
      create: vi.fn(),
      findFirst: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
      count: vi.fn(),
      update: vi.fn(),
      delete: vi.fn()
    },
    $transaction: vi.fn()
  };

  return {
    PrismaClient: vi.fn(() => mockPrismaClient),
    mockPrismaClient
  };
});

// Global test utilities
global.createMockDate = (dateString: string) => new Date(dateString);

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
