import { describe, it, expect } from 'vitest';
import {
  calculateAvailableBalance,
  hasEnoughCredits,
  calculateNewBalance,
  formatCreditAmount,
  formatTransactionType,
  isEarnedTransaction,
  isSpentTransaction,
  isExpiringSoon,
  getDaysUntilExpiration,
  formatExpirationDate,
  groupTransactionsByDate,
  calculateCreditAnalytics,
  calculateUsageTrend,
  createPaginatedResponse,
  calculateOffset,
  isValidAccountId,
  isValidCreditAmount,
  isValidDateRange,
  sanitizeMetadata
} from '../../src/utils/credit-helpers';
import { TestFactories, TestUtils } from '../test-factories';
import type { CreditTransactionType } from '../../src/types/credit-types';

describe('credit-helpers', () => {
  describe('calculateAvailableBalance', () => {
    it('should calculate balance excluding expired credits', () => {
      const account = TestFactories.createCreditAccount({ currentBalance: 1000 });
      const transactions = [
        TestFactories.createCreditTransaction({ amount: 500, expiresAt: null }),
        TestFactories.createExpiredTransaction({ amount: 200 }),
        TestFactories.createCreditTransaction({ 
          amount: 300, 
          expiresAt: TestUtils.createFutureDate(30) 
        })
      ];

      const result = calculateAvailableBalance(account, transactions);

      expect(result).toBe(800); // 500 + 300, excluding expired 200
    });

    it('should include credits without expiration date', () => {
      const account = TestFactories.createCreditAccount();
      const transactions = [
        TestFactories.createCreditTransaction({ amount: 100, expiresAt: null }),
        TestFactories.createCreditTransaction({ amount: 200, expiresAt: null })
      ];

      const result = calculateAvailableBalance(account, transactions);

      expect(result).toBe(300);
    });

    it('should exclude credits marked as expired', () => {
      const account = TestFactories.createCreditAccount();
      const transactions = [
        TestFactories.createCreditTransaction({ 
          amount: 100, 
          expiresAt: TestUtils.createFutureDate(30),
          expiredAt: new Date() // Marked as expired
        })
      ];

      const result = calculateAvailableBalance(account, transactions);

      expect(result).toBe(0);
    });
  });

  describe('hasEnoughCredits', () => {
    it('should return true when account has enough credits', () => {
      const account = TestFactories.createCreditAccount({ currentBalance: 500 });
      
      const result = hasEnoughCredits(account, 300);

      expect(result).toBe(true);
    });

    it('should return false when account has insufficient credits', () => {
      const account = TestFactories.createCreditAccount({ currentBalance: 100 });
      
      const result = hasEnoughCredits(account, 300);

      expect(result).toBe(false);
    });

    it('should use transaction-based calculation when provided', () => {
      const account = TestFactories.createCreditAccount({ currentBalance: 1000 });
      const transactions = [
        TestFactories.createCreditTransaction({ amount: 200, expiresAt: null })
      ];
      
      const result = hasEnoughCredits(account, 150, transactions);

      expect(result).toBe(true);
    });
  });

  describe('calculateNewBalance', () => {
    it('should add positive amount to balance', () => {
      const result = calculateNewBalance(100, 50);
      expect(result).toBe(150);
    });

    it('should subtract negative amount from balance', () => {
      const result = calculateNewBalance(100, -30);
      expect(result).toBe(70);
    });

    it('should not allow negative balance', () => {
      const result = calculateNewBalance(50, -100);
      expect(result).toBe(0);
    });
  });

  describe('formatCreditAmount', () => {
    it('should format small amounts as is', () => {
      expect(formatCreditAmount(0)).toBe('0');
      expect(formatCreditAmount(123)).toBe('123');
      expect(formatCreditAmount(999)).toBe('999');
    });

    it('should format thousands with K suffix', () => {
      expect(formatCreditAmount(1000)).toBe('1.0K');
      expect(formatCreditAmount(1500)).toBe('1.5K');
      expect(formatCreditAmount(999999)).toBe('1000.0K');
    });

    it('should format millions with M suffix', () => {
      expect(formatCreditAmount(1000000)).toBe('1.0M');
      expect(formatCreditAmount(2500000)).toBe('2.5M');
    });
  });

  describe('formatTransactionType', () => {
    it('should format all transaction types correctly', () => {
      const types: Record<CreditTransactionType, string> = {
        EARNED_SUBSCRIPTION: '订阅分配',
        EARNED_PURCHASE: '购买获得',
        EARNED_BONUS: '奖励获得',
        EARNED_REFERRAL: '推荐奖励',
        EARNED_SIGNIN: '签到奖励',
        SPENT_FEATURE: '功能消耗',
        SPENT_TRANSFER: '转账支出',
        RECEIVED_TRANSFER: '转账收入',
        EXPIRED: '积分过期',
        REFUNDED: '退款返还',
        ADJUSTMENT: '管理员调整'
      };

      Object.entries(types).forEach(([type, expected]) => {
        expect(formatTransactionType(type as CreditTransactionType)).toBe(expected);
      });
    });

    it('should return original type for unknown types', () => {
      expect(formatTransactionType('UNKNOWN_TYPE' as any)).toBe('UNKNOWN_TYPE');
    });
  });

  describe('isEarnedTransaction', () => {
    it('should identify earned transaction types', () => {
      expect(isEarnedTransaction('EARNED_SUBSCRIPTION')).toBe(true);
      expect(isEarnedTransaction('EARNED_PURCHASE')).toBe(true);
      expect(isEarnedTransaction('RECEIVED_TRANSFER')).toBe(true);
      expect(isEarnedTransaction('REFUNDED')).toBe(true);
    });

    it('should reject non-earned transaction types', () => {
      expect(isEarnedTransaction('SPENT_FEATURE')).toBe(false);
      expect(isEarnedTransaction('EXPIRED')).toBe(false);
    });
  });

  describe('isSpentTransaction', () => {
    it('should identify spent transaction types', () => {
      expect(isSpentTransaction('SPENT_FEATURE')).toBe(true);
      expect(isSpentTransaction('SPENT_TRANSFER')).toBe(true);
      expect(isSpentTransaction('EXPIRED')).toBe(true);
    });

    it('should reject non-spent transaction types', () => {
      expect(isSpentTransaction('EARNED_PURCHASE')).toBe(false);
      expect(isSpentTransaction('RECEIVED_TRANSFER')).toBe(false);
    });
  });

  describe('isExpiringSoon', () => {
    it('should return true for credits expiring within warning period', () => {
      const expiresAt = TestUtils.createFutureDate(5); // 5 days from now
      
      const result = isExpiringSoon(expiresAt, 7);

      expect(result).toBe(true);
    });

    it('should return false for credits expiring beyond warning period', () => {
      const expiresAt = TestUtils.createFutureDate(10); // 10 days from now
      
      const result = isExpiringSoon(expiresAt, 7);

      expect(result).toBe(false);
    });

    it('should return false for null expiration date', () => {
      const result = isExpiringSoon(null, 7);

      expect(result).toBe(false);
    });

    it('should return false for already expired credits', () => {
      const expiresAt = TestUtils.createPastDate(1); // 1 day ago
      
      const result = isExpiringSoon(expiresAt, 7);

      expect(result).toBe(false);
    });
  });

  describe('getDaysUntilExpiration', () => {
    it('should calculate days until expiration correctly', () => {
      const expiresAt = TestUtils.createFutureDate(10);
      
      const result = getDaysUntilExpiration(expiresAt);

      expect(result).toBe(10);
    });

    it('should return null for null expiration date', () => {
      const result = getDaysUntilExpiration(null);

      expect(result).toBeNull();
    });

    it('should return 0 for already expired credits', () => {
      const expiresAt = TestUtils.createPastDate(1);
      
      const result = getDaysUntilExpiration(expiresAt);

      expect(result).toBe(0);
    });
  });

  describe('formatExpirationDate', () => {
    it('should format null expiration as never expire', () => {
      const result = formatExpirationDate(null);

      expect(result).toBe('永不过期');
    });

    it('should format today expiration', () => {
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today
      
      const result = formatExpirationDate(today);

      expect(result).toBe('今天过期');
    });

    it('should format tomorrow expiration', () => {
      const tomorrow = TestUtils.createFutureDate(1);
      
      const result = formatExpirationDate(tomorrow);

      expect(result).toBe('明天过期');
    });

    it('should format days for short term expiration', () => {
      const future = TestUtils.createFutureDate(5);
      
      const result = formatExpirationDate(future);

      expect(result).toBe('5天后过期');
    });
  });

  describe('createPaginatedResponse', () => {
    it('should create paginated response correctly', () => {
      const items = [1, 2, 3, 4, 5];
      const total = 50;
      const page = 2;
      const limit = 10;

      const result = createPaginatedResponse(items, total, page, limit);

      expect(result).toEqual({
        items,
        total,
        page,
        limit,
        totalPages: 5
      });
    });
  });

  describe('calculateOffset', () => {
    it('should calculate offset correctly', () => {
      expect(calculateOffset(1, 10)).toBe(0);
      expect(calculateOffset(2, 10)).toBe(10);
      expect(calculateOffset(3, 20)).toBe(40);
    });
  });

  describe('validation functions', () => {
    describe('isValidAccountId', () => {
      it('should validate account ID format', () => {
        expect(isValidAccountId('account-123')).toBe(true);
        expect(isValidAccountId('a')).toBe(true);
        expect(isValidAccountId('')).toBe(false);
        expect(isValidAccountId('a'.repeat(51))).toBe(false);
      });
    });

    describe('isValidCreditAmount', () => {
      it('should validate credit amounts', () => {
        expect(isValidCreditAmount(100)).toBe(true);
        expect(isValidCreditAmount(1)).toBe(true);
        expect(isValidCreditAmount(1000000)).toBe(true);
        expect(isValidCreditAmount(0)).toBe(false);
        expect(isValidCreditAmount(-100)).toBe(false);
        expect(isValidCreditAmount(1.5)).toBe(false);
        expect(isValidCreditAmount(1000001)).toBe(false);
      });
    });

    describe('isValidDateRange', () => {
      it('should validate date ranges', () => {
        const past = TestUtils.createPastDate(10);
        const recent = TestUtils.createPastDate(5);
        const future = TestUtils.createFutureDate(5);

        expect(isValidDateRange(past, recent)).toBe(true);
        expect(isValidDateRange(recent, past)).toBe(false);
        expect(isValidDateRange(past, future)).toBe(false);
      });
    });

    describe('sanitizeMetadata', () => {
      it('should keep only basic types', () => {
        const input = {
          string: 'test',
          number: 123,
          boolean: true,
          null: null,
          object: { nested: 'value' },
          array: [1, 2, 3],
          function: () => {},
          undefined: undefined
        };

        const result = sanitizeMetadata(input);

        expect(result).toEqual({
          string: 'test',
          number: 123,
          boolean: true,
          null: null
        });
      });
    });
  });
});
