#!/usr/bin/env node

/**
 * 积分系统测试运行脚本
 * 
 * 使用方法:
 * node run-tests.js [options]
 * 
 * 选项:
 * --coverage    生成覆盖率报告
 * --watch       监听模式
 * --services    只运行服务层测试
 * --utils       只运行工具函数测试
 * --integration 只运行集成测试
 * --ui          启动测试UI界面
 */

const { spawn } = require('child_process');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
const options = {
  coverage: args.includes('--coverage'),
  watch: args.includes('--watch'),
  services: args.includes('--services'),
  utils: args.includes('--utils'),
  integration: args.includes('--integration'),
  ui: args.includes('--ui')
};

// 构建测试命令
function buildTestCommand() {
  let command = 'vitest';
  
  if (options.ui) {
    command += ' --ui';
  } else if (options.watch) {
    command += ' --watch';
  } else {
    command += ' run';
  }
  
  if (options.coverage) {
    command += ' --coverage';
  }
  
  // 指定测试目录
  if (options.services) {
    command += ' __tests__/services';
  } else if (options.utils) {
    command += ' __tests__/utils';
  } else if (options.integration) {
    command += ' __tests__/integration';
  }
  
  return command;
}

// 运行测试
function runTests() {
  const command = buildTestCommand();
  console.log(`🧪 运行积分系统测试: ${command}\n`);
  
  const child = spawn('npx', command.split(' '), {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });
  
  child.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ 所有测试通过!');
    } else {
      console.log(`\n❌ 测试失败，退出码: ${code}`);
      process.exit(code);
    }
  });
  
  child.on('error', (error) => {
    console.error('❌ 测试运行出错:', error);
    process.exit(1);
  });
}

// 显示帮助信息
function showHelp() {
  console.log(`
积分系统测试运行器

使用方法:
  node run-tests.js [选项]

选项:
  --coverage     生成测试覆盖率报告
  --watch        监听文件变化，自动重新运行测试
  --services     只运行服务层测试 (CreditService, CreditAllocationService, CreditPackageService)
  --utils        只运行工具函数测试 (credit-helpers, credit-config, credit-errors)
  --integration  只运行集成测试
  --ui           启动可视化测试界面
  --help         显示此帮助信息

示例:
  node run-tests.js                    # 运行所有测试
  node run-tests.js --coverage         # 运行所有测试并生成覆盖率报告
  node run-tests.js --services --watch # 监听模式运行服务层测试
  node run-tests.js --integration      # 只运行集成测试
  node run-tests.js --ui               # 启动测试UI界面

测试文件结构:
  __tests__/
  ├── services/           # 服务层测试
  │   ├── CreditService.test.ts
  │   ├── CreditAllocationService.test.ts
  │   └── CreditPackageService.test.ts
  ├── utils/              # 工具函数测试
  │   ├── credit-helpers.test.ts
  │   └── credit-config.test.ts
  ├── types/              # 类型测试
  │   └── credit-errors.test.ts
  └── integration/        # 集成测试
      └── credit-system.integration.test.ts
`);
}

// 主函数
function main() {
  if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  console.log('🚀 积分系统测试套件');
  console.log('='.repeat(50));
  
  if (options.coverage) {
    console.log('📊 将生成测试覆盖率报告');
  }
  
  if (options.watch) {
    console.log('👀 监听模式已启用');
  }
  
  if (options.services) {
    console.log('🔧 只运行服务层测试');
  } else if (options.utils) {
    console.log('🛠️  只运行工具函数测试');
  } else if (options.integration) {
    console.log('🔗 只运行集成测试');
  } else {
    console.log('🧪 运行所有测试');
  }
  
  console.log('='.repeat(50));
  
  runTests();
}

// 运行主函数
main();
