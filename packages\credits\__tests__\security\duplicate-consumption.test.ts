import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';
import { CreditError } from '../../src/types/credit-errors';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    update: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn(),
  },
  $transaction: vi.fn(),
} as any;

// Mock Redis for idempotency tracking
const mockRedisClient = {
  get: vi.fn(),
  set: vi.fn(),
  setex: vi.fn(),
  del: vi.fn(),
  exists: vi.fn(),
  incr: vi.fn(),
  expire: vi.fn(),
} as any;

describe('Security: Duplicate Consumption Protection', () => {
  let creditService: CreditService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient, mockRedisClient);
  });

  describe('Idempotency Verification', () => {
    it('should prevent duplicate transactions with same idempotency key', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });
      const idempotencyKey = 'idem-key-123';
      const existingTransaction = TestFactories.createCreditTransaction({
        amount: -100,
        type: 'CONSUMED_FEATURE',
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // First request - should succeed
      mockRedisClient.get.mockResolvedValueOnce(null);
      mockPrismaClient.creditTransaction.create.mockResolvedValueOnce(existingTransaction);
      mockRedisClient.setex.mockResolvedValueOnce('OK');

      const firstResult = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage',
        idempotencyKey,
      });

      expect(firstResult).toEqual(existingTransaction);

      // Second request with same key - should return cached result
      mockRedisClient.get.mockResolvedValueOnce(JSON.stringify(existingTransaction));

      const secondResult = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage',
        idempotencyKey,
      });

      expect(secondResult).toEqual(existingTransaction);
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(1);
    });

    it('should generate unique idempotency keys when not provided', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      mockRedisClient.get.mockResolvedValue(null);
      mockRedisClient.setex.mockResolvedValue('OK');

      const result1 = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage 1',
      });

      const result2 = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage 2',
      });

      // Both should succeed as they have different auto-generated keys
      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(2);
    });

    it('should handle idempotency key expiration', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();
      const idempotencyKey = 'expired-key-123';

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      
      // Key has expired (returns null)
      mockRedisClient.get.mockResolvedValue(null);
      mockRedisClient.setex.mockResolvedValue('OK');

      const result = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage after expiration',
        idempotencyKey,
      });

      expect(result).toEqual(mockTransaction);
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(1);
    });

    it('should validate idempotency key format', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const invalidKey = 'invalid key with spaces and special chars!@#';

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Test',
          idempotencyKey: invalidKey,
        });
      }).rejects.toThrow('Invalid idempotency key format');
    });
  });

  describe('Duplicate Request Detection', () => {
    it('should detect rapid duplicate requests', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const requestFingerprint = 'account-123:100:feature-usage';

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock rapid requests detection
      mockRedisClient.get.mockResolvedValue('1'); // Previous request count
      mockRedisClient.incr.mockResolvedValue(2); // Incremented count

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          requestFingerprint,
        });
      }).rejects.toThrow('Duplicate request detected within time window');
    });

    it('should allow requests after time window expires', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();
      const requestFingerprint = 'account-123:100:feature-usage';

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      
      // No previous requests in time window
      mockRedisClient.get.mockResolvedValue(null);
      mockRedisClient.incr.mockResolvedValue(1);
      mockRedisClient.expire.mockResolvedValue(1);

      const result = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage',
        requestFingerprint,
      });

      expect(result).toEqual(mockTransaction);
    });

    it('should create unique fingerprints for different requests', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      const fingerprint1 = creditService.generateRequestFingerprint({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature A',
        timestamp: new Date('2024-01-01T10:00:00Z'),
      });

      const fingerprint2 = creditService.generateRequestFingerprint({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature B', // Different reason
        timestamp: new Date('2024-01-01T10:00:00Z'),
      });

      const fingerprint3 = creditService.generateRequestFingerprint({
        accountId: mockAccount.id,
        amount: 200, // Different amount
        reason: 'Feature A',
        timestamp: new Date('2024-01-01T10:00:00Z'),
      });

      expect(fingerprint1).not.toBe(fingerprint2);
      expect(fingerprint1).not.toBe(fingerprint3);
      expect(fingerprint2).not.toBe(fingerprint3);
    });
  });

  describe('Transaction Uniqueness Enforcement', () => {
    it('should enforce unique transaction IDs', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const transactionId = 'tx-123';

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock existing transaction with same ID
      mockPrismaClient.creditTransaction.findFirst.mockResolvedValue(
        TestFactories.createCreditTransaction({ id: transactionId })
      );

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          transactionId,
        });
      }).rejects.toThrow('Transaction ID already exists');
    });

    it('should generate unique transaction IDs when not provided', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction1 = TestFactories.createCreditTransaction({ id: 'tx-1' });
      const mockTransaction2 = TestFactories.createCreditTransaction({ id: 'tx-2' });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditTransaction.create
        .mockResolvedValueOnce(mockTransaction1)
        .mockResolvedValueOnce(mockTransaction2);

      const result1 = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage 1',
      });

      const result2 = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage 2',
      });

      expect(result1.id).not.toBe(result2.id);
    });

    it('should validate transaction ID format', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const invalidTransactionId = 'invalid-id-with-special-chars!@#$%';

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Test',
          transactionId: invalidTransactionId,
        });
      }).rejects.toThrow('Invalid transaction ID format');
    });
  });

  describe('Time Window Control', () => {
    it('should enforce minimum time between similar operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const operationKey = `${mockAccount.id}:consume:feature-usage`;

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock recent operation
      const recentTimestamp = Date.now() - 1000; // 1 second ago
      mockRedisClient.get.mockResolvedValue(recentTimestamp.toString());

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
        });
      }).rejects.toThrow('Operation too frequent, please wait before retrying');
    });

    it('should allow operations after minimum time window', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();
      const operationKey = `${mockAccount.id}:consume:feature-usage`;

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      
      // Mock old operation (beyond minimum time window)
      const oldTimestamp = Date.now() - 60000; // 1 minute ago
      mockRedisClient.get.mockResolvedValue(oldTimestamp.toString());
      mockRedisClient.setex.mockResolvedValue('OK');

      const result = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage',
      });

      expect(result).toEqual(mockTransaction);
    });

    it('should have different time windows for different operation types', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      // Consumption operations should have shorter time window
      const consumeWindow = creditService.getOperationTimeWindow('CONSUME');
      
      // Purchase operations should have longer time window
      const purchaseWindow = creditService.getOperationTimeWindow('PURCHASE');
      
      // Transfer operations should have medium time window
      const transferWindow = creditService.getOperationTimeWindow('TRANSFER');

      expect(consumeWindow).toBeLessThan(purchaseWindow);
      expect(transferWindow).toBeLessThan(purchaseWindow);
      expect(transferWindow).toBeGreaterThan(consumeWindow);
    });
  });

  describe('Replay Attack Protection', () => {
    it('should detect and prevent replay attacks', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const requestSignature = 'signature-123';
      const timestamp = new Date();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock request signature already exists
      mockRedisClient.exists.mockResolvedValue(1);

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          requestSignature,
          timestamp,
        });
      }).rejects.toThrow('Replay attack detected');
    });

    it('should reject requests with old timestamps', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const oldTimestamp = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          timestamp: oldTimestamp,
        });
      }).rejects.toThrow('Request timestamp too old');
    });

    it('should reject requests with future timestamps', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const futureTimestamp = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes in future

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          timestamp: futureTimestamp,
        });
      }).rejects.toThrow('Request timestamp in future');
    });

    it('should validate request signature integrity', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const invalidSignature = 'tampered-signature';

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          requestSignature: invalidSignature,
        });
      }).rejects.toThrow('Invalid request signature');
    });
  });

  describe('Concurrent Request Handling', () => {
    it('should handle concurrent requests with same idempotency key', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();
      const idempotencyKey = 'concurrent-key-123';

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      
      // First request gets lock
      mockRedisClient.get
        .mockResolvedValueOnce(null) // No existing result
        .mockResolvedValueOnce(JSON.stringify(mockTransaction)); // Second request gets cached result
      
      mockRedisClient.setex.mockResolvedValue('OK');

      const promises = [
        creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Concurrent request 1',
          idempotencyKey,
        }),
        creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Concurrent request 2',
          idempotencyKey,
        }),
      ];

      const results = await Promise.all(promises);

      // Both should return the same result
      expect(results[0]).toEqual(mockTransaction);
      expect(results[1]).toEqual(mockTransaction);
      
      // But only one database write should occur
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(1);
    });

    it('should implement distributed locking for critical operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const lockKey = `lock:${mockAccount.id}:consume`;

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock lock acquisition failure
      mockRedisClient.set.mockResolvedValue(null); // Lock already held

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
        });
      }).rejects.toThrow('Unable to acquire operation lock, please retry');
    });

    it('should release locks after operation completion', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();
      const lockKey = `lock:${mockAccount.id}:consume`;

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      
      // Mock successful lock acquisition and release
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.del.mockResolvedValue(1);

      const result = await creditService.consumeCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Feature usage',
      });

      expect(result).toEqual(mockTransaction);
      expect(mockRedisClient.del).toHaveBeenCalledWith(lockKey);
    });
  });
});
