import { CreditAllocationService, CreditService } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import {
    allocationIdSchema,
    allocationQuerySchema,
    createAllocationSchema,
    creditAllocationResponseSchema,
    errorResponseSchema,
    paginatedResponseSchema,
    updateAllocationSchema,
} from "./schemas";

// 创建服务实例
const creditService = new CreditService(db);
const allocationService = new CreditAllocationService(db, creditService);

/**
 * 积分分配管理路由
 */
export const allocationRouter = new Hono()
  // 获取分配规则列表
  .get(
    "/",
    authMiddleware,
    validator("query", allocationQuerySchema),
    describeRoute({
      tags: ["Credits"],
      summary: "获取积分分配规则",
      description: "获取用户或组织的积分分配规则列表",
      responses: {
        200: {
          description: "分配规则列表",
          content: {
            "application/json": {
              schema: resolver(paginatedResponseSchema(creditAllocationResponseSchema)),
            },
          },
        },
        400: {
          description: "请求参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const query = c.req.valid("query");

        // 确定查询目标
        const targetUserId = query.userId || user.id;
        const targetOrgId = query.organizationId;

        // 权限检查
        if (query.userId && query.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权查看其他用户的分配规则" });
        }

        // 获取账户ID
        let accountId = query.accountId;
        if (!accountId) {
          const account = await creditService.getOrCreateAccount({
            userId: targetUserId,
            organizationId: targetOrgId,
          });
          accountId = account.id;
        }

        // 构建查询参数
        const queryParams = {
          accountId,
          status: query.status,
          subscriptionPlan: query.subscriptionPlan,
          page: query.page,
          limit: query.limit,
        };

        const result = await allocationService.getAllocations(queryParams);

        const response = {
          data: result.data.map(allocation => ({
            id: allocation.id,
            accountId: allocation.accountId,
            subscriptionPlan: allocation.subscriptionPlan,
            amount: allocation.amount,
            interval: allocation.interval,
            status: allocation.status,
            nextAllocationAt: allocation.nextAllocationAt?.toISOString() || null,
            lastAllocationAt: allocation.lastAllocationAt?.toISOString() || null,
            createdAt: allocation.createdAt.toISOString(),
            updatedAt: allocation.updatedAt.toISOString(),
          })),
          pagination: result.pagination,
        };

        logger.info("积分分配规则查询成功", {
          userId: targetUserId,
          organizationId: targetOrgId,
          accountId,
          resultCount: result.data.length,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分分配规则失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "获取分配规则失败" });
      }
    }
  )

  // 创建分配规则
  .post(
    "/",
    authMiddleware,
    validator("json", createAllocationSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "创建积分分配规则",
      description: "为用户或组织创建新的积分分配规则",
      responses: {
        201: {
          description: "分配规则创建成功",
          content: {
            "application/json": {
              schema: resolver(creditAllocationResponseSchema),
            },
          },
        },
        400: {
          description: "请求参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        409: {
          description: "分配规则已存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 确定目标账户
        const targetUserId = data.userId || user.id;
        const targetOrgId = data.organizationId;

        // 权限检查
        if (data.userId && data.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权为其他用户创建分配规则" });
        }

        // 获取账户ID
        let accountId = data.accountId;
        if (!accountId) {
          const account = await creditService.getOrCreateAccount({
            userId: targetUserId,
            organizationId: targetOrgId,
          });
          accountId = account.id;
        }

        const allocation = await allocationService.createAllocationFromSubscription(
          accountId,
          data.subscriptionPlan,
          {
            customAmount: data.customAmount,
            customInterval: data.customInterval,
            startDate: data.startDate ? new Date(data.startDate) : undefined,
            endDate: data.endDate ? new Date(data.endDate) : undefined,
          }
        );

        const response = {
          id: allocation.id,
          accountId: allocation.accountId,
          subscriptionPlan: allocation.subscriptionPlan,
          amount: allocation.amount,
          interval: allocation.interval,
          status: allocation.status,
          nextAllocationAt: allocation.nextAllocationAt?.toISOString() || null,
          lastAllocationAt: allocation.lastAllocationAt?.toISOString() || null,
          createdAt: allocation.createdAt.toISOString(),
          updatedAt: allocation.updatedAt.toISOString(),
        };

        logger.info("积分分配规则创建成功", {
          allocationId: allocation.id,
          accountId,
          subscriptionPlan: data.subscriptionPlan,
          amount: allocation.amount,
          interval: allocation.interval,
          userId: user.id,
        });

        return c.json(response, 201);
      } catch (error) {
        logger.error("创建积分分配规则失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是规则已存在的错误
        if (error instanceof Error && error.message.includes("already exists")) {
          throw new HTTPException(409, { message: "该订阅计划的分配规则已存在" });
        }

        throw new HTTPException(500, { message: "创建分配规则失败" });
      }
    }
  )

  // 更新分配规则
  .put(
    "/:id",
    authMiddleware,
    validator("param", { id: allocationIdSchema }),
    validator("json", updateAllocationSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "更新积分分配规则",
      description: "更新指定的积分分配规则",
      responses: {
        200: {
          description: "分配规则更新成功",
          content: {
            "application/json": {
              schema: resolver(creditAllocationResponseSchema),
            },
          },
        },
        404: {
          description: "分配规则不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");
        const data = c.req.valid("json");

        // 获取现有分配规则
        const existingAllocation = await allocationService.getAllocation(id);
        if (!existingAllocation) {
          throw new HTTPException(404, { message: "分配规则不存在" });
        }

        // 权限检查：验证用户是否有权更新此分配规则
        const account = await creditService.getAccountById(existingAllocation.accountId);
        if (!account) {
          throw new HTTPException(404, { message: "关联账户不存在" });
        }

        if (account.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权更新此分配规则" });
        }

        const updatedAllocation = await allocationService.updateAllocation(id, {
          status: data.status,
          customAmount: data.customAmount,
          customInterval: data.customInterval,
          endDate: data.endDate ? new Date(data.endDate) : undefined,
        });

        const response = {
          id: updatedAllocation.id,
          accountId: updatedAllocation.accountId,
          subscriptionPlan: updatedAllocation.subscriptionPlan,
          amount: updatedAllocation.amount,
          interval: updatedAllocation.interval,
          status: updatedAllocation.status,
          nextAllocationAt: updatedAllocation.nextAllocationAt?.toISOString() || null,
          lastAllocationAt: updatedAllocation.lastAllocationAt?.toISOString() || null,
          createdAt: updatedAllocation.createdAt.toISOString(),
          updatedAt: updatedAllocation.updatedAt.toISOString(),
        };

        logger.info("积分分配规则更新成功", {
          allocationId: id,
          accountId: account.id,
          changes: data,
          userId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("更新积分分配规则失败", {
          error: error instanceof Error ? error.message : String(error),
          allocationId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "更新分配规则失败" });
      }
    }
  )

  // 执行分配
  .post(
    "/:id/execute",
    authMiddleware,
    validator("param", { id: allocationIdSchema }),
    describeRoute({
      tags: ["Credits"],
      summary: "执行积分分配",
      description: "手动执行指定的积分分配规则",
      responses: {
        200: {
          description: "分配执行成功",
          content: {
            "application/json": {
              schema: resolver(z.object({
                success: z.boolean(),
                transaction: z.object({
                  id: z.string(),
                  amount: z.number(),
                  createdAt: z.string(),
                }).nullable(),
                message: z.string(),
              })),
            },
          },
        },
        404: {
          description: "分配规则不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const { id } = c.req.valid("param");

        // 获取分配规则
        const allocation = await allocationService.getAllocation(id);
        if (!allocation) {
          throw new HTTPException(404, { message: "分配规则不存在" });
        }

        // 权限检查
        const account = await creditService.getAccountById(allocation.accountId);
        if (!account) {
          throw new HTTPException(404, { message: "关联账户不存在" });
        }

        if (account.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权执行此分配规则" });
        }

        const result = await allocationService.executeAllocation(id);

        const response = {
          success: result.success,
          transaction: result.transaction ? {
            id: result.transaction.id,
            amount: result.transaction.amount,
            createdAt: result.transaction.createdAt.toISOString(),
          } : null,
          message: result.success ? "分配执行成功" : "分配执行失败",
        };

        logger.info("积分分配执行", {
          allocationId: id,
          accountId: allocation.accountId,
          success: result.success,
          transactionId: result.transaction?.id,
          userId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("执行积分分配失败", {
          error: error instanceof Error ? error.message : String(error),
          allocationId: c.req.param("id"),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "执行分配失败" });
      }
    }
  )

  // 批量执行到期分配
  .post(
    "/execute-batch",
    authMiddleware,
    describeRoute({
      tags: ["Credits"],
      summary: "批量执行到期分配",
      description: "批量执行所有到期的积分分配规则（仅管理员）",
      responses: {
        200: {
          description: "批量执行完成",
          content: {
            "application/json": {
              schema: resolver(z.object({
                processed: z.number(),
                successful: z.number(),
                failed: z.number(),
                errors: z.array(z.string()),
              })),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");

        // 只有管理员可以执行批量分配
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以执行批量分配" });
        }

        const result = await allocationService.executeAllDueAllocations();

        logger.info("批量积分分配执行完成", {
          processed: result.processed,
          successful: result.successful,
          failed: result.failed,
          adminUserId: user.id,
        });

        return c.json(result);
      } catch (error) {
        logger.error("批量执行积分分配失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "批量执行分配失败" });
      }
    }
  );
