'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, Zap, Database, Globe } from 'lucide-react';

// 导入监控函数
import { captureError } from '@repo/monitoring/sentry';
import { trackEvent, trackUserAction } from '@repo/monitoring/analytics';
import { logInfo, logError } from '@repo/monitoring/logger';
import { recordMetric } from '@repo/monitoring/metrics';

export default function MonitoringTestPage() {
  const [testResults, setTestResults] = useState<Array<{
    name: string;
    status: 'success' | 'error' | 'pending';
    message: string;
  }>>([]);

  const addTestResult = (name: string, status: 'success' | 'error', message: string) => {
    setTestResults(prev => [...prev, { name, status, message }]);
  };

  // 测试错误捕获
  const testErrorCapture = async () => {
    try {
      addTestResult('错误捕获', 'pending', '正在测试...');
      
      const testError = new Error('这是一个测试错误 - 用于验证监控系统');
      captureError(testError, {
        userId: 'test-user-123',
        requestId: 'test-request-456',
        context: 'monitoring-test',
      });
      
      addTestResult('错误捕获', 'success', 'Sentry 错误捕获测试成功');
    } catch (error) {
      addTestResult('错误捕获', 'error', `测试失败: ${(error as Error).message}`);
    }
  };

  // 测试分析追踪
  const testAnalytics = async () => {
    try {
      addTestResult('分析追踪', 'pending', '正在测试...');
      
      // 测试事件追踪
      trackEvent('monitoring_test', {
        test_type: 'analytics',
        timestamp: new Date().toISOString(),
      });
      
      // 测试用户行为追踪
      trackUserAction('test_button_click', {
        button_name: 'analytics_test',
        page: '/admin/monitoring/test',
      });
      
      addTestResult('分析追踪', 'success', 'Vercel Analytics 事件追踪测试成功');
    } catch (error) {
      addTestResult('分析追踪', 'error', `测试失败: ${(error as Error).message}`);
    }
  };

  // 测试日志记录
  const testLogging = async () => {
    try {
      addTestResult('日志记录', 'pending', '正在测试...');
      
      // 测试不同级别的日志
      logInfo('监控系统测试 - 信息日志', {
        test_type: 'logging',
        level: 'info',
        timestamp: new Date().toISOString(),
      });
      
      logError('监控系统测试 - 错误日志', new Error('测试错误'), {
        test_type: 'logging',
        level: 'error',
      });
      
      addTestResult('日志记录', 'success', '结构化日志记录测试成功');
    } catch (error) {
      addTestResult('日志记录', 'error', `测试失败: ${(error as Error).message}`);
    }
  };

  // 测试指标记录
  const testMetrics = async () => {
    try {
      addTestResult('指标记录', 'pending', '正在测试...');
      
      // 记录自定义指标
      recordMetric({
        name: 'monitoring.test.duration',
        value: Math.random() * 1000,
        tags: {
          test_type: 'metrics',
          environment: 'test',
        },
      });
      
      recordMetric({
        name: 'monitoring.test.count',
        value: 1,
        tags: {
          action: 'test_execution',
        },
      });
      
      addTestResult('指标记录', 'success', '自定义指标记录测试成功');
    } catch (error) {
      addTestResult('指标记录', 'error', `测试失败: ${(error as Error).message}`);
    }
  };

  // 测试性能监控
  const testPerformance = async () => {
    try {
      addTestResult('性能监控', 'pending', '正在测试...');
      
      const start = performance.now();
      
      // 模拟一些异步操作
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500));
      
      const duration = performance.now() - start;
      
      recordMetric({
        name: 'monitoring.test.performance',
        value: duration,
        tags: {
          operation: 'async_test',
        },
      });
      
      addTestResult('性能监控', 'success', `性能监控测试成功 (耗时: ${duration.toFixed(2)}ms)`);
    } catch (error) {
      addTestResult('性能监控', 'error', `测试失败: ${(error as Error).message}`);
    }
  };

  // 运行所有测试
  const runAllTests = async () => {
    setTestResults([]);
    await testErrorCapture();
    await testAnalytics();
    await testLogging();
    await testMetrics();
    await testPerformance();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">监控系统测试</h1>
          <p className="text-muted-foreground">
            测试各个监控组件是否正常工作
          </p>
        </div>
        <Button onClick={runAllTests} className="flex items-center gap-2">
          <Zap className="h-4 w-4" />
          运行所有测试
        </Button>
      </div>

      {/* 测试按钮 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              错误捕获测试
            </CardTitle>
            <CardDescription>
              测试 Sentry 错误追踪功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testErrorCapture} variant="outline" className="w-full">
              测试错误捕获
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-blue-500" />
              分析追踪测试
            </CardTitle>
            <CardDescription>
              测试 Vercel Analytics 事件追踪
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testAnalytics} variant="outline" className="w-full">
              测试分析追踪
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-green-500" />
              日志记录测试
            </CardTitle>
            <CardDescription>
              测试结构化日志记录功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testLogging} variant="outline" className="w-full">
              测试日志记录
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-500" />
              指标记录测试
            </CardTitle>
            <CardDescription>
              测试自定义指标记录功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testMetrics} variant="outline" className="w-full">
              测试指标记录
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-purple-500" />
              性能监控测试
            </CardTitle>
            <CardDescription>
              测试性能指标收集功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testPerformance} variant="outline" className="w-full">
              测试性能监控
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>测试结果</CardTitle>
            <CardDescription>
              监控系统各组件测试结果
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {result.status === 'success' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {result.status === 'error' && (
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                    )}
                    {result.status === 'pending' && (
                      <div className="h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    )}
                    <span className="font-medium">{result.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">{result.message}</span>
                    <Badge variant={result.status === 'success' ? 'default' : result.status === 'error' ? 'destructive' : 'secondary'}>
                      {result.status === 'success' ? '成功' : result.status === 'error' ? '失败' : '进行中'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
