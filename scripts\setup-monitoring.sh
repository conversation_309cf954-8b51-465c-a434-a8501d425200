#!/bin/bash

# MapMoment 监控系统快速设置脚本
set -e

echo "🚀 MapMoment 监控系统设置向导"
echo "================================"

# 检查必要的工具
check_dependencies() {
    echo "📋 检查依赖..."
    
    if ! command -v pnpm &> /dev/null; then
        echo "❌ pnpm 未安装，请先安装 pnpm"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    echo "✅ 依赖检查通过"
}

# 选择监控方案
select_monitoring_plan() {
    echo ""
    echo "📊 请选择监控方案:"
    echo "1) 轻量级免费方案 (推荐新项目)"
    echo "2) 专业级混合方案 (推荐成长期项目)"
    echo "3) 企业级全栈方案 (推荐成熟项目)"
    echo "4) 自建开源方案 (推荐技术团队)"
    
    read -p "请输入选择 (1-4): " plan_choice
    
    case $plan_choice in
        1) PLAN="free" ;;
        2) PLAN="professional" ;;
        3) PLAN="enterprise" ;;
        4) PLAN="selfhosted" ;;
        *) echo "❌ 无效选择"; exit 1 ;;
    esac
    
    echo "✅ 已选择: $PLAN 方案"
}

# 安装依赖
install_dependencies() {
    echo ""
    echo "📦 安装监控依赖..."
    
    case $PLAN in
        "free")
            pnpm add @sentry/nextjs @vercel/analytics @vercel/speed-insights
            ;;
        "professional")
            pnpm add @sentry/nextjs @vercel/analytics @vercel/speed-insights posthog-js posthog-node @logtail/pino
            ;;
        "enterprise")
            pnpm add @datadog/browser-rum @datadog/browser-logs dd-trace
            ;;
        "selfhosted")
            pnpm add @opentelemetry/api @opentelemetry/sdk-node @opentelemetry/instrumentation
            ;;
    esac
    
    echo "✅ 依赖安装完成"
}

# 配置环境变量
setup_environment() {
    echo ""
    echo "⚙️  配置环境变量..."
    
    if [ ! -f ".env.local" ]; then
        cp .env.local.example .env.local
        echo "✅ 已创建 .env.local 文件"
    fi
    
    case $PLAN in
        "free")
            echo ""
            echo "请配置以下环境变量到 .env.local:"
            echo "SENTRY_DSN=your-sentry-dsn"
            echo "NEXT_PUBLIC_SENTRY_DSN=your-public-sentry-dsn"
            ;;
        "professional")
            echo ""
            echo "请配置以下环境变量到 .env.local:"
            echo "SENTRY_DSN=your-sentry-dsn"
            echo "NEXT_PUBLIC_SENTRY_DSN=your-public-sentry-dsn"
            echo "NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key"
            echo "LOGTAIL_TOKEN=your-logtail-token"
            ;;
        "enterprise")
            echo ""
            echo "请配置以下环境变量到 .env.local:"
            echo "NEXT_PUBLIC_DD_APPLICATION_ID=your-dd-app-id"
            echo "NEXT_PUBLIC_DD_CLIENT_TOKEN=your-dd-client-token"
            ;;
        "selfhosted")
            echo ""
            echo "请配置以下环境变量到 .env.local:"
            echo "OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318"
            ;;
    esac
}

# 创建配置文件
create_config_files() {
    echo ""
    echo "📝 创建配置文件..."
    
    # 创建监控包目录
    mkdir -p packages/monitoring/src
    
    # 根据方案创建不同的配置
    case $PLAN in
        "free"|"professional")
            # Sentry 配置已经通过我们之前创建的文件存在
            echo "✅ Sentry 配置文件已就绪"
            ;;
        "enterprise")
            # DataDog 配置
            cat > apps/web/lib/datadog.ts << 'EOF'
import { datadogRum } from '@datadog/browser-rum';

if (typeof window !== 'undefined') {
  datadogRum.init({
    applicationId: process.env.NEXT_PUBLIC_DD_APPLICATION_ID!,
    clientToken: process.env.NEXT_PUBLIC_DD_CLIENT_TOKEN!,
    site: 'datadoghq.com',
    service: 'mapmoment',
    env: process.env.NODE_ENV,
    version: '1.0.0',
    sessionSampleRate: 100,
    trackInteractions: true,
    trackResources: true,
    trackLongTasks: true,
  });
}
EOF
            echo "✅ DataDog 配置文件已创建"
            ;;
        "selfhosted")
            # OpenTelemetry 配置
            cat > apps/web/lib/telemetry.ts << 'EOF'
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';

const sdk = new NodeSDK({
  instrumentations: [getNodeAutoInstrumentations()],
});

sdk.start();
EOF
            echo "✅ OpenTelemetry 配置文件已创建"
            ;;
    esac
}

# 验证设置
verify_setup() {
    echo ""
    echo "🔍 验证设置..."
    
    # 检查文件是否存在
    if [ -f "packages/monitoring/package.json" ]; then
        echo "✅ 监控包配置正确"
    else
        echo "❌ 监控包配置缺失"
    fi
    
    # 检查环境变量
    if [ -f ".env.local" ]; then
        echo "✅ 环境变量文件存在"
    else
        echo "❌ 环境变量文件缺失"
    fi
    
    echo ""
    echo "🎉 监控系统设置完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 配置 .env.local 中的环境变量"
    echo "2. 运行 'pnpm dev' 启动开发服务器"
    echo "3. 访问 /admin/monitoring 查看监控面板"
    echo "4. 查看 docs/monitoring-solutions.md 了解详细配置"
}

# 主函数
main() {
    check_dependencies
    select_monitoring_plan
    install_dependencies
    setup_environment
    create_config_files
    verify_setup
}

# 运行主函数
main
