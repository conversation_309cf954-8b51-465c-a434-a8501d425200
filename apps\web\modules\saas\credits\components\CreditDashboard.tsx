"use client";

import { 
  Bar<PERSON>hart3, 
  Calendar, 
  Coins, 
  TrendingDown, 
  TrendingUp,
  Zap 
} from "lucide-react";
import { useCreditStats } from "../hooks/useCredits";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";

interface CreditDashboardProps {
  className?: string;
  variant?: "full" | "compact";
}

/**
 * 积分系统仪表板组件
 */
export function CreditDashboard({ 
  className, 
  variant = "full" 
}: CreditDashboardProps) {
  const stats = useCreditStats();

  if (!stats) {
    return <CreditDashboardSkeleton variant={variant} className={className} />;
  }

  if (variant === "compact") {
    return (
      <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
        <StatCard
          title="当前余额"
          value={stats.balance}
          icon={Coins}
          color="text-primary"
          suffix="积分"
        />
        <StatCard
          title="累计获得"
          value={stats.totalEarned}
          icon={TrendingUp}
          color="text-green-600"
          suffix="积分"
        />
        <StatCard
          title="累计消耗"
          value={stats.totalConsumed}
          icon={TrendingDown}
          color="text-orange-600"
          suffix="积分"
        />
        <StatCard
          title="本周获得"
          value={stats.weeklyEarned}
          icon={Zap}
          color="text-blue-600"
          suffix="积分"
        />
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="当前余额"
          value={stats.balance}
          icon={Coins}
          color="text-primary"
          suffix="积分"
          description="可用于功能消耗"
        />
        <StatCard
          title="累计获得"
          value={stats.totalEarned}
          icon={TrendingUp}
          color="text-green-600"
          suffix="积分"
          description="历史总获得量"
        />
        <StatCard
          title="累计消耗"
          value={stats.totalConsumed}
          icon={TrendingDown}
          color="text-orange-600"
          suffix="积分"
          description="历史总消耗量"
        />
        <StatCard
          title="本月消耗"
          value={stats.monthlyConsumption}
          icon={BarChart3}
          color="text-purple-600"
          suffix="积分"
          description="当月使用情况"
        />
      </div>

      {/* 最近交易 */}
      {stats.recentTransactions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              最近交易
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentTransactions.map((transaction) => (
                <RecentTransactionItem
                  key={transaction.id}
                  transaction={transaction}
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

/**
 * 统计卡片组件
 */
interface StatCardProps {
  title: string;
  value: number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  suffix?: string;
  description?: string;
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  suffix, 
  description 
}: StatCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <div className="flex items-baseline gap-1">
              <p className={cn("text-2xl font-bold", color)}>
                {value.toLocaleString()}
              </p>
              {suffix && (
                <span className="text-sm text-muted-foreground">{suffix}</span>
              )}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          <div className={cn("p-2 rounded-lg bg-muted", color)}>
            <Icon className="h-5 w-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 最近交易项组件
 */
interface RecentTransactionItemProps {
  transaction: any; // CreditTransaction type
}

function RecentTransactionItem({ transaction }: RecentTransactionItemProps) {
  const isPositive = transaction.type.startsWith("EARNED_") || 
                    transaction.type.startsWith("PURCHASED_");
  
  const getTransactionTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      EARNED_SUBSCRIPTION: "订阅赠送",
      EARNED_PURCHASE: "购买获得",
      EARNED_BONUS: "奖励获得",
      CONSUMED_AI_GENERATION: "AI生成",
      CONSUMED_STORAGE_UPLOAD: "存储上传",
      CONSUMED_MAP_GEOCODING: "地图编码",
      CONSUMED_API_CALL: "API调用",
    };
    return typeMap[type] || type;
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      COMPLETED: { label: "已完成", status: "success" as const },
      PENDING: { label: "处理中", status: "warning" as const },
      FAILED: { label: "失败", status: "error" as const },
    };
    return statusMap[status as keyof typeof statusMap] || { label: status, status: "info" as const };
  };

  const statusInfo = getStatusBadge(transaction.status);

  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center gap-3">
        <div className={cn(
          "w-2 h-2 rounded-full",
          isPositive ? "bg-green-500" : "bg-orange-500"
        )} />
        <div>
          <div className="font-medium text-sm">
            {getTransactionTypeLabel(transaction.type)}
          </div>
          <div className="text-xs text-muted-foreground">
            {new Date(transaction.createdAt).toLocaleDateString()}
          </div>
        </div>
      </div>
      <div className="text-right">
        <div className={cn(
          "font-medium text-sm",
          isPositive ? "text-green-600" : "text-orange-600"
        )}>
          {isPositive ? "+" : "-"}{Math.abs(transaction.amount).toLocaleString()}
        </div>
        <Badge status={statusInfo.status} className="text-xs">
          {statusInfo.label}
        </Badge>
      </div>
    </div>
  );
}

/**
 * 仪表板骨架屏
 */
function CreditDashboardSkeleton({ 
  variant = "full", 
  className 
}: { 
  variant?: "full" | "compact";
  className?: string;
}) {
  if (variant === "compact") {
    return (
      <div className={cn("grid gap-4 md:grid-cols-2 lg:grid-cols-4", className)}>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-8 w-20 mb-1" />
                  <Skeleton className="h-3 w-12" />
                </div>
                <Skeleton className="h-9 w-9 rounded-lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Skeleton className="h-4 w-16 mb-2" />
                  <Skeleton className="h-8 w-20 mb-1" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <Skeleton className="h-9 w-9 rounded-lg" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-24" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between py-2">
                <div className="flex items-center gap-3">
                  <Skeleton className="w-2 h-2 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-3 w-16" />
                  </div>
                </div>
                <div className="text-right">
                  <Skeleton className="h-4 w-12 mb-1" />
                  <Skeleton className="h-5 w-10" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
