import pino from 'pino';
import type { ErrorContext } from './types';

// 创建结构化日志记录器
const createLogger = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return pino({
    level: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info'),
    ...(isDevelopment && {
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
        },
      },
    }),
    ...(!isDevelopment && {
      // 生产环境使用 JSON 格式
      formatters: {
        level: (label) => ({ level: label }),
        log: (object) => object,
      },
    }),
  });
};

export const logger = createLogger();

// 结构化日志工具函数
export const logInfo = (message: string, data?: Record<string, any>) => {
  logger.info(data, message);
};

export const logError = (message: string, error?: Error, context?: ErrorContext) => {
  logger.error(
    {
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : undefined,
      context,
    },
    message
  );
};

export const logWarn = (message: string, data?: Record<string, any>) => {
  logger.warn(data, message);
};

export const logDebug = (message: string, data?: Record<string, any>) => {
  logger.debug(data, message);
};

// API 请求日志
export const logApiRequest = (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
  logger.info({
    type: 'api_request',
    method,
    url,
    statusCode,
    duration,
    userId,
  }, `${method} ${url} - ${statusCode} (${duration}ms)`);
};

// 数据库操作日志
export const logDatabaseOperation = (operation: string, table: string, duration: number, success: boolean) => {
  logger.info({
    type: 'database_operation',
    operation,
    table,
    duration,
    success,
  }, `DB ${operation} on ${table} - ${success ? 'SUCCESS' : 'FAILED'} (${duration}ms)`);
};

// 业务事件日志
export const logBusinessEvent = (event: string, userId?: string, organizationId?: string, data?: Record<string, any>) => {
  logger.info({
    type: 'business_event',
    event,
    userId,
    organizationId,
    data,
  }, `Business event: ${event}`);
};

// 安全事件日志
export const logSecurityEvent = (event: string, userId?: string, ip?: string, userAgent?: string, data?: Record<string, any>) => {
  logger.warn({
    type: 'security_event',
    event,
    userId,
    ip,
    userAgent,
    data,
  }, `Security event: ${event}`);
};

// 性能日志
export const logPerformance = (operation: string, duration: number, metadata?: Record<string, any>) => {
  logger.info({
    type: 'performance',
    operation,
    duration,
    metadata,
  }, `Performance: ${operation} took ${duration}ms`);
};

// 日志中间件（用于 Hono.js）
export const createLoggerMiddleware = () => {
  return async (c: any, next: any) => {
    const start = Date.now();
    const method = c.req.method;
    const url = c.req.url;
    
    try {
      await next();
      const duration = Date.now() - start;
      const statusCode = c.res.status;
      
      logApiRequest(method, url, statusCode, duration);
    } catch (error) {
      const duration = Date.now() - start;
      logError(`API Error: ${method} ${url}`, error as Error, {
        method,
        url,
        statusCode: 500,
      });
      throw error;
    }
  };
};

// 日志聚合和导出（用于外部日志服务）
export const exportLogs = (startTime: Date, endTime: Date) => {
  // 这里可以实现日志导出逻辑
  // 例如发送到 Elasticsearch、Splunk 等
  logger.info({
    type: 'log_export',
    startTime,
    endTime,
  }, 'Exporting logs');
};
