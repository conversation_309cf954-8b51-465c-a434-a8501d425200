# MapMoment 监控和可观测性方案

## 🎯 方案概览

基于 MapMoment 项目的技术栈和独立开发者需求，我们设计了4个不同层次的监控方案：

| 方案 | 月成本 | 复杂度 | 适用阶段 | 核心优势 |
|------|--------|--------|----------|----------|
| **轻量级免费方案** | $0 | 低 | MVP/初期 | 零成本快速上线 |
| **专业级混合方案** | $20-50 | 中 | 成长期 | 性价比最优 |
| **企业级全栈方案** | $100-200 | 高 | 成熟期 | 功能最完整 |
| **自建开源方案** | $10-30 | 高 | 技术驱动 | 完全可控 |

---

## 📊 方案1：轻量级免费方案

### 技术栈组合
- **APM**: Vercel Analytics (免费)
- **错误追踪**: Sentry (免费层)
- **日志**: 自建结构化日志
- **业务指标**: 自建 + Umami Analytics

### 成本分析
- **总成本**: $0/月
- **Vercel Analytics**: 免费（100k events/月）
- **Sentry**: 免费（5k errors/月，1个用户）
- **Umami**: 自托管免费

### 免费额度限制
- Sentry: 5,000 errors/月，1个用户
- Vercel Analytics: 100,000 events/月
- 无实时告警（需要手动检查）

### 集成步骤

#### 1. 安装依赖
```bash
pnpm add @sentry/nextjs @vercel/analytics @vercel/speed-insights
```

#### 2. 配置环境变量
```bash
# .env.local
SENTRY_DSN="your-sentry-dsn"
NEXT_PUBLIC_SENTRY_DSN="your-public-sentry-dsn"
NEXT_PUBLIC_UMAMI_WEBSITE_ID="your-website-id"
```

#### 3. 集成到应用
```typescript
// apps/web/app/layout.tsx
import { MonitoringProvider } from '@/components/providers/MonitoringProvider';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <MonitoringProvider>
          {children}
        </MonitoringProvider>
      </body>
    </html>
  );
}
```

#### 4. API 监控集成
```typescript
// packages/api/src/app.ts
import { monitoringMiddleware } from './middleware/monitoring';

const app = new Hono()
  .use('*', monitoringMiddleware)
  .route('/api', apiRouter);
```

### 优势
- ✅ 完全免费
- ✅ 快速集成
- ✅ 覆盖基本监控需求
- ✅ 适合 MVP 阶段

### 劣势
- ❌ 功能有限
- ❌ 无实时告警
- ❌ 数据保留期短
- ❌ 无高级分析功能

---

## 💼 方案2：专业级混合方案

### 技术栈组合
- **APM**: Vercel Analytics + Sentry Performance
- **错误追踪**: Sentry Pro
- **日志**: Better Stack (LogTail)
- **业务指标**: PostHog
- **监控**: UptimeRobot

### 成本分析
- **总成本**: $20-50/月
- **Sentry Pro**: $26/月（50k errors，无限用户）
- **Better Stack**: $8/月（5GB logs）
- **PostHog**: $0-20/月（1M events免费）
- **UptimeRobot**: 免费（50个监控）

### 集成步骤

#### 1. 升级 Sentry 配置
```typescript
// sentry.client.config.ts
Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  tracesSampleRate: 0.1,
  profilesSampleRate: 0.1,
  replaysSessionSampleRate: 0.01,
  integrations: [
    new Sentry.Replay(),
    new Sentry.BrowserTracing(),
  ],
});
```

#### 2. 集成 PostHog
```bash
pnpm add posthog-js posthog-node
```

```typescript
// lib/posthog.ts
import posthog from 'posthog-js';

if (typeof window !== 'undefined') {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
    api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
  });
}
```

#### 3. 集成 Better Stack
```typescript
// packages/monitoring/src/logger.ts
import { createLogger } from '@logtail/pino';

const logger = createLogger(process.env.LOGTAIL_TOKEN!);
```

### 优势
- ✅ 专业级功能
- ✅ 实时告警
- ✅ 用户行为分析
- ✅ 性能监控
- ✅ 合理成本

### 劣势
- ❌ 需要付费
- ❌ 配置复杂度中等
- ❌ 多个服务管理

---

## 🏢 方案3：企业级全栈方案

### 技术栈组合
- **APM**: DataDog APM
- **错误追踪**: DataDog Error Tracking
- **日志**: DataDog Logs
- **监控**: DataDog Infrastructure
- **业务指标**: DataDog RUM

### 成本分析
- **总成本**: $100-200/月
- **DataDog Pro**: $15/host/月 + $5/100万spans
- **RUM**: $1.2/1000 sessions
- **Logs**: $1.7/GB ingested

### 集成步骤

#### 1. 安装 DataDog SDK
```bash
pnpm add @datadog/browser-rum @datadog/browser-logs dd-trace
```

#### 2. 配置 DataDog
```typescript
// lib/datadog.ts
import { datadogRum } from '@datadog/browser-rum';

datadogRum.init({
  applicationId: process.env.NEXT_PUBLIC_DD_APPLICATION_ID!,
  clientToken: process.env.NEXT_PUBLIC_DD_CLIENT_TOKEN!,
  site: 'datadoghq.com',
  service: 'mapmoment',
  env: process.env.NODE_ENV,
  version: '1.0.0',
  sessionSampleRate: 100,
  trackInteractions: true,
  trackResources: true,
  trackLongTasks: true,
});
```

### 优势
- ✅ 企业级功能
- ✅ 统一平台
- ✅ 强大的分析能力
- ✅ 自定义仪表板
- ✅ 高级告警

### 劣势
- ❌ 成本较高
- ❌ 配置复杂
- ❌ 可能过度设计

---

## 🛠️ 方案4：自建开源方案

### 技术栈组合
- **APM**: OpenTelemetry + Jaeger
- **错误追踪**: Sentry (自托管)
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **监控**: Prometheus + Grafana
- **告警**: AlertManager

### 成本分析
- **总成本**: $10-30/月（服务器成本）
- **VPS**: $10-20/月（2-4GB RAM）
- **存储**: $5-10/月

### 部署架构
```yaml
# docker-compose.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
  
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
  
  jaeger:
    image: jaegertracing/all-in-one
    ports:
      - "16686:16686"
      - "14268:14268"
  
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
  
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### 优势
- ✅ 完全可控
- ✅ 成本极低
- ✅ 无数据限制
- ✅ 高度定制化

### 劣势
- ❌ 维护复杂
- ❌ 需要运维知识
- ❌ 初期投入大
- ❌ 可靠性需要自己保证

---

## 🎯 推荐方案选择

### 项目初期（MVP阶段）
**推荐：方案1 - 轻量级免费方案**
- 零成本快速验证
- 覆盖基本监控需求
- 快速集成和部署

### 项目成长期（有付费用户）
**推荐：方案2 - 专业级混合方案**
- 性价比最优
- 功能相对完整
- 支持业务增长

### 项目成熟期（规模化运营）
**推荐：方案3 - 企业级全栈方案**
- 功能最完整
- 统一管理平台
- 支持大规模监控

### 技术驱动团队
**推荐：方案4 - 自建开源方案**
- 完全可控
- 成本极低
- 高度定制化

---

## 🚀 实施建议

### 阶段性实施
1. **第一阶段**：从方案1开始，建立基础监控
2. **第二阶段**：根据业务增长升级到方案2
3. **第三阶段**：规模化后考虑方案3或方案4

### 关键指标定义
- **性能指标**：API响应时间、页面加载时间、数据库查询时间
- **可用性指标**：错误率、服务可用性、系统健康状态
- **业务指标**：用户活跃度、功能使用率、转化率

### 告警策略
- **P0告警**：系统不可用、错误率>5%
- **P1告警**：性能下降>50%、关键功能异常
- **P2告警**：性能下降>20%、非关键功能异常

建议从方案1开始实施，随着项目发展逐步升级到更高级的方案。

---

## 📋 具体实施步骤

### 方案1实施清单

#### 1. 环境准备
```bash
# 1. 创建 Sentry 账号并获取 DSN
# 2. 在 Vercel 项目中启用 Analytics
# 3. 配置环境变量

# 安装监控包依赖
pnpm add @sentry/nextjs @vercel/analytics @vercel/speed-insights
```

#### 2. 代码集成
```bash
# 创建监控包
mkdir -p packages/monitoring/src
touch packages/monitoring/package.json
touch packages/monitoring/index.ts

# 创建配置文件
touch apps/web/instrumentation.ts
touch apps/web/sentry.client.config.ts
touch apps/web/sentry.server.config.ts
```

#### 3. 配置检查清单
- [ ] Sentry DSN 配置正确
- [ ] Vercel Analytics 已启用
- [ ] 错误捕获正常工作
- [ ] 性能监控数据可见
- [ ] 日志输出格式正确

#### 4. 测试验证
```typescript
// 测试错误捕获
throw new Error('Test error for monitoring');

// 测试性能追踪
import { trackEvent } from '@repo/monitoring/analytics';
trackEvent('test_event', { test: true });

// 测试日志记录
import { logInfo } from '@repo/monitoring/logger';
logInfo('Test log message', { test: true });
```

### 监控仪表板设置

#### Sentry 仪表板配置
1. **错误监控**：设置错误阈值告警
2. **性能监控**：配置响应时间告警
3. **发布追踪**：关联代码部署和错误

#### Vercel Analytics 配置
1. **页面访问**：追踪关键页面PV
2. **用户行为**：追踪关键操作
3. **转化漏斗**：设置业务转化追踪

### 告警配置

#### Sentry 告警规则
```yaml
# 错误率告警
- name: "高错误率告警"
  condition: "error_rate > 1%"
  time_window: "5分钟"
  notification: "邮件 + Slack"

# 性能告警
- name: "响应时间告警"
  condition: "p95_response_time > 2000ms"
  time_window: "10分钟"
  notification: "邮件"
```

#### 自定义告警脚本
```typescript
// packages/monitoring/src/alerts.ts
export const checkSystemHealth = async () => {
  const metrics = await getMetricsStats('api.response.duration');

  if (metrics && metrics.avg > 2000) {
    // 发送告警
    await sendAlert({
      level: 'warning',
      message: `API响应时间过高: ${metrics.avg}ms`,
      metrics,
    });
  }
};
```

---

## 🔧 最佳实践配置

### 1. 错误处理最佳实践

```typescript
// 全局错误边界
'use client';
import { captureError } from '@repo/monitoring/sentry';

export class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    captureError(error, {
      errorInfo: errorInfo.componentStack,
    });
  }
}

// API 错误处理
export const apiErrorHandler = (error: Error, context: any) => {
  captureError(error, {
    url: context.req.url,
    method: context.req.method,
    userId: context.user?.id,
  });
};
```

### 2. 性能监控最佳实践

```typescript
// 关键业务流程监控
export const trackUserJourney = (step: string, userId: string) => {
  trackBusinessMetric({
    event: 'user_journey',
    userId,
    properties: { step, timestamp: Date.now() },
  });
};

// 数据库查询优化监控
export const monitorSlowQueries = (query: string, duration: number) => {
  if (duration > 1000) {
    logWarn('Slow query detected', {
      query: query.substring(0, 100),
      duration,
    });
  }
};
```

### 3. 业务指标追踪

```typescript
// 关键业务事件
export const trackBusinessEvents = {
  userSignup: (userId: string) =>
    trackBusinessMetric({
      event: 'user_signup',
      userId,
      properties: { source: 'web' },
    }),

  diaryCreated: (userId: string, diaryId: string) =>
    trackBusinessMetric({
      event: 'diary_created',
      userId,
      properties: { diaryId },
    }),

  paymentCompleted: (userId: string, amount: number) =>
    trackBusinessMetric({
      event: 'payment_completed',
      userId,
      properties: { amount, currency: 'USD' },
    }),
};
```

### 4. 成本优化建议

#### Sentry 成本优化
```typescript
// 采样率配置
const tracesSampleRate = process.env.NODE_ENV === 'production' ? 0.1 : 1.0;
const profilesSampleRate = process.env.NODE_ENV === 'production' ? 0.05 : 1.0;

// 错误过滤
const ignoreErrors = [
  'ResizeObserver loop limit exceeded',
  'Non-Error promise rejection captured',
  'ChunkLoadError',
  /^Network request failed/,
];
```

#### 日志成本优化
```typescript
// 日志级别控制
const logLevel = process.env.NODE_ENV === 'production' ? 'info' : 'debug';

// 敏感信息过滤
const sanitizeLogData = (data: any) => {
  const sanitized = { ...data };
  delete sanitized.password;
  delete sanitized.token;
  delete sanitized.apiKey;
  return sanitized;
};
```

---

## 📊 监控效果评估

### 关键指标 KPI

#### 技术指标
- **可用性**: 99.9% uptime
- **性能**: P95 响应时间 < 500ms
- **错误率**: < 0.1%
- **MTTR**: < 30分钟

#### 业务指标
- **用户体验**: 页面加载时间 < 3秒
- **转化率**: 注册转化率 > 5%
- **留存率**: 7日留存率 > 30%

### 监控报告模板

```typescript
// 每周监控报告
export const generateWeeklyReport = async () => {
  const report = {
    period: '2024-01-01 to 2024-01-07',
    summary: {
      totalRequests: 1234567,
      errorRate: 0.12,
      avgResponseTime: 245,
      uptime: 99.95,
    },
    topErrors: await getTopErrors(7),
    slowestEndpoints: await getSlowestEndpoints(7),
    businessMetrics: {
      newUsers: 123,
      activeUsers: 1234,
      revenue: 5678,
    },
  };

  return report;
};
```

通过这套完整的监控方案，你可以从免费的基础监控开始，随着业务发展逐步升级到更专业的监控体系。
