import { createMiddleware } from 'hono/factory';
import { trackApiResponse, recordMetric } from '@repo/monitoring/metrics';
import { logApiRequest, logError } from '@repo/monitoring/logger';
import { captureError } from '@repo/monitoring/sentry';

// API 监控中间件
export const monitoringMiddleware = createMiddleware(async (c, next) => {
  const start = Date.now();
  const method = c.req.method;
  const url = c.req.url;
  const userAgent = c.req.header('user-agent');
  const userId = c.get('user')?.id;
  
  try {
    await next();
    
    const duration = Date.now() - start;
    const statusCode = c.res.status;
    
    // 记录 API 响应时间
    trackApiResponse(url, method, statusCode, duration);
    
    // 记录结构化日志
    logApiRequest(method, url, statusCode, duration, userId);
    
    // 记录自定义指标
    recordMetric({
      name: 'api.request.count',
      value: 1,
      tags: {
        method,
        status: statusCode.toString(),
        endpoint: extractEndpoint(url),
      },
    });
    
    recordMetric({
      name: 'api.request.duration',
      value: duration,
      tags: {
        method,
        endpoint: extractEndpoint(url),
      },
    });
    
  } catch (error) {
    const duration = Date.now() - start;
    const statusCode = 500;
    
    // 记录错误
    logError(`API Error: ${method} ${url}`, error as Error, {
      method,
      url,
      statusCode,
      userAgent,
      userId,
    });
    
    // 发送错误到 Sentry
    captureError(error as Error, {
      method,
      url,
      statusCode,
      userAgent,
      userId,
    });
    
    // 记录错误指标
    recordMetric({
      name: 'api.error.count',
      value: 1,
      tags: {
        method,
        endpoint: extractEndpoint(url),
        error_type: (error as Error).name,
      },
    });
    
    throw error;
  }
});

// 数据库查询监控中间件
export const databaseMonitoringMiddleware = createMiddleware(async (c, next) => {
  // 拦截 Prisma 查询（需要在 Prisma 客户端中配置）
  const originalPrisma = c.get('prisma');
  
  if (originalPrisma) {
    // 包装 Prisma 客户端以监控查询
    const monitoredPrisma = new Proxy(originalPrisma, {
      get(target, prop) {
        const original = target[prop];
        
        if (typeof original === 'object' && original !== null) {
          // 包装模型方法
          return new Proxy(original, {
            get(modelTarget, modelProp) {
              const modelMethod = modelTarget[modelProp];
              
              if (typeof modelMethod === 'function') {
                return async (...args: any[]) => {
                  const start = Date.now();
                  const operation = `${String(prop)}.${String(modelProp)}`;
                  
                  try {
                    const result = await modelMethod.apply(modelTarget, args);
                    const duration = Date.now() - start;
                    
                    // 记录成功的数据库操作
                    recordMetric({
                      name: 'database.query.duration',
                      value: duration,
                      tags: {
                        operation,
                        success: 'true',
                      },
                    });
                    
                    return result;
                  } catch (error) {
                    const duration = Date.now() - start;
                    
                    // 记录失败的数据库操作
                    recordMetric({
                      name: 'database.query.duration',
                      value: duration,
                      tags: {
                        operation,
                        success: 'false',
                        error: (error as Error).name,
                      },
                    });
                    
                    throw error;
                  }
                };
              }
              
              return modelMethod;
            },
          });
        }
        
        return original;
      },
    });
    
    c.set('prisma', monitoredPrisma);
  }
  
  await next();
});

// 提取端点名称（移除动态参数）
function extractEndpoint(url: string): string {
  try {
    const pathname = new URL(url).pathname;
    // 简单的端点提取逻辑，可以根据需要改进
    return pathname
      .replace(/\/[0-9a-f-]{36}/g, '/:id') // UUID
      .replace(/\/\d+/g, '/:id') // 数字 ID
      .replace(/\/[a-zA-Z0-9-_]+$/g, '/:param'); // 最后的参数
  } catch {
    return url;
  }
}

// 健康检查端点
export const healthCheckHandler = (c: any) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0',
  };
  
  return c.json(health);
};

// 指标端点（用于 Prometheus 等监控系统）
export const metricsHandler = (c: any) => {
  const { exportMetrics } = require('@repo/monitoring/metrics');
  const metrics = exportMetrics();
  
  return c.json({
    timestamp: new Date().toISOString(),
    metrics,
  });
};
