import { z } from "zod";

// ============================================================================
// 基础类型验证
// ============================================================================

export const creditAccountIdSchema = z.string().min(1, "账户ID不能为空");
export const transactionIdSchema = z.string().min(1, "交易ID不能为空");
export const allocationIdSchema = z.string().min(1, "分配ID不能为空");
export const packageIdSchema = z.string().min(1, "积分包ID不能为空");

export const creditAmountSchema = z.number()
  .int("积分数量必须是整数")
  .min(1, "积分数量必须大于0");

export const reasonSchema = z.string()
  .min(1, "原因不能为空")
  .max(500, "原因不能超过500个字符");

// ============================================================================
// 积分账户相关
// ============================================================================

export const createAccountSchema = z.object({
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  initialBalance: z.number().int().min(0).default(0),
}).refine(
  (data) => data.userId || data.organizationId,
  {
    message: "必须提供用户ID或组织ID",
    path: ["userId", "organizationId"]
  }
);

export const updateAccountStatusSchema = z.object({
  status: z.enum(["ACTIVE", "SUSPENDED", "FROZEN"]),
  reason: reasonSchema.optional()
});

export const accountQuerySchema = z.object({
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  includeBalance: z.boolean().default(true),
  includeTransactions: z.boolean().default(false),
  transactionLimit: z.number().int().min(1).max(100).default(10)
});

// ============================================================================
// 积分交易相关
// ============================================================================

export const consumeCreditsSchema = z.object({
  accountId: creditAccountIdSchema.optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  amount: creditAmountSchema,
  reason: reasonSchema,
  featureId: z.string().optional(),
  metadata: z.record(z.any()).optional()
}).refine(
  (data) => data.accountId || data.userId || data.organizationId,
  {
    message: "必须提供账户ID、用户ID或组织ID",
    path: ["accountId", "userId", "organizationId"]
  }
);

export const addCreditsSchema = z.object({
  accountId: creditAccountIdSchema.optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  amount: creditAmountSchema,
  reason: reasonSchema,
  type: z.enum(["EARNED_PURCHASE", "EARNED_SUBSCRIPTION", "EARNED_BONUS", "EARNED_REFUND"]),
  expiresAt: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional()
}).refine(
  (data) => data.accountId || data.userId || data.organizationId,
  {
    message: "必须提供账户ID、用户ID或组织ID",
    path: ["accountId", "userId", "organizationId"]
  }
);

export const transferCreditsSchema = z.object({
  fromAccountId: creditAccountIdSchema.optional(),
  fromUserId: z.string().optional(),
  fromOrganizationId: z.string().optional(),
  toAccountId: creditAccountIdSchema.optional(),
  toUserId: z.string().optional(),
  toOrganizationId: z.string().optional(),
  amount: creditAmountSchema,
  reason: reasonSchema,
  metadata: z.record(z.any()).optional()
}).refine(
  (data) => (data.fromAccountId || data.fromUserId || data.fromOrganizationId) &&
           (data.toAccountId || data.toUserId || data.toOrganizationId),
  {
    message: "必须提供发送方和接收方的账户信息",
    path: ["fromAccountId", "toAccountId"]
  }
);

export const transactionQuerySchema = z.object({
  accountId: creditAccountIdSchema.optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  type: z.enum([
    "EARNED_PURCHASE", "EARNED_SUBSCRIPTION", "EARNED_BONUS", "EARNED_REFUND",
    "SPENT_FEATURE", "SPENT_TRANSFER", "RECEIVED_TRANSFER", "EXPIRED", "REFUNDED"
  ]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(["createdAt", "amount", "type"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
});

// ============================================================================
// 积分分配相关
// ============================================================================

export const createAllocationSchema = z.object({
  accountId: creditAccountIdSchema.optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  subscriptionPlan: z.string().min(1, "订阅计划不能为空"),
  customAmount: z.number().int().min(1).optional(),
  customInterval: z.enum(["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
}).refine(
  (data) => data.accountId || data.userId || data.organizationId,
  {
    message: "必须提供账户ID、用户ID或组织ID",
    path: ["accountId", "userId", "organizationId"]
  }
);

export const updateAllocationSchema = z.object({
  status: z.enum(["ACTIVE", "PAUSED", "CANCELLED"]).optional(),
  customAmount: z.number().int().min(1).optional(),
  customInterval: z.enum(["DAILY", "WEEKLY", "MONTHLY", "YEARLY"]).optional(),
  endDate: z.string().datetime().optional()
});

export const allocationQuerySchema = z.object({
  accountId: creditAccountIdSchema.optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  status: z.enum(["ACTIVE", "PAUSED", "CANCELLED"]).optional(),
  subscriptionPlan: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20)
});

// ============================================================================
// 积分包相关
// ============================================================================

export const createPackageSchema = z.object({
  name: z.string().min(1, "积分包名称不能为空").max(100),
  description: z.string().max(500).optional(),
  credits: z.number().int().min(1, "积分数量必须大于0"),
  price: z.number().min(0, "价格不能为负数"),
  currency: z.string().length(3, "货币代码必须是3位"),
  validityDays: z.number().int().min(1).optional(),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  metadata: z.record(z.any()).optional()
});

export const updatePackageSchema = createPackageSchema.partial();

export const purchasePackageSchema = z.object({
  packageId: packageIdSchema,
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  paymentProvider: z.enum(["stripe", "lemonsqueezy", "chargebee", "polar", "creem"]),
  paymentMethodId: z.string().optional(),
  metadata: z.record(z.any()).optional()
}).refine(
  (data) => data.userId || data.organizationId,
  {
    message: "必须提供用户ID或组织ID",
    path: ["userId", "organizationId"]
  }
);

export const refundPackageSchema = z.object({
  purchaseId: z.string().min(1, "购买ID不能为空"),
  reason: reasonSchema,
  refundAmount: z.number().min(0).optional(),
  metadata: z.record(z.any()).optional()
});

export const packageQuerySchema = z.object({
  isActive: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  minCredits: z.number().int().min(1).optional(),
  maxCredits: z.number().int().min(1).optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  currency: z.string().length(3).optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(["createdAt", "price", "credits", "name"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
});

// ============================================================================
// 管理员相关
// ============================================================================

export const adminAccountQuerySchema = z.object({
  status: z.enum(["ACTIVE", "SUSPENDED", "FROZEN"]).optional(),
  hasLowBalance: z.boolean().optional(),
  balanceThreshold: z.number().int().min(0).default(100),
  search: z.string().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(["createdAt", "currentBalance", "totalEarned", "totalSpent"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc")
});

export const adminAnalyticsQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  groupBy: z.enum(["day", "week", "month"]).default("day"),
  includeTransactionTypes: z.boolean().default(true),
  includeTopFeatures: z.boolean().default(true),
  includePackageStats: z.boolean().default(true)
});

// ============================================================================
// 响应类型
// ============================================================================

export const creditAccountResponseSchema = z.object({
  id: z.string(),
  userId: z.string().nullable(),
  organizationId: z.string().nullable(),
  currentBalance: z.number(),
  totalEarned: z.number(),
  totalSpent: z.number(),
  status: z.enum(["ACTIVE", "SUSPENDED", "FROZEN"]),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const creditTransactionResponseSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  type: z.string(),
  amount: z.number(),
  reason: z.string(),
  featureId: z.string().nullable(),
  purchaseId: z.string().nullable(),
  expiresAt: z.string().nullable(),
  metadata: z.record(z.any()).nullable(),
  createdAt: z.string()
});

export const creditAllocationResponseSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  subscriptionPlan: z.string(),
  amount: z.number(),
  interval: z.string(),
  status: z.enum(["ACTIVE", "PAUSED", "CANCELLED"]),
  nextAllocationAt: z.string().nullable(),
  lastAllocationAt: z.string().nullable(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export const creditPackageResponseSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  credits: z.number(),
  price: z.number(),
  currency: z.string(),
  validityDays: z.number().nullable(),
  isActive: z.boolean(),
  isFeatured: z.boolean(),
  salesCount: z.number(),
  totalRevenue: z.number(),
  createdAt: z.string(),
  updatedAt: z.string()
});

// ============================================================================
// 分页响应
// ============================================================================

export const paginationResponseSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number()
});

export const paginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema),
    pagination: paginationResponseSchema
  });

// ============================================================================
// 错误响应
// ============================================================================

export const errorResponseSchema = z.object({
  error: z.string(),
  message: z.string().optional(),
  details: z.record(z.any()).optional()
});
