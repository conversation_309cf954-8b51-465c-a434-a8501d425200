// Sentry 服务端配置
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  
  // 服务端采样率
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.05 : 1.0,
  
  // 性能分析采样率
  profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.05 : 1.0,
  
  integrations: [
    // 数据库查询追踪
    new Sentry.Integrations.Prisma({ client: undefined }), // 需要在使用时传入 Prisma 客户端
  ],
  
  // 过滤敏感信息
  beforeSend(event) {
    // 移除敏感的环境变量
    if (event.contexts?.runtime?.name === 'node') {
      delete event.contexts.runtime;
    }
    
    // 移除敏感的请求头
    if (event.request?.headers) {
      delete event.request.headers.authorization;
      delete event.request.headers.cookie;
      delete event.request.headers['x-api-key'];
    }
    
    return event;
  },
  
  // 忽略健康检查等路由
  ignoreTransactions: [
    '/api/health',
    '/api/ping',
    '/_next/static',
  ],
});
