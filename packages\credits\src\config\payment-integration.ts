import { type Config } from "@repo/config";

/**
 * 积分系统与支付系统集成配置
 */

// 支付提供商类型
export type PaymentProvider = "stripe" | "lemonsqueezy" | "chargebee" | "polar" | "creem";

// 积分包产品配置
export interface CreditPackageProductConfig {
  /** 积分包ID */
  packageId: string;
  /** 积分包名称 */
  name: string;
  /** 积分数量 */
  credits: number;
  /** 价格（分） */
  price: number;
  /** 货币 */
  currency: string;
  /** 有效期（天数，null表示永久有效） */
  validityDays: number | null;
  /** 是否推荐 */
  featured: boolean;
  /** 排序权重 */
  sortOrder: number;
  /** 不同支付提供商的产品ID映射 */
  productIds: Record<PaymentProvider, string>;
}

// 订阅计划积分分配配置
export interface SubscriptionCreditAllocation {
  /** 订阅计划ID */
  planId: string;
  /** 积分分配配置 */
  allocation: {
    /** 每次分配的积分数量 */
    amount: number;
    /** 分配周期 */
    period: "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY";
    /** 分配间隔（例如：每2周分配一次） */
    intervalCount: number;
    /** 积分有效期（天数，null表示永久有效） */
    validityDays: number | null;
    /** 是否在订阅开始时立即分配 */
    immediateAllocation: boolean;
    /** 立即分配的积分数量 */
    immediateAmount?: number;
  };
}

// 功能积分消耗配置
export interface FeatureCreditConsumption {
  /** 功能ID */
  featureId: string;
  /** 功能名称 */
  name: string;
  /** 每次使用消耗的积分数量 */
  creditsPerUse: number;
  /** 是否启用 */
  enabled: boolean;
  /** 描述 */
  description?: string;
  /** 功能分类 */
  category: "ai" | "storage" | "map" | "api" | "export" | "analytics" | "integration" | "other";
}

/**
 * 积分系统支付集成配置
 */
export const CREDIT_PAYMENT_CONFIG = {
  // 积分包产品配置
  packages: [
    {
      packageId: "starter-100",
      name: "入门包",
      credits: 100,
      price: 999, // $9.99
      currency: "USD",
      validityDays: 30,
      featured: false,
      sortOrder: 1,
      productIds: {
        stripe: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_STARTER_STRIPE || "",
        lemonsqueezy: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_STARTER_LEMONSQUEEZY || "",
        chargebee: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_STARTER_CHARGEBEE || "",
        polar: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_STARTER_POLAR || "",
        creem: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_STARTER_CREEM || "",
      },
    },
    {
      packageId: "pro-500",
      name: "专业包",
      credits: 500,
      price: 3999, // $39.99
      currency: "USD",
      validityDays: 60,
      featured: true,
      sortOrder: 2,
      productIds: {
        stripe: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_PRO_STRIPE || "",
        lemonsqueezy: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_PRO_LEMONSQUEEZY || "",
        chargebee: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_PRO_CHARGEBEE || "",
        polar: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_PRO_POLAR || "",
        creem: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_PRO_CREEM || "",
      },
    },
    {
      packageId: "enterprise-2000",
      name: "企业包",
      credits: 2000,
      price: 14999, // $149.99
      currency: "USD",
      validityDays: 90,
      featured: false,
      sortOrder: 3,
      productIds: {
        stripe: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_ENTERPRISE_STRIPE || "",
        lemonsqueezy: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_ENTERPRISE_LEMONSQUEEZY || "",
        chargebee: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_ENTERPRISE_CHARGEBEE || "",
        polar: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_ENTERPRISE_POLAR || "",
        creem: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_ENTERPRISE_CREEM || "",
      },
    },
    {
      packageId: "unlimited-10000",
      name: "无限包",
      credits: 10000,
      price: 49999, // $499.99
      currency: "USD",
      validityDays: null, // 永久有效
      featured: false,
      sortOrder: 4,
      productIds: {
        stripe: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_UNLIMITED_STRIPE || "",
        lemonsqueezy: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_UNLIMITED_LEMONSQUEEZY || "",
        chargebee: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_UNLIMITED_CHARGEBEE || "",
        polar: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_UNLIMITED_POLAR || "",
        creem: process.env.NEXT_PUBLIC_CREDIT_PACKAGE_UNLIMITED_CREEM || "",
      },
    },
  ] as CreditPackageProductConfig[],

  // 订阅计划积分分配配置
  subscriptionAllocations: [
    {
      planId: "free",
      allocation: {
        amount: 10,
        period: "DAILY" as const,
        intervalCount: 1,
        validityDays: 7,
        immediateAllocation: true,
        immediateAmount: 50,
      },
    },
    {
      planId: "pro",
      allocation: {
        amount: 100,
        period: "MONTHLY" as const,
        intervalCount: 1,
        validityDays: 60,
        immediateAllocation: true,
        immediateAmount: 200,
      },
    },
    {
      planId: "enterprise",
      allocation: {
        amount: 500,
        period: "MONTHLY" as const,
        intervalCount: 1,
        validityDays: 90,
        immediateAllocation: true,
        immediateAmount: 1000,
      },
    },
  ] as SubscriptionCreditAllocation[],

  // 功能积分消耗配置
  features: [
    // AI 功能
    {
      featureId: "ai_chat",
      name: "AI 聊天",
      creditsPerUse: 1,
      enabled: true,
      description: "每条AI聊天消息",
      category: "ai" as const,
    },
    {
      featureId: "ai_image_generation",
      name: "AI 图片生成",
      creditsPerUse: 10,
      enabled: true,
      description: "每张AI生成的图片",
      category: "ai" as const,
    },
    {
      featureId: "ai_text_analysis",
      name: "AI 文本分析",
      creditsPerUse: 2,
      enabled: true,
      description: "每次AI文本分析",
      category: "ai" as const,
    },

    // 存储功能
    {
      featureId: "file_upload",
      name: "文件上传",
      creditsPerUse: 1,
      enabled: true,
      description: "每MB文件上传",
      category: "storage" as const,
    },
    {
      featureId: "video_export",
      name: "视频导出",
      creditsPerUse: 20,
      enabled: true,
      description: "每个视频导出任务",
      category: "export" as const,
    },

    // 地图功能
    {
      featureId: "map_geocoding",
      name: "地图地理编码",
      creditsPerUse: 1,
      enabled: true,
      description: "每次地址解析",
      category: "map" as const,
    },
    {
      featureId: "map_routing",
      name: "地图路线规划",
      creditsPerUse: 2,
      enabled: true,
      description: "每次路线计算",
      category: "map" as const,
    },

    // API 调用
    {
      featureId: "api_call",
      name: "API 调用",
      creditsPerUse: 1,
      enabled: true,
      description: "每次外部API调用",
      category: "api" as const,
    },

    // 数据导出
    {
      featureId: "data_export",
      name: "数据导出",
      creditsPerUse: 5,
      enabled: true,
      description: "每次数据导出",
      category: "export" as const,
    },

    // 高级功能
    {
      featureId: "advanced_analytics",
      name: "高级分析",
      creditsPerUse: 10,
      enabled: true,
      description: "每次高级分析报告",
      category: "analytics" as const,
    },
    {
      featureId: "custom_integration",
      name: "自定义集成",
      creditsPerUse: 50,
      enabled: true,
      description: "每次自定义集成配置",
      category: "integration" as const,
    },
  ] as FeatureCreditConsumption[],
} as const;

/**
 * 获取积分包配置
 */
export function getCreditPackageConfig(packageId: string): CreditPackageProductConfig | undefined {
  return CREDIT_PAYMENT_CONFIG.packages.find(pkg => pkg.packageId === packageId);
}

/**
 * 获取订阅计划的积分分配配置
 */
export function getSubscriptionCreditAllocation(planId: string): SubscriptionCreditAllocation | undefined {
  return CREDIT_PAYMENT_CONFIG.subscriptionAllocations.find(allocation => allocation.planId === planId);
}

/**
 * 获取功能的积分消耗配置
 */
export function getFeatureCreditConsumption(featureId: string): FeatureCreditConsumption | undefined {
  return CREDIT_PAYMENT_CONFIG.features.find(feature => feature.featureId === featureId);
}

/**
 * 获取支付提供商的积分包产品ID
 */
export function getCreditPackageProductId(packageId: string, provider: PaymentProvider): string | undefined {
  const packageConfig = getCreditPackageConfig(packageId);
  return packageConfig?.productIds[provider];
}

/**
 * 根据产品ID查找积分包配置
 */
export function findCreditPackageByProductId(productId: string, provider: PaymentProvider): CreditPackageProductConfig | undefined {
  return CREDIT_PAYMENT_CONFIG.packages.find(pkg => pkg.productIds[provider] === productId);
}

/**
 * 验证积分包配置的完整性
 */
export function validateCreditPackageConfig(): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  for (const pkg of CREDIT_PAYMENT_CONFIG.packages) {
    // 检查必要字段
    if (!pkg.packageId) {
      errors.push(`积分包缺少packageId: ${JSON.stringify(pkg)}`);
    }
    if (!pkg.name) {
      errors.push(`积分包 ${pkg.packageId} 缺少name`);
    }
    if (pkg.credits <= 0) {
      errors.push(`积分包 ${pkg.packageId} 的积分数量必须大于0`);
    }
    if (pkg.price <= 0) {
      errors.push(`积分包 ${pkg.packageId} 的价格必须大于0`);
    }

    // 检查产品ID配置
    for (const provider of Object.keys(pkg.productIds) as PaymentProvider[]) {
      if (!pkg.productIds[provider]) {
        errors.push(`积分包 ${pkg.packageId} 缺少 ${provider} 的产品ID`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
