import { type Config } from "@repo/config";
import { type PrismaClient } from "@repo/database";
import { logger } from "@repo/logs";
import {
  CREDIT_PAYMENT_CONFIG,
  getSubscriptionCreditAllocation,
  getFeatureCreditConsumption,
  validateCreditPackageConfig,
  type PaymentProvider,
} from "../config/payment-integration";
import { CreditError } from "../types/credit-errors";

/**
 * 积分系统配置管理服务
 * 负责管理积分系统与现有系统配置的集成
 */
export class ConfigurationService {
  constructor(private db: PrismaClient) {}

  /**
   * 验证积分系统配置的完整性
   */
  async validateConfiguration(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 验证积分包配置
      const packageValidation = validateCreditPackageConfig();
      if (!packageValidation.isValid) {
        errors.push(...packageValidation.errors);
      }

      // 验证环境变量
      const envValidation = this.validateEnvironmentVariables();
      if (!envValidation.isValid) {
        errors.push(...envValidation.errors);
      }
      warnings.push(...envValidation.warnings);

      // 验证数据库配置
      const dbValidation = await this.validateDatabaseConfiguration();
      if (!dbValidation.isValid) {
        errors.push(...dbValidation.errors);
      }
      warnings.push(...dbValidation.warnings);

      // 验证支付提供商配置
      const paymentValidation = await this.validatePaymentProviderConfiguration();
      if (!paymentValidation.isValid) {
        errors.push(...paymentValidation.errors);
      }
      warnings.push(...paymentValidation.warnings);

      logger.info("积分系统配置验证完成", {
        isValid: errors.length === 0,
        errorCount: errors.length,
        warningCount: warnings.length,
      });

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    } catch (error) {
      const errorMsg = `配置验证失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      return {
        isValid: false,
        errors: [errorMsg],
        warnings,
      };
    }
  }

  /**
   * 验证环境变量配置
   */
  private validateEnvironmentVariables(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查积分包产品ID环境变量
    for (const pkg of CREDIT_PAYMENT_CONFIG.packages) {
      for (const provider of Object.keys(pkg.productIds) as PaymentProvider[]) {
        const envKey = this.getProductIdEnvKey(pkg.packageId, provider);
        if (!process.env[envKey]) {
          warnings.push(`缺少环境变量: ${envKey}`);
        }
      }
    }

    // 检查支付提供商配置
    const requiredPaymentEnvVars = [
      "STRIPE_SECRET_KEY",
      "STRIPE_WEBHOOK_SECRET",
      "LEMONSQUEEZY_API_KEY",
      "LEMONSQUEEZY_WEBHOOK_SECRET",
      "CHARGEBEE_SITE",
      "CHARGEBEE_API_KEY",
      "POLAR_ACCESS_TOKEN",
      "POLAR_WEBHOOK_SECRET",
      "CREEM_API_KEY",
      "CREEM_WEBHOOK_SECRET",
    ];

    for (const envVar of requiredPaymentEnvVars) {
      if (!process.env[envVar]) {
        warnings.push(`支付提供商环境变量未配置: ${envVar}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证数据库配置
   */
  private async validateDatabaseConfiguration(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 检查积分相关表是否存在
      const tables = [
        "CreditAccount",
        "CreditTransaction", 
        "CreditAllocation",
        "CreditPackage",
      ];

      for (const table of tables) {
        try {
          // 尝试查询表结构
          await this.db.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
        } catch (error) {
          errors.push(`数据库表 ${table} 不存在或无法访问`);
        }
      }

      // 检查 Purchase 表是否支持 CREDIT_PACKAGE 类型
      try {
        const purchaseTypes = await this.db.$queryRaw`
          SELECT DISTINCT type FROM "Purchase" WHERE type = 'CREDIT_PACKAGE'
        `;
        if (!purchaseTypes) {
          warnings.push("Purchase 表可能不支持 CREDIT_PACKAGE 类型");
        }
      } catch (error) {
        warnings.push("无法验证 Purchase 表的 CREDIT_PACKAGE 支持");
      }

    } catch (error) {
      errors.push(`数据库连接失败: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证支付提供商配置
   */
  private async validatePaymentProviderConfiguration(): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查每个积分包在各支付提供商中的产品是否存在
    for (const pkg of CREDIT_PAYMENT_CONFIG.packages) {
      for (const provider of Object.keys(pkg.productIds) as PaymentProvider[]) {
        const productId = pkg.productIds[provider];
        if (!productId) {
          warnings.push(`积分包 ${pkg.packageId} 在 ${provider} 中缺少产品ID`);
          continue;
        }

        // 这里可以添加实际的产品验证逻辑
        // 例如调用支付提供商的API验证产品是否存在
        // 由于需要API调用，这里只做基本检查
        if (productId.length < 3) {
          warnings.push(`积分包 ${pkg.packageId} 在 ${provider} 中的产品ID可能无效: ${productId}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 获取积分包产品ID的环境变量键名
   */
  private getProductIdEnvKey(packageId: string, provider: PaymentProvider): string {
    const packageName = packageId.toUpperCase().replace(/-/g, "_");
    const providerName = provider.toUpperCase().replace(/-/g, "_");
    return `NEXT_PUBLIC_CREDIT_PACKAGE_${packageName}_${providerName}`;
  }

  /**
   * 同步配置到数据库
   */
  async syncConfigurationToDatabase(): Promise<{
    success: boolean;
    results: {
      packages: { created: number; updated: number; errors: string[] };
    };
    errors: string[];
  }> {
    const errors: string[] = [];
    
    try {
      // 同步积分包配置
      const packageResults = await this.syncCreditPackagesToDatabase();

      logger.info("配置同步完成", {
        packages: packageResults,
      });

      return {
        success: errors.length === 0,
        results: {
          packages: packageResults,
        },
        errors,
      };
    } catch (error) {
      const errorMsg = `配置同步失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      errors.push(errorMsg);

      return {
        success: false,
        results: {
          packages: { created: 0, updated: 0, errors: [] },
        },
        errors,
      };
    }
  }

  /**
   * 同步积分包配置到数据库
   */
  private async syncCreditPackagesToDatabase(): Promise<{
    created: number;
    updated: number;
    errors: string[];
  }> {
    const results = {
      created: 0,
      updated: 0,
      errors: [] as string[],
    };

    for (const packageConfig of CREDIT_PAYMENT_CONFIG.packages) {
      try {
        const existingPackage = await this.db.creditPackage.findFirst({
          where: {
            OR: [
              { id: packageConfig.packageId },
              { name: packageConfig.name },
            ],
          },
        });

        const packageData = {
          name: packageConfig.name,
          description: `${packageConfig.credits} 积分包`,
          creditAmount: packageConfig.credits,
          price: packageConfig.price,
          currency: packageConfig.currency,
          validityDays: packageConfig.validityDays,
          productIds: packageConfig.productIds,
          featured: packageConfig.featured,
          sortOrder: packageConfig.sortOrder,
          status: "ACTIVE" as const,
        };

        if (existingPackage) {
          await this.db.creditPackage.update({
            where: { id: existingPackage.id },
            data: packageData,
          });
          results.updated++;
        } else {
          await this.db.creditPackage.create({
            data: {
              id: packageConfig.packageId,
              ...packageData,
            },
          });
          results.created++;
        }
      } catch (error) {
        const errorMsg = `同步积分包 ${packageConfig.packageId} 失败: ${error instanceof Error ? error.message : String(error)}`;
        results.errors.push(errorMsg);
        logger.error(errorMsg);
      }
    }

    return results;
  }

  /**
   * 获取功能的积分消耗配置
   */
  getFeatureConsumptionConfig(featureId: string) {
    return getFeatureCreditConsumption(featureId);
  }

  /**
   * 获取订阅计划的积分分配配置
   */
  getSubscriptionAllocationConfig(planId: string) {
    return getSubscriptionCreditAllocation(planId);
  }

  /**
   * 获取所有积分包配置
   */
  getCreditPackageConfigs() {
    return CREDIT_PAYMENT_CONFIG.packages;
  }

  /**
   * 获取所有功能消耗配置
   */
  getFeatureConsumptionConfigs() {
    return CREDIT_PAYMENT_CONFIG.features;
  }

  /**
   * 获取所有订阅分配配置
   */
  getSubscriptionAllocationConfigs() {
    return CREDIT_PAYMENT_CONFIG.subscriptionAllocations;
  }
}
