"use client";

import { Check, Coins, Crown, Star, Zap } from "lucide-react";
import { useState } from "react";
import { useCreditPackages, usePurchaseCreditPackage } from "../hooks/useCredits";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";

interface CreditPackagesProps {
  className?: string;
  variant?: "grid" | "list";
  showDescription?: boolean;
  onPurchaseSuccess?: () => void;
}

/**
 * 积分包购买组件
 */
export function CreditPackages({
  className,
  variant = "grid",
  showDescription = true,
  onPurchaseSuccess,
}: CreditPackagesProps) {
  const { data: packages, isLoading, error } = useCreditPackages();
  const purchaseMutation = usePurchaseCreditPackage();
  const [selectedProvider, setSelectedProvider] = useState("stripe");

  if (isLoading) {
    return <CreditPackagesSkeleton variant={variant} className={className} />;
  }

  if (error || !packages) {
    return (
      <Card className={cn("border-destructive/20", className)}>
        <CardContent className="p-6 text-center">
          <div className="text-destructive">
            <Coins className="h-8 w-8 mx-auto mb-2" />
            <p>积分包加载失败</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const handlePurchase = async (packageId: string) => {
    try {
      await purchaseMutation.mutateAsync({
        packageId,
        provider: selectedProvider,
        redirectUrl: window.location.href,
      });
      onPurchaseSuccess?.();
    } catch (error) {
      // 错误已在hook中处理
    }
  };

  const sortedPackages = packages
    .filter(pkg => pkg.status === "ACTIVE")
    .sort((a, b) => a.sortOrder - b.sortOrder);

  if (variant === "list") {
    return (
      <div className={cn("space-y-4", className)}>
        {sortedPackages.map((pkg) => (
          <CreditPackageCard
            key={pkg.id}
            package={pkg}
            variant="horizontal"
            showDescription={showDescription}
            onPurchase={() => handlePurchase(pkg.id)}
            isLoading={purchaseMutation.isPending}
          />
        ))}
      </div>
    );
  }

  return (
    <div className={cn("grid gap-6 md:grid-cols-2 lg:grid-cols-3", className)}>
      {sortedPackages.map((pkg) => (
        <CreditPackageCard
          key={pkg.id}
          package={pkg}
          variant="vertical"
          showDescription={showDescription}
          onPurchase={() => handlePurchase(pkg.id)}
          isLoading={purchaseMutation.isPending}
        />
      ))}
    </div>
  );
}

/**
 * 单个积分包卡片组件
 */
interface CreditPackageCardProps {
  package: any; // CreditPackage type
  variant: "vertical" | "horizontal";
  showDescription: boolean;
  onPurchase: () => void;
  isLoading: boolean;
}

function CreditPackageCard({
  package: pkg,
  variant,
  showDescription,
  onPurchase,
  isLoading,
}: CreditPackageCardProps) {
  const getPackageIcon = (creditAmount: number) => {
    if (creditAmount >= 10000) return Crown;
    if (creditAmount >= 5000) return Star;
    if (creditAmount >= 1000) return Zap;
    return Coins;
  };

  const getValuePerCredit = () => {
    return (pkg.price / pkg.creditAmount).toFixed(4);
  };

  const Icon = getPackageIcon(pkg.creditAmount);

  if (variant === "horizontal") {
    return (
      <Card className={cn("relative", pkg.featured && "ring-2 ring-primary")}>
        {pkg.featured && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge status="info" className="bg-primary text-primary-foreground">
              <Star className="h-3 w-3 mr-1" />
              推荐
            </Badge>
          </div>
        )}
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Icon className="h-6 w-6 text-primary" />
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-lg">{pkg.name}</h3>
                {showDescription && (
                  <p className="text-sm text-muted-foreground">{pkg.description}</p>
                )}
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-2xl font-bold text-primary">
                    {pkg.creditAmount.toLocaleString()}
                  </span>
                  <span className="text-sm text-muted-foreground">积分</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">
                ¥{pkg.price.toFixed(2)}
              </div>
              <div className="text-xs text-muted-foreground">
                ¥{getValuePerCredit()}/积分
              </div>
              <Button
                onClick={onPurchase}
                disabled={isLoading}
                className="mt-2"
              >
                {isLoading ? "处理中..." : "立即购买"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("relative", pkg.featured && "ring-2 ring-primary")}>
      {pkg.featured && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge status="info" className="bg-primary text-primary-foreground">
            <Star className="h-3 w-3 mr-1" />
            推荐
          </Badge>
        </div>
      )}
      <CardHeader className="text-center pb-4">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon className="h-8 w-8 text-primary" />
        </div>
        <CardTitle className="text-xl">{pkg.name}</CardTitle>
        {showDescription && (
          <p className="text-sm text-muted-foreground">{pkg.description}</p>
        )}
      </CardHeader>
      <CardContent className="text-center">
        <div className="mb-6">
          <div className="text-3xl font-bold text-primary mb-1">
            {pkg.creditAmount.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">积分</div>
        </div>
        
        <div className="mb-6">
          <div className="text-2xl font-bold mb-1">
            ¥{pkg.price.toFixed(2)}
          </div>
          <div className="text-xs text-muted-foreground">
            ¥{getValuePerCredit()}/积分
          </div>
        </div>

        {pkg.validityDays && (
          <div className="mb-4 text-xs text-muted-foreground">
            有效期：{pkg.validityDays} 天
          </div>
        )}

        <Button
          onClick={onPurchase}
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? "处理中..." : "立即购买"}
        </Button>
      </CardContent>
    </Card>
  );
}

/**
 * 积分包骨架屏
 */
function CreditPackagesSkeleton({
  variant = "grid",
  className,
}: {
  variant?: "grid" | "list";
  className?: string;
}) {
  const skeletonCount = variant === "grid" ? 3 : 4;

  if (variant === "list") {
    return (
      <div className={cn("space-y-4", className)}>
        {Array.from({ length: skeletonCount }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Skeleton className="w-12 h-12 rounded-lg" />
                  <div>
                    <Skeleton className="h-5 w-24 mb-2" />
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </div>
                <div className="text-right">
                  <Skeleton className="h-8 w-16 mb-1" />
                  <Skeleton className="h-3 w-12 mb-2" />
                  <Skeleton className="h-9 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("grid gap-6 md:grid-cols-2 lg:grid-cols-3", className)}>
      {Array.from({ length: skeletonCount }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="text-center pb-4">
            <Skeleton className="w-16 h-16 rounded-full mx-auto mb-4" />
            <Skeleton className="h-6 w-24 mx-auto mb-2" />
            <Skeleton className="h-4 w-32 mx-auto" />
          </CardHeader>
          <CardContent className="text-center">
            <Skeleton className="h-9 w-20 mx-auto mb-1" />
            <Skeleton className="h-4 w-12 mx-auto mb-6" />
            <Skeleton className="h-8 w-16 mx-auto mb-1" />
            <Skeleton className="h-3 w-12 mx-auto mb-6" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
