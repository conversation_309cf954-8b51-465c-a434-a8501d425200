import { describe, it, expect, beforeEach, vi } from 'vitest';
import { PaymentIntegrationService } from '../../src/services/PaymentIntegrationService';
import { CreditService } from '../../src/services/CreditService';
import { CreditPackageService } from '../../src/services/CreditPackageService';
import { TestFactories } from '../test-factories';

// Mock Prisma Client
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findMany: vi.fn(),
  },
  creditPackage: {
    findFirst: vi.fn(),
    findMany: vi.fn(),
  },
  $transaction: vi.fn(),
} as any;

// Mock payment providers
const mockStripeClient = {
  checkout: {
    sessions: {
      create: vi.fn(),
      retrieve: vi.fn(),
    },
  },
  paymentIntents: {
    create: vi.fn(),
    confirm: vi.fn(),
    retrieve: vi.fn(),
  },
  webhooks: {
    constructEvent: vi.fn(),
  },
} as any;

const mockLemonSqueezyClient = {
  createCheckout: vi.fn(),
  getOrder: vi.fn(),
  processWebhook: vi.fn(),
} as any;

describe('Integration Tests: Payment System Integration', () => {
  let paymentIntegrationService: PaymentIntegrationService;
  let creditService: CreditService;
  let packageService: CreditPackageService;

  beforeEach(() => {
    vi.clearAllMocks();
    
    creditService = new CreditService(mockPrismaClient);
    packageService = new CreditPackageService(mockPrismaClient);
    paymentIntegrationService = new PaymentIntegrationService(
      mockPrismaClient,
      creditService,
      packageService
    );
  });

  describe('Stripe Payment Integration', () => {
    it('should process successful Stripe credit package purchase', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        userId: 'user-123',
        currentBalance: 500,
      });
      const mockPackage = TestFactories.createCreditPackage({
        id: 'pkg-stripe-1000',
        credits: 1000,
        price: 9.99,
        currency: 'USD',
      });
      const mockTransaction = TestFactories.createCreditTransaction({
        amount: 1000,
        type: 'EARNED_PURCHASE',
        balanceAfter: 1500,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
      mockPrismaClient.creditAccount.update.mockResolvedValue({
        ...mockAccount,
        currentBalance: 1500,
      });

      const result = await paymentIntegrationService.handleCreditPackagePurchase({
        productId: 'stripe-prod-1000',
        provider: 'stripe',
        customerId: 'cus_stripe123',
        purchaseId: 'purchase-123',
        userId: 'user-123',
        metadata: {
          sessionId: 'cs_stripe_session_123',
        },
      });

      expect(result.transaction).toEqual(mockTransaction);
      expect(result.package).toEqual(mockPackage);
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            amount: 1000,
            type: 'EARNED_PURCHASE',
            metadata: expect.objectContaining({
              provider: 'stripe',
              purchaseId: 'purchase-123',
            }),
          }),
        })
      );
    });

    it('should handle Stripe payment failure', async () => {
      const mockAccount = TestFactories.createCreditAccount();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await paymentIntegrationService.handleCreditPackagePurchase({
          productId: 'stripe-prod-invalid',
          provider: 'stripe',
          customerId: 'cus_stripe123',
          purchaseId: 'purchase-failed',
          userId: 'user-123',
          metadata: {
            paymentStatus: 'failed',
            errorCode: 'card_declined',
          },
        });
      }).rejects.toThrow('Payment failed: card_declined');
    });

    it('should process Stripe webhook events correctly', async () => {
      const webhookPayload = {
        id: 'evt_stripe_123',
        type: 'checkout.session.completed',
        data: {
          object: {
            id: 'cs_stripe_session_123',
            payment_status: 'paid',
            customer: 'cus_stripe123',
            metadata: {
              userId: 'user-123',
              packageId: 'pkg-stripe-1000',
            },
          },
        },
      };

      const mockAccount = TestFactories.createCreditAccount({ userId: 'user-123' });
      const mockPackage = TestFactories.createCreditPackage({ credits: 1000 });
      const mockTransaction = TestFactories.createCreditTransaction({ amount: 1000 });

      mockStripeClient.webhooks.constructEvent.mockReturnValue(webhookPayload);
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      const result = await paymentIntegrationService.processStripeWebhook(
        JSON.stringify(webhookPayload),
        'stripe_webhook_signature'
      );

      expect(result.processed).toBe(true);
      expect(result.transaction).toEqual(mockTransaction);
    });

    it('should handle Stripe refund processing', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1500,
      });
      const mockRefundTransaction = TestFactories.createCreditTransaction({
        amount: -1000,
        type: 'REFUNDED_PURCHASE',
        balanceAfter: 500,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockRefundTransaction);

      const result = await paymentIntegrationService.processRefund({
        provider: 'stripe',
        purchaseId: 'purchase-123',
        refundAmount: 1000,
        reason: 'Customer request',
        userId: 'user-123',
      });

      expect(result.transaction).toEqual(mockRefundTransaction);
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            amount: -1000,
            type: 'REFUNDED_PURCHASE',
          }),
        })
      );
    });
  });

  describe('LemonSqueezy Payment Integration', () => {
    it('should process successful LemonSqueezy credit package purchase', async () => {
      const mockAccount = TestFactories.createCreditAccount({ userId: 'user-123' });
      const mockPackage = TestFactories.createCreditPackage({ credits: 2000 });
      const mockTransaction = TestFactories.createCreditTransaction({ amount: 2000 });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      mockLemonSqueezyClient.getOrder.mockResolvedValue({
        id: 'order-ls-123',
        status: 'paid',
        product_id: 'ls-prod-2000',
        customer_id: 'cus-ls-123',
        total: 19.99,
      });

      const result = await paymentIntegrationService.handleCreditPackagePurchase({
        productId: 'ls-prod-2000',
        provider: 'lemonsqueezy',
        customerId: 'cus-ls-123',
        purchaseId: 'order-ls-123',
        userId: 'user-123',
      });

      expect(result.transaction).toEqual(mockTransaction);
      expect(result.package).toEqual(mockPackage);
    });

    it('should handle LemonSqueezy webhook validation', async () => {
      const webhookPayload = {
        meta: {
          event_name: 'order_created',
        },
        data: {
          id: 'order-ls-123',
          attributes: {
            status: 'paid',
            product_id: 'ls-prod-2000',
            customer_id: 'cus-ls-123',
            user_email: '<EMAIL>',
          },
        },
      };

      mockLemonSqueezyClient.processWebhook.mockReturnValue({
        isValid: true,
        event: webhookPayload,
      });

      const result = await paymentIntegrationService.processLemonSqueezyWebhook(
        JSON.stringify(webhookPayload),
        'ls_webhook_signature'
      );

      expect(result.processed).toBe(true);
    });
  });

  describe('Multi-Provider Payment Handling', () => {
    it('should route payments to correct provider', async () => {
      const providers = ['stripe', 'lemonsqueezy', 'chargebee', 'polar'];
      
      for (const provider of providers) {
        const mockAccount = TestFactories.createCreditAccount();
        const mockPackage = TestFactories.createCreditPackage();
        const mockTransaction = TestFactories.createCreditTransaction();

        mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
        mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);
        mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

        const result = await paymentIntegrationService.handleCreditPackagePurchase({
          productId: `${provider}-prod-1000`,
          provider: provider as any,
          customerId: `cus-${provider}-123`,
          purchaseId: `purchase-${provider}-123`,
          userId: 'user-123',
        });

        expect(result.transaction).toEqual(mockTransaction);
      }
    });

    it('should handle provider-specific error codes', async () => {
      const providerErrors = [
        { provider: 'stripe', errorCode: 'card_declined', expectedMessage: 'Card was declined' },
        { provider: 'lemonsqueezy', errorCode: 'payment_failed', expectedMessage: 'Payment processing failed' },
        { provider: 'chargebee', errorCode: 'subscription_cancelled', expectedMessage: 'Subscription was cancelled' },
      ];

      for (const { provider, errorCode, expectedMessage } of providerErrors) {
        await expect(async () => {
          await paymentIntegrationService.handlePaymentError({
            provider: provider as any,
            errorCode,
            purchaseId: 'purchase-123',
          });
        }).rejects.toThrow(expectedMessage);
      }
    });

    it('should maintain transaction consistency across providers', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 0 });
      
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return await callback(mockPrismaClient);
      });

      const purchases = [
        { provider: 'stripe', credits: 1000 },
        { provider: 'lemonsqueezy', credits: 2000 },
        { provider: 'chargebee', credits: 500 },
      ];

      let expectedBalance = 0;
      for (const purchase of purchases) {
        const mockTransaction = TestFactories.createCreditTransaction({
          amount: purchase.credits,
          balanceAfter: expectedBalance + purchase.credits,
        });
        
        mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);
        
        await paymentIntegrationService.handleCreditPackagePurchase({
          productId: `${purchase.provider}-prod-${purchase.credits}`,
          provider: purchase.provider as any,
          customerId: `cus-${purchase.provider}-123`,
          purchaseId: `purchase-${purchase.provider}-123`,
          userId: 'user-123',
        });

        expectedBalance += purchase.credits;
      }

      // Verify all transactions were processed
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(3);
    });
  });

  describe('Payment Failure Recovery', () => {
    it('should retry failed payment processing', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockPackage = TestFactories.createCreditPackage();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);
      
      // First attempt fails, second succeeds
      mockPrismaClient.creditTransaction.create
        .mockRejectedValueOnce(new Error('Database connection failed'))
        .mockResolvedValueOnce(TestFactories.createCreditTransaction());

      const result = await paymentIntegrationService.handleCreditPackagePurchase({
        productId: 'stripe-prod-1000',
        provider: 'stripe',
        customerId: 'cus-stripe123',
        purchaseId: 'purchase-retry-123',
        userId: 'user-123',
      });

      expect(result.transaction).toBeDefined();
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledTimes(2);
    });

    it('should handle partial payment failures', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Simulate partial payment (less than expected amount)
      await expect(async () => {
        await paymentIntegrationService.handleCreditPackagePurchase({
          productId: 'stripe-prod-1000',
          provider: 'stripe',
          customerId: 'cus-stripe123',
          purchaseId: 'purchase-partial-123',
          userId: 'user-123',
          metadata: {
            expectedAmount: 1000,
            actualAmount: 500, // Partial payment
          },
        });
      }).rejects.toThrow('Partial payment detected');
    });

    it('should rollback failed transactions', async () => {
      const mockAccount = TestFactories.createCreditAccount({ currentBalance: 1000 });
      
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        throw new Error('Transaction failed');
      });

      await expect(async () => {
        await paymentIntegrationService.handleCreditPackagePurchase({
          productId: 'stripe-prod-1000',
          provider: 'stripe',
          customerId: 'cus-stripe123',
          purchaseId: 'purchase-rollback-123',
          userId: 'user-123',
        });
      }).rejects.toThrow('Transaction failed');

      // Verify account balance wasn't modified
      const finalAccount = await creditService.getAccount({ userId: 'user-123' });
      expect(finalAccount?.currentBalance).toBe(1000);
    });
  });

  describe('Payment Security and Validation', () => {
    it('should validate webhook signatures', async () => {
      const invalidSignature = 'invalid_signature';
      
      mockStripeClient.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      await expect(async () => {
        await paymentIntegrationService.processStripeWebhook(
          '{"test": "payload"}',
          invalidSignature
        );
      }).rejects.toThrow('Invalid signature');
    });

    it('should prevent duplicate payment processing', async () => {
      const duplicatePurchaseId = 'purchase-duplicate-123';
      
      // Mock existing transaction with same purchase ID
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue([
        TestFactories.createCreditTransaction({
          metadata: { purchaseId: duplicatePurchaseId },
        }),
      ]);

      await expect(async () => {
        await paymentIntegrationService.handleCreditPackagePurchase({
          productId: 'stripe-prod-1000',
          provider: 'stripe',
          customerId: 'cus-stripe123',
          purchaseId: duplicatePurchaseId,
          userId: 'user-123',
        });
      }).rejects.toThrow('Duplicate purchase ID detected');
    });

    it('should validate payment amounts', async () => {
      const mockPackage = TestFactories.createCreditPackage({
        price: 9.99,
        currency: 'USD',
      });

      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);

      await expect(async () => {
        await paymentIntegrationService.validatePaymentAmount({
          packageId: mockPackage.id,
          paidAmount: 5.00, // Less than package price
          currency: 'USD',
        });
      }).rejects.toThrow('Payment amount mismatch');
    });

    it('should handle currency conversion', async () => {
      const mockPackage = TestFactories.createCreditPackage({
        price: 9.99,
        currency: 'USD',
      });

      const convertedAmount = await paymentIntegrationService.convertCurrency({
        amount: 9.99,
        fromCurrency: 'USD',
        toCurrency: 'EUR',
        exchangeRate: 0.85,
      });

      expect(convertedAmount).toBeCloseTo(8.49, 2);
    });
  });
});
