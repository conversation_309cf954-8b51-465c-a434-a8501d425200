"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@ui/components/use-toast";

// 积分账户类型
export interface CreditAccount {
  id: string;
  userId?: string;
  organizationId?: string;
  balance: number;
  totalEarned: number;
  totalConsumed: number;
  createdAt: string;
  updatedAt: string;
}

// 积分交易类型
export interface CreditTransaction {
  id: string;
  accountId: string;
  amount: number;
  type: string;
  reason: string;
  status: string;
  expiresAt?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

// 积分包类型
export interface CreditPackage {
  id: string;
  name: string;
  description: string;
  creditAmount: number;
  price: number;
  currency: string;
  validityDays?: number;
  featured: boolean;
  sortOrder: number;
  status: string;
  productIds: Record<string, string>;
}

// API 查询键
export const creditQueryKeys = {
  all: ["credits"] as const,
  account: () => [...creditQueryKeys.all, "account"] as const,
  transactions: (accountId?: string) => 
    [...creditQueryKeys.all, "transactions", accountId] as const,
  packages: () => [...creditQueryKeys.all, "packages"] as const,
  allocations: (accountId?: string) => 
    [...creditQueryKeys.all, "allocations", accountId] as const,
};

/**
 * 获取积分账户信息
 */
export function useCreditAccount() {
  const { toast } = useToast();

  return useQuery({
    queryKey: creditQueryKeys.account(),
    queryFn: async (): Promise<CreditAccount> => {
      const response = await fetch("/api/credits/account");
      if (!response.ok) {
        throw new Error("获取积分账户失败");
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 5, // 5分钟
    retry: (failureCount, error) => {
      if (error instanceof Error && error.message.includes("404")) {
        return false; // 账户不存在时不重试
      }
      return failureCount < 3;
    },
    meta: {
      errorMessage: "获取积分账户信息失败",
    },
  });
}

/**
 * 获取积分交易记录
 */
export function useCreditTransactions(accountId?: string, options?: {
  page?: number;
  limit?: number;
  type?: string;
}) {
  return useQuery({
    queryKey: [...creditQueryKeys.transactions(accountId), options],
    queryFn: async (): Promise<{
      transactions: CreditTransaction[];
      total: number;
      page: number;
      limit: number;
    }> => {
      const params = new URLSearchParams();
      if (options?.page) params.append("page", options.page.toString());
      if (options?.limit) params.append("limit", options.limit.toString());
      if (options?.type) params.append("type", options.type);

      const response = await fetch(`/api/credits/transactions?${params}`);
      if (!response.ok) {
        throw new Error("获取积分交易记录失败");
      }
      return response.json();
    },
    enabled: !!accountId,
    staleTime: 1000 * 60 * 2, // 2分钟
  });
}

/**
 * 获取积分包列表
 */
export function useCreditPackages() {
  return useQuery({
    queryKey: creditQueryKeys.packages(),
    queryFn: async (): Promise<CreditPackage[]> => {
      const response = await fetch("/api/credits/packages");
      if (!response.ok) {
        throw new Error("获取积分包列表失败");
      }
      return response.json();
    },
    staleTime: 1000 * 60 * 10, // 10分钟
  });
}

/**
 * 消耗积分
 */
export function useConsumeCredits() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (params: {
      amount: number;
      reason: string;
      featureId?: string;
      metadata?: Record<string, any>;
    }) => {
      const response = await fetch("/api/credits/transactions/consume", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "积分消耗失败");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // 刷新积分账户和交易记录
      queryClient.invalidateQueries({ queryKey: creditQueryKeys.account() });
      queryClient.invalidateQueries({ queryKey: creditQueryKeys.transactions() });
      
      toast({
        title: "积分消耗成功",
        description: `消耗了 ${variables.amount} 积分：${variables.reason}`,
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: "积分消耗失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    },
  });
}

/**
 * 购买积分包
 */
export function usePurchaseCreditPackage() {
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (params: {
      packageId: string;
      provider: string;
      redirectUrl?: string;
    }) => {
      const response = await fetch("/api/credits/packages/purchase", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "积分包购买失败");
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data.checkoutUrl) {
        // 跳转到支付页面
        window.location.href = data.checkoutUrl;
      }
    },
    onError: (error) => {
      toast({
        title: "积分包购买失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    },
  });
}

/**
 * 检查功能是否有足够积分
 */
export function useCheckFeatureCredits() {
  const { data: account } = useCreditAccount();

  return {
    checkCredits: (requiredCredits: number) => {
      if (!account) return { canUse: false, reason: "账户信息加载中" };
      
      if (account.balance < requiredCredits) {
        return {
          canUse: false,
          reason: `积分不足，需要 ${requiredCredits} 积分，当前余额 ${account.balance} 积分`,
        };
      }

      return { canUse: true };
    },
    balance: account?.balance || 0,
    isLoading: !account,
  };
}

/**
 * 积分余额实时更新Hook
 */
export function useCreditBalance() {
  const { data: account, isLoading, error } = useCreditAccount();

  return {
    balance: account?.balance || 0,
    totalEarned: account?.totalEarned || 0,
    totalConsumed: account?.totalConsumed || 0,
    isLoading,
    error,
    hasAccount: !!account,
  };
}

/**
 * 积分统计Hook
 */
export function useCreditStats() {
  const { data: account } = useCreditAccount();
  const { data: transactionsData } = useCreditTransactions(account?.id, {
    limit: 100, // 获取最近100条记录用于统计
  });

  const stats = {
    balance: account?.balance || 0,
    totalEarned: account?.totalEarned || 0,
    totalConsumed: account?.totalConsumed || 0,
    recentTransactions: transactionsData?.transactions?.slice(0, 5) || [],
    monthlyConsumption: 0,
    weeklyEarned: 0,
  };

  // 计算本月消耗和本周获得
  if (transactionsData?.transactions) {
    const now = new Date();
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    stats.monthlyConsumption = transactionsData.transactions
      .filter(t => 
        new Date(t.createdAt) >= monthStart && 
        t.type.startsWith("CONSUMED_")
      )
      .reduce((sum, t) => sum + t.amount, 0);

    stats.weeklyEarned = transactionsData.transactions
      .filter(t => 
        new Date(t.createdAt) >= weekStart && 
        t.type.startsWith("EARNED_")
      )
      .reduce((sum, t) => sum + t.amount, 0);
  }

  return stats;
}
