// 监控相关类型定义

export interface MonitoringConfig {
  sentry: {
    dsn: string;
    environment: string;
    tracesSampleRate: number;
    profilesSampleRate: number;
  };
  analytics: {
    enabled: boolean;
    vercel: boolean;
    umami: {
      enabled: boolean;
      websiteId?: string;
      url?: string;
    };
  };
  metrics: {
    enabled: boolean;
    customEvents: boolean;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    pretty: boolean;
  };
}

export interface CustomMetric {
  name: string;
  value: number;
  tags?: Record<string, string>;
  timestamp?: Date;
}

export interface BusinessMetric {
  event: string;
  userId?: string;
  organizationId?: string;
  properties?: Record<string, any>;
}

export interface PerformanceMetric {
  name: string;
  duration: number;
  success: boolean;
  metadata?: Record<string, any>;
}

export interface ErrorContext {
  userId?: string;
  organizationId?: string;
  requestId?: string;
  userAgent?: string;
  url?: string;
  method?: string;
  statusCode?: number;
}
