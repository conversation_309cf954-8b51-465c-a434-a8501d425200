import { CreditService, CreditAllocationService, CreditPackageService } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";
import { authMiddleware } from "../../middleware/auth";
import {
  adminAccountQuerySchema,
  adminAnalyticsQuerySchema,
  creditAccountResponseSchema,
  errorResponseSchema,
  paginatedResponseSchema,
} from "./schemas";

// 创建服务实例
const creditService = new CreditService(db);
const allocationService = new CreditAllocationService(db, creditService);
const packageService = new CreditPackageService(db, creditService);

/**
 * 积分系统管理员路由
 */
export const adminRouter = new Hono()
  // 获取所有积分账户（管理员）
  .get(
    "/accounts",
    authMiddleware,
    adminMiddleware,
    validator("query", adminAccountQuerySchema),
    describeRoute({
      tags: ["Credits Admin"],
      summary: "获取所有积分账户",
      description: "获取系统中所有积分账户的列表（仅管理员）",
      responses: {
        200: {
          description: "积分账户列表",
          content: {
            "application/json": {
              schema: resolver(paginatedResponseSchema(creditAccountResponseSchema)),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const query = c.req.valid("query");

        // 构建查询参数
        const queryParams = {
          status: query.status,
          minBalance: query.minBalance,
          maxBalance: query.maxBalance,
          hasOrganization: query.hasOrganization,
          createdAfter: query.createdAfter ? new Date(query.createdAfter) : undefined,
          createdBefore: query.createdBefore ? new Date(query.createdBefore) : undefined,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
          sortOrder: query.sortOrder,
        };

        const result = await creditService.getAllAccounts(queryParams);

        const response = {
          data: result.data.map(account => ({
            id: account.id,
            userId: account.userId,
            organizationId: account.organizationId,
            currentBalance: account.currentBalance,
            totalEarned: account.totalEarned,
            totalSpent: account.totalSpent,
            status: account.status,
            createdAt: account.createdAt.toISOString(),
            updatedAt: account.updatedAt.toISOString(),
          })),
          pagination: result.pagination,
        };

        logger.info("管理员查询积分账户列表", {
          resultCount: result.data.length,
          filters: queryParams,
          adminUserId: c.get("user")?.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分账户列表失败", {
          error: error instanceof Error ? error.message : String(error),
          adminUserId: c.get("user")?.id,
        });

        throw new HTTPException(500, { message: "获取积分账户列表失败" });
      }
    }
  )

  // 获取积分系统分析数据（管理员）
  .get(
    "/analytics",
    authMiddleware,
    adminMiddleware,
    validator("query", adminAnalyticsQuerySchema),
    describeRoute({
      tags: ["Credits Admin"],
      summary: "获取积分系统分析数据",
      description: "获取积分系统的统计和分析数据（仅管理员）",
      responses: {
        200: {
          description: "分析数据",
          content: {
            "application/json": {
              schema: resolver(z.object({
                overview: z.object({
                  totalAccounts: z.number(),
                  activeAccounts: z.number(),
                  totalCreditsIssued: z.number(),
                  totalCreditsConsumed: z.number(),
                  totalCreditsBalance: z.number(),
                }),
                transactions: z.object({
                  totalTransactions: z.number(),
                  recentTransactions: z.number(),
                  topConsumingFeatures: z.array(z.object({
                    featureId: z.string(),
                    totalConsumed: z.number(),
                    transactionCount: z.number(),
                  })),
                }),
                packages: z.object({
                  totalPackages: z.number(),
                  activePackages: z.number(),
                  totalSales: z.number(),
                  totalRevenue: z.number(),
                  topSellingPackages: z.array(z.object({
                    id: z.string(),
                    name: z.string(),
                    salesCount: z.number(),
                    revenue: z.number(),
                  })),
                }),
                allocations: z.object({
                  totalAllocations: z.number(),
                  activeAllocations: z.number(),
                  totalAllocated: z.number(),
                  upcomingAllocations: z.number(),
                }),
              })),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const query = c.req.valid("query");
        const user = c.get("user");

        // 设置时间范围
        const startDate = query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 默认30天前
        const endDate = query.endDate ? new Date(query.endDate) : new Date();

        // 获取账户概览数据
        const accountStats = await creditService.getAccountStatistics({
          startDate,
          endDate,
        });

        // 获取交易统计数据
        const transactionStats = await creditService.getTransactionStatistics({
          startDate,
          endDate,
          includeFeatureBreakdown: true,
        });

        // 获取积分包统计数据
        const packageStats = await packageService.getPackageStatistics({
          startDate,
          endDate,
          includeTopSelling: true,
        });

        // 获取分配统计数据
        const allocationStats = await allocationService.getAllocationStatistics({
          startDate,
          endDate,
        });

        const response = {
          overview: {
            totalAccounts: accountStats.totalAccounts,
            activeAccounts: accountStats.activeAccounts,
            totalCreditsIssued: accountStats.totalCreditsIssued,
            totalCreditsConsumed: accountStats.totalCreditsConsumed,
            totalCreditsBalance: accountStats.totalCreditsBalance,
          },
          transactions: {
            totalTransactions: transactionStats.totalTransactions,
            recentTransactions: transactionStats.recentTransactions,
            topConsumingFeatures: transactionStats.topConsumingFeatures || [],
          },
          packages: {
            totalPackages: packageStats.totalPackages,
            activePackages: packageStats.activePackages,
            totalSales: packageStats.totalSales,
            totalRevenue: packageStats.totalRevenue,
            topSellingPackages: packageStats.topSellingPackages || [],
          },
          allocations: {
            totalAllocations: allocationStats.totalAllocations,
            activeAllocations: allocationStats.activeAllocations,
            totalAllocated: allocationStats.totalAllocated,
            upcomingAllocations: allocationStats.upcomingAllocations,
          },
        };

        logger.info("管理员查询积分系统分析数据", {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          adminUserId: user?.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分系统分析数据失败", {
          error: error instanceof Error ? error.message : String(error),
          adminUserId: c.get("user")?.id,
        });

        throw new HTTPException(500, { message: "获取分析数据失败" });
      }
    }
  )

  // 系统维护操作（管理员）
  .post(
    "/maintenance/cleanup-expired",
    authMiddleware,
    adminMiddleware,
    describeRoute({
      tags: ["Credits Admin"],
      summary: "清理过期积分",
      description: "清理系统中所有过期的积分（仅管理员）",
      responses: {
        200: {
          description: "清理完成",
          content: {
            "application/json": {
              schema: resolver(z.object({
                cleaned: z.number(),
                totalAmount: z.number(),
                message: z.string(),
              })),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");

        const result = await creditService.cleanupExpiredCredits();

        logger.info("管理员执行过期积分清理", {
          cleaned: result.cleaned,
          totalAmount: result.totalAmount,
          adminUserId: user?.id,
        });

        return c.json({
          cleaned: result.cleaned,
          totalAmount: result.totalAmount,
          message: `成功清理 ${result.cleaned} 条过期积分记录，总计 ${result.totalAmount} 积分`,
        });
      } catch (error) {
        logger.error("清理过期积分失败", {
          error: error instanceof Error ? error.message : String(error),
          adminUserId: c.get("user")?.id,
        });

        throw new HTTPException(500, { message: "清理过期积分失败" });
      }
    }
  )

  // 重新计算账户余额（管理员）
  .post(
    "/maintenance/recalculate-balances",
    authMiddleware,
    adminMiddleware,
    describeRoute({
      tags: ["Credits Admin"],
      summary: "重新计算账户余额",
      description: "重新计算所有积分账户的余额（仅管理员）",
      responses: {
        200: {
          description: "重新计算完成",
          content: {
            "application/json": {
              schema: resolver(z.object({
                processed: z.number(),
                updated: z.number(),
                message: z.string(),
              })),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");

        const result = await creditService.recalculateAllBalances();

        logger.info("管理员执行账户余额重新计算", {
          processed: result.processed,
          updated: result.updated,
          adminUserId: user?.id,
        });

        return c.json({
          processed: result.processed,
          updated: result.updated,
          message: `处理了 ${result.processed} 个账户，更新了 ${result.updated} 个账户的余额`,
        });
      } catch (error) {
        logger.error("重新计算账户余额失败", {
          error: error instanceof Error ? error.message : String(error),
          adminUserId: c.get("user")?.id,
        });

        throw new HTTPException(500, { message: "重新计算账户余额失败" });
      }
    }
  );
