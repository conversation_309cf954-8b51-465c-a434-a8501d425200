import { type PrismaClient } from "@repo/database";
import { logger } from "@repo/logs";
import { CreditAllocationService } from "./CreditAllocationService";
import { CreditService } from "./CreditService";
import { CreditError } from "../types/credit-errors";

/**
 * 积分系统定时任务服务
 * 负责处理积分分配的自动执行和系统维护任务
 */
export class SchedulerService {
  private creditService: CreditService;
  private allocationService: CreditAllocationService;

  constructor(
    private db: PrismaClient,
    creditService?: CreditService,
    allocationService?: CreditAllocationService
  ) {
    this.creditService = creditService || new CreditService(db);
    this.allocationService = allocationService || new CreditAllocationService(db, this.creditService);
  }

  /**
   * 执行所有到期的积分分配
   * 建议每小时运行一次
   */
  async executePendingAllocations(): Promise<{
    processed: number;
    successful: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      processed: 0,
      successful: 0,
      failed: 0,
      errors: [] as string[],
    };

    try {
      logger.info("开始执行待处理的积分分配");

      // 获取所有到期的分配规则
      const pendingAllocations = await this.db.creditAllocation.findMany({
        where: {
          status: "ACTIVE",
          nextExecutionAt: {
            lte: new Date(),
          },
        },
        include: {
          account: {
            include: {
              user: true,
              organization: true,
            },
          },
        },
        orderBy: {
          nextExecutionAt: "asc",
        },
      });

      logger.info(`找到 ${pendingAllocations.length} 个待处理的积分分配`);

      for (const allocation of pendingAllocations) {
        results.processed++;

        try {
          // 执行分配
          await this.allocationService.executeAllocation(allocation.id);
          results.successful++;

          logger.debug("积分分配执行成功", {
            allocationId: allocation.id,
            accountId: allocation.accountId,
            amount: allocation.amount,
          });
        } catch (error) {
          results.failed++;
          const errorMsg = `分配 ${allocation.id} 执行失败: ${error instanceof Error ? error.message : String(error)}`;
          results.errors.push(errorMsg);
          logger.error(errorMsg, {
            allocationId: allocation.id,
            accountId: allocation.accountId,
          });
        }
      }

      logger.info("积分分配执行完成", results);
      return results;
    } catch (error) {
      const errorMsg = `执行积分分配失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      results.errors.push(errorMsg);
      return results;
    }
  }

  /**
   * 清理过期的积分
   * 建议每天运行一次
   */
  async cleanupExpiredCredits(): Promise<{
    expiredTransactions: number;
    reclaimedCredits: number;
    errors: string[];
  }> {
    const results = {
      expiredTransactions: 0,
      reclaimedCredits: 0,
      errors: [] as string[],
    };

    try {
      logger.info("开始清理过期积分");

      // 查找所有过期的积分交易
      const expiredTransactions = await this.db.creditTransaction.findMany({
        where: {
          expiresAt: {
            lte: new Date(),
          },
          status: "COMPLETED",
          type: {
            in: ["EARNED_PURCHASE", "EARNED_SUBSCRIPTION", "EARNED_BONUS", "EARNED_REFERRAL"],
          },
        },
        include: {
          account: true,
        },
      });

      logger.info(`找到 ${expiredTransactions.length} 个过期的积分交易`);

      for (const transaction of expiredTransactions) {
        try {
          // 创建过期回收交易
          await this.creditService.consumeCredits({
            accountId: transaction.accountId,
            amount: transaction.amount,
            reason: `积分过期回收: ${transaction.reason}`,
            type: "EXPIRED",
            metadata: {
              originalTransactionId: transaction.id,
              expiredAt: transaction.expiresAt,
            },
          });

          // 更新原交易状态
          await this.db.creditTransaction.update({
            where: { id: transaction.id },
            data: { status: "EXPIRED" },
          });

          results.expiredTransactions++;
          results.reclaimedCredits += transaction.amount;

          logger.debug("过期积分回收成功", {
            transactionId: transaction.id,
            accountId: transaction.accountId,
            amount: transaction.amount,
          });
        } catch (error) {
          const errorMsg = `回收过期积分失败 ${transaction.id}: ${error instanceof Error ? error.message : String(error)}`;
          results.errors.push(errorMsg);
          logger.error(errorMsg);
        }
      }

      logger.info("过期积分清理完成", results);
      return results;
    } catch (error) {
      const errorMsg = `清理过期积分失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      results.errors.push(errorMsg);
      return results;
    }
  }

  /**
   * 停用过期的分配规则
   * 建议每天运行一次
   */
  async deactivateExpiredAllocations(): Promise<{
    deactivated: number;
    errors: string[];
  }> {
    const results = {
      deactivated: 0,
      errors: [] as string[],
    };

    try {
      logger.info("开始停用过期的分配规则");

      // 查找所有过期的分配规则
      const expiredAllocations = await this.db.creditAllocation.findMany({
        where: {
          status: "ACTIVE",
          endsAt: {
            lte: new Date(),
          },
        },
      });

      logger.info(`找到 ${expiredAllocations.length} 个过期的分配规则`);

      for (const allocation of expiredAllocations) {
        try {
          await this.allocationService.deactivateAllocation({
            allocationId: allocation.id,
            reason: "分配规则已过期",
          });

          results.deactivated++;

          logger.debug("分配规则停用成功", {
            allocationId: allocation.id,
            accountId: allocation.accountId,
          });
        } catch (error) {
          const errorMsg = `停用分配规则失败 ${allocation.id}: ${error instanceof Error ? error.message : String(error)}`;
          results.errors.push(errorMsg);
          logger.error(errorMsg);
        }
      }

      logger.info("过期分配规则停用完成", results);
      return results;
    } catch (error) {
      const errorMsg = `停用过期分配规则失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      results.errors.push(errorMsg);
      return results;
    }
  }

  /**
   * 生成积分系统统计报告
   * 建议每天运行一次
   */
  async generateDailyReport(): Promise<{
    totalAccounts: number;
    totalCredits: number;
    dailyTransactions: number;
    activeAllocations: number;
    topConsumers: Array<{
      accountId: string;
      userId?: string;
      organizationId?: string;
      totalConsumed: number;
    }>;
    errors: string[];
  }> {
    const results = {
      totalAccounts: 0,
      totalCredits: 0,
      dailyTransactions: 0,
      activeAllocations: 0,
      topConsumers: [] as Array<{
        accountId: string;
        userId?: string;
        organizationId?: string;
        totalConsumed: number;
      }>,
      errors: [] as string[],
    };

    try {
      logger.info("开始生成积分系统日报");

      // 统计总账户数
      results.totalAccounts = await this.db.creditAccount.count();

      // 统计总积分数
      const totalCreditsResult = await this.db.creditAccount.aggregate({
        _sum: {
          balance: true,
        },
      });
      results.totalCredits = totalCreditsResult._sum.balance || 0;

      // 统计今日交易数
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      results.dailyTransactions = await this.db.creditTransaction.count({
        where: {
          createdAt: {
            gte: today,
            lt: tomorrow,
          },
        },
      });

      // 统计活跃分配规则数
      results.activeAllocations = await this.db.creditAllocation.count({
        where: {
          status: "ACTIVE",
        },
      });

      // 统计消费最多的账户（过去30天）
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const topConsumersData = await this.db.creditTransaction.groupBy({
        by: ["accountId"],
        where: {
          type: {
            in: ["CONSUMED_FEATURE", "CONSUMED_API", "CONSUMED_STORAGE"],
          },
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
        _sum: {
          amount: true,
        },
        orderBy: {
          _sum: {
            amount: "desc",
          },
        },
        take: 10,
      });

      // 获取账户详细信息
      for (const consumer of topConsumersData) {
        try {
          const account = await this.db.creditAccount.findUnique({
            where: { id: consumer.accountId },
            select: {
              id: true,
              userId: true,
              organizationId: true,
            },
          });

          if (account) {
            results.topConsumers.push({
              accountId: account.id,
              userId: account.userId || undefined,
              organizationId: account.organizationId || undefined,
              totalConsumed: consumer._sum.amount || 0,
            });
          }
        } catch (error) {
          logger.warn("获取消费者账户信息失败", {
            accountId: consumer.accountId,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      logger.info("积分系统日报生成完成", {
        totalAccounts: results.totalAccounts,
        totalCredits: results.totalCredits,
        dailyTransactions: results.dailyTransactions,
        activeAllocations: results.activeAllocations,
        topConsumersCount: results.topConsumers.length,
      });

      return results;
    } catch (error) {
      const errorMsg = `生成日报失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      results.errors.push(errorMsg);
      return results;
    }
  }

  /**
   * 执行完整的维护任务
   * 包括分配执行、过期清理、规则停用等
   */
  async runMaintenanceTasks(): Promise<{
    allocations: Awaited<ReturnType<typeof this.executePendingAllocations>>;
    expiredCredits: Awaited<ReturnType<typeof this.cleanupExpiredCredits>>;
    expiredAllocations: Awaited<ReturnType<typeof this.deactivateExpiredAllocations>>;
    report: Awaited<ReturnType<typeof this.generateDailyReport>>;
    errors: string[];
  }> {
    const results = {
      allocations: { processed: 0, successful: 0, failed: 0, errors: [] },
      expiredCredits: { expiredTransactions: 0, reclaimedCredits: 0, errors: [] },
      expiredAllocations: { deactivated: 0, errors: [] },
      report: {
        totalAccounts: 0,
        totalCredits: 0,
        dailyTransactions: 0,
        activeAllocations: 0,
        topConsumers: [],
        errors: [],
      },
      errors: [] as string[],
    };

    try {
      logger.info("开始执行积分系统维护任务");

      // 执行积分分配
      results.allocations = await this.executePendingAllocations();

      // 清理过期积分
      results.expiredCredits = await this.cleanupExpiredCredits();

      // 停用过期分配规则
      results.expiredAllocations = await this.deactivateExpiredAllocations();

      // 生成日报
      results.report = await this.generateDailyReport();

      logger.info("积分系统维护任务完成", {
        allocationsProcessed: results.allocations.processed,
        expiredCreditsReclaimed: results.expiredCredits.reclaimedCredits,
        allocationsDeactivated: results.expiredAllocations.deactivated,
        totalErrors: results.allocations.errors.length + 
                    results.expiredCredits.errors.length + 
                    results.expiredAllocations.errors.length + 
                    results.report.errors.length,
      });

      return results;
    } catch (error) {
      const errorMsg = `维护任务执行失败: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(errorMsg);
      results.errors.push(errorMsg);
      return results;
    }
  }
}
