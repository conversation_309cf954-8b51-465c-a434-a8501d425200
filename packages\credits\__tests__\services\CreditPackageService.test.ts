import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditPackageService } from '../../src/services/CreditPackageService';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories, TestUtils } from '../test-factories';

// Mock Prisma Client
const mockPrismaClient = {
  creditPackage: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    update: vi.fn(),
    delete: vi.fn()
  },
  creditTransaction: {
    findFirst: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn()
  },
  $transaction: vi.fn()
} as any;

// Mock CreditService
const mockCreditService = {
  getOrCreateAccount: vi.fn(),
  addCredits: vi.fn(),
  consumeCredits: vi.fn(),
  getTransaction: vi.fn()
} as any;

describe('CreditPackageService', () => {
  let packageService: CreditPackageService;

  beforeEach(() => {
    vi.clearAllMocks();
    packageService = new CreditPackageService(mockPrismaClient, mockCreditService);
  });

  describe('createPackage', () => {
    it('should create package successfully', async () => {
      const mockPackage = TestFactories.createCreditPackage();
      mockPrismaClient.creditPackage.create.mockResolvedValue(mockPackage);

      const result = await packageService.createPackage({
        name: 'Basic Package',
        description: 'A basic credit package',
        creditAmount: 500,
        price: 9.99,
        currency: 'USD',
        productIds: { stripe: 'price_123' },
        validityDays: 365
      });

      expect(result).toEqual(mockPackage);
      expect(mockPrismaClient.creditPackage.create).toHaveBeenCalledWith({
        data: {
          name: 'Basic Package',
          description: 'A basic credit package',
          creditAmount: 500,
          price: 9.99,
          currency: 'USD',
          productIds: { stripe: 'price_123' },
          validityDays: 365,
          status: 'ACTIVE',
          featured: false,
          sortOrder: 0
        }
      });
    });

    it('should validate input parameters', async () => {
      await expect(packageService.createPackage({
        name: '',
        description: 'Test',
        creditAmount: 500,
        price: 9.99,
        currency: 'USD'
      })).rejects.toThrow('Package name is required');

      await expect(packageService.createPackage({
        name: 'Test',
        description: 'Test',
        creditAmount: 0,
        price: 9.99,
        currency: 'USD'
      })).rejects.toThrow('Credit amount must be a positive integer');

      await expect(packageService.createPackage({
        name: 'Test',
        description: 'Test',
        creditAmount: 500,
        price: -1,
        currency: 'USD'
      })).rejects.toThrow('Price must be a positive number');
    });
  });

  describe('getPackages', () => {
    it('should return packages with pagination', async () => {
      const mockPackages = [
        TestFactories.createCreditPackage(),
        TestFactories.createCreditPackage({ id: 'package-456' })
      ];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);
      mockPrismaClient.creditPackage.count.mockResolvedValue(2);

      const result = await packageService.getPackages();

      expect(result.items).toEqual(mockPackages);
      expect(result.total).toBe(2);
      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: 0,
        take: 20
      });
    });

    it('should support status filtering', async () => {
      const mockPackages = [TestFactories.createCreditPackage()];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);
      mockPrismaClient.creditPackage.count.mockResolvedValue(1);

      await packageService.getPackages({ status: 'ACTIVE' });

      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: { status: 'ACTIVE' },
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: 0,
        take: 20
      });
    });

    it('should support pagination', async () => {
      const mockPackages = [TestFactories.createCreditPackage()];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);
      mockPrismaClient.creditPackage.count.mockResolvedValue(1);

      await packageService.getPackages({ page: 2, limit: 10 });

      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip: 10,
        take: 10
      });
    });
  });

  describe('getActivePackages', () => {
    it('should return only active packages', async () => {
      const mockPackages = [TestFactories.createCreditPackage({ status: 'ACTIVE' })];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);

      const result = await packageService.getActivePackages();

      expect(result).toEqual(mockPackages);
      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: { status: 'ACTIVE' },
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ]
      });
    });
  });

  describe('getFeaturedPackages', () => {
    it('should return only featured packages', async () => {
      const mockPackages = [TestFactories.createFeaturedPackage()];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);

      const result = await packageService.getFeaturedPackages();

      expect(result).toEqual(mockPackages);
      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: { 
          status: 'ACTIVE',
          featured: true 
        },
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ]
      });
    });
  });

  describe('getPackage', () => {
    it('should return package when found', async () => {
      const mockPackage = TestFactories.createCreditPackage();
      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(mockPackage);

      const result = await packageService.getPackage('package-123');

      expect(result).toEqual(mockPackage);
      expect(mockPrismaClient.creditPackage.findUnique).toHaveBeenCalledWith({
        where: { id: 'package-123' }
      });
    });

    it('should return null when package not found', async () => {
      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(null);

      const result = await packageService.getPackage('package-123');

      expect(result).toBeNull();
    });

    it('should validate package ID', async () => {
      await expect(packageService.getPackage('')).rejects.toThrow('Invalid package ID format');
    });
  });

  describe('getPackageByProductId', () => {
    it('should return package when found by product ID', async () => {
      const mockPackage = TestFactories.createCreditPackage();
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(mockPackage);

      const result = await packageService.getPackageByProductId('stripe', 'price_123');

      expect(result).toEqual(mockPackage);
      expect(mockPrismaClient.creditPackage.findFirst).toHaveBeenCalledWith({
        where: {
          productIds: {
            path: ['stripe'],
            equals: 'price_123'
          }
        }
      });
    });

    it('should return null when package not found', async () => {
      mockPrismaClient.creditPackage.findFirst.mockResolvedValue(null);

      const result = await packageService.getPackageByProductId('stripe', 'price_123');

      expect(result).toBeNull();
    });
  });

  describe('updatePackage', () => {
    it('should update package successfully', async () => {
      const mockPackage = TestFactories.createCreditPackage({ name: 'Updated Package' });
      mockPrismaClient.creditPackage.update.mockResolvedValue(mockPackage);

      const result = await packageService.updatePackage('package-123', {
        name: 'Updated Package',
        price: 19.99
      });

      expect(result).toEqual(mockPackage);
      expect(mockPrismaClient.creditPackage.update).toHaveBeenCalledWith({
        where: { id: 'package-123' },
        data: {
          name: 'Updated Package',
          price: 19.99,
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should validate package ID', async () => {
      await expect(packageService.updatePackage('', {})).rejects.toThrow('Invalid package ID format');
    });
  });

  describe('updatePackageStatus', () => {
    it('should update package status successfully', async () => {
      const mockPackage = TestFactories.createCreditPackage({ status: 'INACTIVE' });
      mockPrismaClient.creditPackage.update.mockResolvedValue(mockPackage);

      const result = await packageService.updatePackageStatus('package-123', 'INACTIVE');

      expect(result).toEqual(mockPackage);
      expect(mockPrismaClient.creditPackage.update).toHaveBeenCalledWith({
        where: { id: 'package-123' },
        data: {
          status: 'INACTIVE',
          updatedAt: expect.any(Date)
        }
      });
    });
  });

  describe('processPurchase', () => {
    it('should process purchase successfully', async () => {
      const mockPackage = TestFactories.createCreditPackage();
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(mockPackage);
      mockCreditService.getOrCreateAccount.mockResolvedValue(mockAccount);
      mockCreditService.addCredits.mockResolvedValue(mockTransaction);

      const result = await packageService.processPurchase({
        packageId: 'package-123',
        userId: 'user-123',
        purchaseId: 'purchase-456',
        paymentProvider: 'stripe'
      });

      expect(result).toEqual(mockTransaction);
      expect(mockCreditService.addCredits).toHaveBeenCalledWith({
        accountId: mockAccount.id,
        amount: mockPackage.creditAmount,
        type: 'EARNED_PURCHASE',
        reason: `Credit package purchase: ${mockPackage.name}`,
        description: `Purchased ${mockPackage.creditAmount} credits`,
        purchaseId: 'purchase-456',
        expiresAt: expect.any(Date),
        metadata: {
          packageId: 'package-123',
          packageName: mockPackage.name,
          paymentProvider: 'stripe',
          originalPrice: mockPackage.price,
          currency: mockPackage.currency
        }
      });
    });

    it('should throw error when package not found', async () => {
      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(null);

      await expect(packageService.processPurchase({
        packageId: 'package-123',
        userId: 'user-123',
        purchaseId: 'purchase-456',
        paymentProvider: 'stripe'
      })).rejects.toThrow('Credit package not found');
    });

    it('should throw error when package is inactive', async () => {
      const inactivePackage = TestFactories.createCreditPackage({ status: 'INACTIVE' });
      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(inactivePackage);

      await expect(packageService.processPurchase({
        packageId: 'package-123',
        userId: 'user-123',
        purchaseId: 'purchase-456',
        paymentProvider: 'stripe'
      })).rejects.toThrow('Credit package is not active');
    });
  });

  describe('processRefund', () => {
    it('should process refund successfully', async () => {
      const mockTransaction = TestFactories.createCreditTransaction({
        type: 'EARNED_PURCHASE',
        amount: 500,
        purchaseId: 'purchase-456'
      });
      const mockRefundTransaction = TestFactories.createCreditTransaction({
        type: 'REFUNDED',
        amount: -500
      });

      mockCreditService.getTransaction.mockResolvedValue(mockTransaction);
      mockCreditService.consumeCredits.mockResolvedValue(mockRefundTransaction);

      const result = await packageService.processRefund({
        purchaseId: 'purchase-456',
        reason: 'Customer request'
      });

      expect(result).toEqual(mockRefundTransaction);
      expect(mockCreditService.consumeCredits).toHaveBeenCalledWith({
        accountId: mockTransaction.accountId,
        amount: 500,
        reason: 'Refund for purchase: purchase-456',
        description: 'Customer request',
        metadata: {
          originalTransactionId: mockTransaction.id,
          refundReason: 'Customer request'
        }
      });
    });

    it('should throw error when transaction not found', async () => {
      mockCreditService.getTransaction.mockResolvedValue(null);

      await expect(packageService.processRefund({
        purchaseId: 'purchase-456',
        reason: 'Customer request'
      })).rejects.toThrow('Purchase transaction not found');
    });

    it('should throw error for non-purchase transaction', async () => {
      const nonPurchaseTransaction = TestFactories.createCreditTransaction({
        type: 'SPENT_FEATURE'
      });
      mockCreditService.getTransaction.mockResolvedValue(nonPurchaseTransaction);

      await expect(packageService.processRefund({
        purchaseId: 'purchase-456',
        reason: 'Customer request'
      })).rejects.toThrow('Transaction is not a purchase transaction');
    });
  });

  describe('getPackageSalesStats', () => {
    it('should return sales statistics', async () => {
      const mockTransactions = TestFactories.createTransactionList(5, {
        type: 'EARNED_PURCHASE',
        metadata: { packageId: 'package-123' }
      });
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(mockTransactions);
      mockPrismaClient.creditTransaction.count.mockResolvedValue(5);

      const result = await packageService.getPackageSalesStats('package-123');

      expect(result.totalSales).toBe(5);
      expect(result.totalRevenue).toBeGreaterThan(0);
      expect(result.averageOrderValue).toBeGreaterThan(0);
    });
  });

  describe('getPopularPackages', () => {
    it('should return popular packages based on sales', async () => {
      const mockPackages = [TestFactories.createCreditPackage()];
      mockPrismaClient.creditPackage.findMany.mockResolvedValue(mockPackages);

      const result = await packageService.getPopularPackages(5);

      expect(result).toEqual(mockPackages);
      expect(mockPrismaClient.creditPackage.findMany).toHaveBeenCalledWith({
        where: { status: 'ACTIVE' },
        orderBy: { createdAt: 'desc' }, // This would be more complex in real implementation
        take: 5
      });
    });
  });
});
