import { track } from '@vercel/analytics';
import type { BusinessMetric } from './types';

// Vercel Analytics 集成
export const trackEvent = (event: string, properties?: Record<string, any>) => {
  if (typeof window !== 'undefined') {
    track(event, properties);
  }
};

// 业务指标追踪
export const trackBusinessMetric = (metric: BusinessMetric) => {
  const { event, userId, organizationId, properties = {} } = metric;
  
  // 添加通用属性
  const enrichedProperties = {
    ...properties,
    timestamp: new Date().toISOString(),
    ...(userId && { user_id: userId }),
    ...(organizationId && { organization_id: organizationId }),
  };
  
  trackEvent(event, enrichedProperties);
};

// 页面访问追踪
export const trackPageView = (page: string, properties?: Record<string, any>) => {
  trackEvent('page_view', {
    page,
    ...properties,
  });
};

// 用户行为追踪
export const trackUserAction = (action: string, properties?: Record<string, any>) => {
  trackEvent('user_action', {
    action,
    ...properties,
  });
};

// 功能使用追踪
export const trackFeatureUsage = (feature: string, properties?: Record<string, any>) => {
  trackEvent('feature_usage', {
    feature,
    ...properties,
  });
};

// 支付相关追踪
export const trackPaymentEvent = (event: 'payment_initiated' | 'payment_completed' | 'payment_failed', properties?: Record<string, any>) => {
  trackEvent(event, {
    category: 'payment',
    ...properties,
  });
};

// 错误追踪（用于业务逻辑错误，非技术错误）
export const trackBusinessError = (error: string, properties?: Record<string, any>) => {
  trackEvent('business_error', {
    error,
    ...properties,
  });
};

// 性能指标追踪
export const trackPerformance = (metric: string, value: number, properties?: Record<string, any>) => {
  trackEvent('performance_metric', {
    metric,
    value,
    ...properties,
  });
};

// Umami Analytics 集成（可选）
export class UmamiAnalytics {
  private websiteId: string;
  private apiUrl: string;
  
  constructor(websiteId: string, apiUrl: string = 'https://umami.is/api') {
    this.websiteId = websiteId;
    this.apiUrl = apiUrl;
  }
  
  track(event: string, properties?: Record<string, any>) {
    if (typeof window === 'undefined') return;
    
    // 使用 Umami 的跟踪脚本
    if (window.umami) {
      window.umami.track(event, properties);
    }
  }
  
  pageView(url?: string) {
    if (typeof window === 'undefined') return;
    
    if (window.umami) {
      window.umami.track(url || window.location.pathname);
    }
  }
}

// 全局 Umami 类型声明
declare global {
  interface Window {
    umami?: {
      track: (event: string, properties?: Record<string, any>) => void;
    };
  }
}
