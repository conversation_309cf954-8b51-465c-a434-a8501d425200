# 积分系统测试指南

## 📋 测试概览

积分系统的测试套件提供了全面的测试覆盖，确保系统的可靠性和稳定性。

### 测试统计
- **总测试文件**: 7个
- **测试类型**: 单元测试、集成测试
- **覆盖范围**: 服务层、工具函数、类型定义、错误处理
- **目标覆盖率**: 80%+

## 🗂️ 测试文件结构

```
packages/credits/__tests__/
├── test-factories.ts                    # 测试数据工厂
├── services/                           # 服务层测试
│   ├── CreditService.test.ts           # 积分服务测试
│   ├── CreditAllocationService.test.ts # 分配服务测试
│   └── CreditPackageService.test.ts    # 积分包服务测试
├── utils/                              # 工具函数测试
│   ├── credit-helpers.test.ts          # 辅助函数测试
│   └── credit-config.test.ts           # 配置测试
├── types/                              # 类型测试
│   └── credit-errors.test.ts           # 错误处理测试
└── integration/                        # 集成测试
    └── credit-system.integration.test.ts # 系统集成测试
```

## 🧪 测试内容详解

### 1. 服务层测试

#### CreditService.test.ts
测试核心积分管理功能：
- ✅ 账户创建和管理
- ✅ 余额查询和验证
- ✅ 积分交易处理
- ✅ 积分消耗和添加
- ✅ 积分转账功能
- ✅ 错误处理和边界条件

#### CreditAllocationService.test.ts
测试订阅分配功能：
- ✅ 分配规则创建
- ✅ 订阅计划分配
- ✅ 分配执行逻辑
- ✅ 批量分配处理
- ✅ 分配状态管理
- ✅ 账户分配暂停/恢复

#### CreditPackageService.test.ts
测试积分包管理：
- ✅ 积分包CRUD操作
- ✅ 购买处理流程
- ✅ 退款处理逻辑
- ✅ 销售统计分析
- ✅ 热门包查询
- ✅ 状态管理

### 2. 工具函数测试

#### credit-helpers.test.ts
测试辅助工具函数：
- ✅ 余额计算函数
- ✅ 格式化函数
- ✅ 过期检查函数
- ✅ 数据分析函数
- ✅ 验证函数
- ✅ 分页处理函数

#### credit-config.test.ts
测试配置管理：
- ✅ 功能消耗配置
- ✅ 订阅分配配置
- ✅ 系统默认配置
- ✅ 日期计算函数
- ✅ 验证函数
- ✅ 配置完整性检查

### 3. 类型和错误测试

#### credit-errors.test.ts
测试错误处理系统：
- ✅ 错误类层次结构
- ✅ 错误工厂函数
- ✅ 错误识别函数
- ✅ 错误格式化
- ✅ 错误处理转换
- ✅ 所有错误类型覆盖

### 4. 集成测试

#### credit-system.integration.test.ts
测试完整业务流程：
- ✅ 用户完整生命周期
- ✅ 积分转账流程
- ✅ 批量分配处理
- ✅ 购买退款流程
- ✅ 账户暂停恢复
- ✅ 积分过期处理
- ✅ 错误恢复机制

## 🚀 运行测试

### 基本命令

```bash
# 运行所有测试
pnpm test

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 监听模式运行测试
pnpm test:watch

# 启动测试UI界面
pnpm test:ui
```

### 分类运行

```bash
# 只运行服务层测试
pnpm test:services

# 只运行工具函数测试
pnpm test:utils

# 只运行集成测试
pnpm test:integration
```

### 使用测试脚本

```bash
# 使用自定义测试脚本
node run-tests.js --coverage
node run-tests.js --services --watch
node run-tests.js --integration
```

## 📊 测试覆盖率目标

| 类型 | 目标覆盖率 | 当前状态 |
|------|-----------|----------|
| 语句覆盖率 | 80%+ | ✅ |
| 分支覆盖率 | 80%+ | ✅ |
| 函数覆盖率 | 80%+ | ✅ |
| 行覆盖率 | 80%+ | ✅ |

## 🔧 测试配置

### vitest.config.ts
- 测试环境: Node.js
- 覆盖率提供者: v8
- 全局变量支持
- 自动模拟设置

### test-setup.ts
- Prisma客户端模拟
- 全局测试工具
- 自动清理机制

## 🏭 测试数据工厂

### TestFactories
提供标准化的测试数据创建：
- `createCreditAccount()` - 创建积分账户
- `createCreditTransaction()` - 创建积分交易
- `createCreditAllocation()` - 创建分配规则
- `createCreditPackage()` - 创建积分包
- `createTransactionList()` - 批量创建交易
- `createExpiredTransaction()` - 创建过期交易

### TestUtils
提供测试辅助工具：
- `createFutureDate()` - 创建未来日期
- `createPastDate()` - 创建过去日期
- `randomString()` - 生成随机字符串
- `randomNumber()` - 生成随机数字

## 🐛 调试测试

### 常见问题

1. **Mock未正确设置**
   ```typescript
   // 确保在每个测试前清理mock
   beforeEach(() => {
     vi.clearAllMocks();
   });
   ```

2. **异步测试超时**
   ```typescript
   // 使用适当的等待
   await expect(asyncFunction()).resolves.toBe(expected);
   ```

3. **日期相关测试不稳定**
   ```typescript
   // 使用固定日期进行测试
   const fixedDate = new Date('2024-01-15T10:00:00Z');
   ```

### 调试技巧

```typescript
// 使用console.log调试
it('should debug test', () => {
  console.log('Debug info:', testData);
  expect(result).toBe(expected);
});

// 使用vitest的调试功能
import { vi } from 'vitest';
const spy = vi.spyOn(service, 'method');
expect(spy).toHaveBeenCalledWith(expectedArgs);
```

## 📈 持续改进

### 测试质量检查清单
- [ ] 所有公共方法都有测试
- [ ] 错误场景都有覆盖
- [ ] 边界条件都有测试
- [ ] 异步操作都有测试
- [ ] Mock使用正确
- [ ] 测试数据清理完整
- [ ] 测试描述清晰明确

### 新增测试指南
1. 为新功能编写测试
2. 确保测试覆盖率不下降
3. 遵循现有测试模式
4. 使用测试工厂创建数据
5. 添加适当的错误测试

## 🎯 最佳实践

1. **测试命名**: 使用描述性的测试名称
2. **测试隔离**: 每个测试独立运行
3. **数据清理**: 测试后清理所有数据
4. **Mock策略**: 只模拟外部依赖
5. **断言明确**: 使用具体的断言
6. **错误测试**: 测试所有错误路径
7. **性能考虑**: 避免不必要的重复操作

通过这套完整的测试体系，我们确保积分系统的高质量和可靠性！
