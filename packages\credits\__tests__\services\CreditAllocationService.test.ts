import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditAllocationService } from '../../src/services/CreditAllocationService';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories, TestUtils } from '../test-factories';

// Mock Prisma Client
const mockPrismaClient = {
  creditAllocation: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    updateMany: vi.fn()
  },
  creditAccount: {
    findFirst: vi.fn(),
    findUnique: vi.fn()
  },
  $transaction: vi.fn()
} as any;

// Mock CreditService
const mockCreditService = {
  getOrCreateAccount: vi.fn(),
  addCredits: vi.fn()
} as any;

describe('CreditAllocationService', () => {
  let allocationService: CreditAllocationService;

  beforeEach(() => {
    vi.clearAllMocks();
    allocationService = new CreditAllocationService(mockPrismaClient, mockCreditService);
  });

  describe('createAllocation', () => {
    it('should create allocation successfully', async () => {
      const mockAllocation = TestFactories.createCreditAllocation();
      mockPrismaClient.creditAllocation.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAllocation.create.mockResolvedValue(mockAllocation);

      const result = await allocationService.createAllocation({
        accountId: 'account-123',
        planId: 'basic',
        creditsPerPeriod: 200,
        period: 'MONTHLY'
      });

      expect(result).toEqual(mockAllocation);
      expect(mockPrismaClient.creditAllocation.create).toHaveBeenCalledWith({
        data: {
          accountId: 'account-123',
          planId: 'basic',
          creditsPerPeriod: 200,
          period: 'MONTHLY',
          status: 'ACTIVE',
          nextAllocationAt: expect.any(Date),
          expirationPolicy: 'ROLLING_EXPIRATION',
          expirationDays: 365
        }
      });
    });

    it('should throw error when allocation already exists', async () => {
      const existingAllocation = TestFactories.createCreditAllocation();
      mockPrismaClient.creditAllocation.findFirst.mockResolvedValue(existingAllocation);

      await expect(allocationService.createAllocation({
        accountId: 'account-123',
        planId: 'basic',
        creditsPerPeriod: 200,
        period: 'MONTHLY'
      })).rejects.toThrow('Credit allocation already exists for this account and plan');
    });

    it('should validate input parameters', async () => {
      await expect(allocationService.createAllocation({
        accountId: '',
        planId: 'basic',
        creditsPerPeriod: 200,
        period: 'MONTHLY'
      })).rejects.toThrow('Invalid account ID format');

      await expect(allocationService.createAllocation({
        accountId: 'account-123',
        planId: '',
        creditsPerPeriod: 200,
        period: 'MONTHLY'
      })).rejects.toThrow('Plan ID is required');

      await expect(allocationService.createAllocation({
        accountId: 'account-123',
        planId: 'basic',
        creditsPerPeriod: 0,
        period: 'MONTHLY'
      })).rejects.toThrow('Credits per period must be a positive integer');
    });
  });

  describe('createAllocationFromSubscription', () => {
    it('should create allocation from subscription plan', async () => {
      const mockAllocation = TestFactories.createCreditAllocation();
      mockPrismaClient.creditAllocation.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAllocation.create.mockResolvedValue(mockAllocation);

      const result = await allocationService.createAllocationFromSubscription(
        'account-123',
        'basic'
      );

      expect(result).toEqual(mockAllocation);
      expect(mockPrismaClient.creditAllocation.create).toHaveBeenCalledWith({
        data: {
          accountId: 'account-123',
          planId: 'basic',
          creditsPerPeriod: 200, // From SUBSCRIPTION_CREDIT_ALLOCATIONS.BASIC
          period: 'MONTHLY',
          status: 'ACTIVE',
          nextAllocationAt: expect.any(Date),
          expirationPolicy: 'ROLLING_EXPIRATION',
          expirationDays: 90
        }
      });
    });

    it('should throw error for unknown subscription plan', async () => {
      await expect(allocationService.createAllocationFromSubscription(
        'account-123',
        'unknown_plan'
      )).rejects.toThrow('Unknown subscription plan: unknown_plan');
    });
  });

  describe('getAllocations', () => {
    it('should return allocations for account', async () => {
      const mockAllocations = [
        TestFactories.createCreditAllocation(),
        TestFactories.createCreditAllocation({ id: 'allocation-456' })
      ];
      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(mockAllocations);
      mockPrismaClient.creditAllocation.count.mockResolvedValue(2);

      const result = await allocationService.getAllocations('account-123');

      expect(result.items).toEqual(mockAllocations);
      expect(result.total).toBe(2);
      expect(mockPrismaClient.creditAllocation.findMany).toHaveBeenCalledWith({
        where: { accountId: 'account-123' },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20
      });
    });

    it('should support pagination', async () => {
      const mockAllocations = [TestFactories.createCreditAllocation()];
      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(mockAllocations);
      mockPrismaClient.creditAllocation.count.mockResolvedValue(1);

      const result = await allocationService.getAllocations('account-123', {
        page: 2,
        limit: 10
      });

      expect(result.page).toBe(2);
      expect(result.limit).toBe(10);
      expect(mockPrismaClient.creditAllocation.findMany).toHaveBeenCalledWith({
        where: { accountId: 'account-123' },
        orderBy: { createdAt: 'desc' },
        skip: 10,
        take: 10
      });
    });

    it('should support status filtering', async () => {
      const mockAllocations = [TestFactories.createCreditAllocation()];
      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(mockAllocations);
      mockPrismaClient.creditAllocation.count.mockResolvedValue(1);

      await allocationService.getAllocations('account-123', {
        status: 'ACTIVE'
      });

      expect(mockPrismaClient.creditAllocation.findMany).toHaveBeenCalledWith({
        where: { 
          accountId: 'account-123',
          status: 'ACTIVE'
        },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20
      });
    });
  });

  describe('getActiveAllocations', () => {
    it('should return only active allocations', async () => {
      const mockAllocations = [
        TestFactories.createCreditAllocation({ status: 'ACTIVE' })
      ];
      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(mockAllocations);

      const result = await allocationService.getActiveAllocations('account-123');

      expect(result).toEqual(mockAllocations);
      expect(mockPrismaClient.creditAllocation.findMany).toHaveBeenCalledWith({
        where: {
          accountId: 'account-123',
          status: 'ACTIVE'
        },
        orderBy: { nextAllocationAt: 'asc' }
      });
    });
  });

  describe('updateAllocationStatus', () => {
    it('should update allocation status successfully', async () => {
      const mockAllocation = TestFactories.createCreditAllocation({ status: 'PAUSED' });
      mockPrismaClient.creditAllocation.update.mockResolvedValue(mockAllocation);

      const result = await allocationService.updateAllocationStatus('allocation-123', 'PAUSED');

      expect(result).toEqual(mockAllocation);
      expect(mockPrismaClient.creditAllocation.update).toHaveBeenCalledWith({
        where: { id: 'allocation-123' },
        data: {
          status: 'PAUSED',
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should validate allocation ID', async () => {
      await expect(allocationService.updateAllocationStatus('', 'PAUSED'))
        .rejects.toThrow('Invalid allocation ID format');
    });
  });

  describe('executeAllocation', () => {
    it('should execute allocation successfully', async () => {
      const mockAllocation = TestFactories.createCreditAllocation({
        nextAllocationAt: TestUtils.createPastDate(1), // Due for allocation
        status: 'ACTIVE'
      });
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue(mockAllocation);
      mockCreditService.getOrCreateAccount.mockResolvedValue(mockAccount);
      mockCreditService.addCredits.mockResolvedValue(mockTransaction);
      mockPrismaClient.creditAllocation.update.mockResolvedValue({
        ...mockAllocation,
        lastAllocationAt: expect.any(Date),
        nextAllocationAt: expect.any(Date)
      });

      const result = await allocationService.executeAllocation('allocation-123');

      expect(result.success).toBe(true);
      expect(result.transaction).toEqual(mockTransaction);
      expect(mockCreditService.addCredits).toHaveBeenCalledWith({
        accountId: mockAllocation.accountId,
        amount: mockAllocation.creditsPerPeriod,
        type: 'EARNED_SUBSCRIPTION',
        reason: `Subscription allocation for plan: ${mockAllocation.planId}`,
        expiresAt: expect.any(Date)
      });
    });

    it('should skip allocation when not due', async () => {
      const mockAllocation = TestFactories.createCreditAllocation({
        nextAllocationAt: TestUtils.createFutureDate(1), // Not due yet
        status: 'ACTIVE'
      });

      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue(mockAllocation);

      const result = await allocationService.executeAllocation('allocation-123');

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Allocation not due yet');
      expect(mockCreditService.addCredits).not.toHaveBeenCalled();
    });

    it('should skip allocation when paused', async () => {
      const mockAllocation = TestFactories.createCreditAllocation({
        nextAllocationAt: TestUtils.createPastDate(1),
        status: 'PAUSED'
      });

      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue(mockAllocation);

      const result = await allocationService.executeAllocation('allocation-123');

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Allocation is paused');
      expect(mockCreditService.addCredits).not.toHaveBeenCalled();
    });

    it('should throw error when allocation not found', async () => {
      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue(null);

      await expect(allocationService.executeAllocation('allocation-123'))
        .rejects.toThrow('Credit allocation not found');
    });
  });

  describe('executeAllDueAllocations', () => {
    it('should execute all due allocations', async () => {
      const dueAllocations = [
        TestFactories.createCreditAllocation({
          id: 'allocation-1',
          nextAllocationAt: TestUtils.createPastDate(1)
        }),
        TestFactories.createCreditAllocation({
          id: 'allocation-2',
          nextAllocationAt: TestUtils.createPastDate(2)
        })
      ];

      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(dueAllocations);

      // Mock executeAllocation to return success
      vi.spyOn(allocationService, 'executeAllocation').mockResolvedValue({
        success: true,
        transaction: TestFactories.createCreditTransaction()
      });

      const result = await allocationService.executeAllDueAllocations();

      expect(result.processed).toBe(2);
      expect(result.successful).toBe(2);
      expect(result.failed).toBe(0);
      expect(allocationService.executeAllocation).toHaveBeenCalledTimes(2);
    });

    it('should handle allocation execution failures', async () => {
      const dueAllocations = [
        TestFactories.createCreditAllocation({ id: 'allocation-1' }),
        TestFactories.createCreditAllocation({ id: 'allocation-2' })
      ];

      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(dueAllocations);

      // Mock one success and one failure
      vi.spyOn(allocationService, 'executeAllocation')
        .mockResolvedValueOnce({ success: true, transaction: TestFactories.createCreditTransaction() })
        .mockRejectedValueOnce(new Error('Execution failed'));

      const result = await allocationService.executeAllDueAllocations();

      expect(result.processed).toBe(2);
      expect(result.successful).toBe(1);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });
  });

  describe('pauseAccountAllocations', () => {
    it('should pause all active allocations for account', async () => {
      mockPrismaClient.creditAllocation.updateMany.mockResolvedValue({ count: 2 });

      const result = await allocationService.pauseAccountAllocations('account-123');

      expect(result).toBe(2);
      expect(mockPrismaClient.creditAllocation.updateMany).toHaveBeenCalledWith({
        where: {
          accountId: 'account-123',
          status: 'ACTIVE'
        },
        data: {
          status: 'PAUSED',
          updatedAt: expect.any(Date)
        }
      });
    });
  });

  describe('resumeAccountAllocations', () => {
    it('should resume all paused allocations for account', async () => {
      mockPrismaClient.creditAllocation.updateMany.mockResolvedValue({ count: 2 });

      const result = await allocationService.resumeAccountAllocations('account-123');

      expect(result).toBe(2);
      expect(mockPrismaClient.creditAllocation.updateMany).toHaveBeenCalledWith({
        where: {
          accountId: 'account-123',
          status: 'PAUSED'
        },
        data: {
          status: 'ACTIVE',
          updatedAt: expect.any(Date)
        }
      });
    });
  });
});
