import { CreditService } from "@repo/credits";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { authMiddleware } from "../../middleware/auth";
import {
  accountQuerySchema,
  createAccountSchema,
  creditAccountResponseSchema,
  errorResponseSchema,
  updateAccountStatusSchema,
} from "./schemas";

// 创建积分服务实例
const creditService = new CreditService(db);

/**
 * 积分账户管理路由
 */
export const accountRouter = new Hono()
  // 获取积分账户信息
  .get(
    "/",
    authMiddleware,
    validator("query", accountQuerySchema),
    describeRoute({
      tags: ["Credits"],
      summary: "获取积分账户信息",
      description: "获取当前用户或指定组织的积分账户信息",
      responses: {
        200: {
          description: "积分账户信息",
          content: {
            "application/json": {
              schema: resolver(creditAccountResponseSchema),
            },
          },
        },
        404: {
          description: "账户不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const query = c.req.valid("query");

        // 确定查询目标
        const targetUserId = query.userId || user.id;
        const targetOrgId = query.organizationId;

        // 权限检查：用户只能查看自己的账户，除非是管理员
        if (query.userId && query.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权访问其他用户的积分账户" });
        }

        // 获取或创建账户
        const account = await creditService.getOrCreateAccount({
          userId: targetUserId,
          organizationId: targetOrgId,
        });

        // 获取余额信息
        const balance = await creditService.getBalance({
          userId: targetUserId,
          organizationId: targetOrgId,
        });

        // 构建响应
        const response = {
          id: account.id,
          userId: account.userId,
          organizationId: account.organizationId,
          currentBalance: balance,
          totalEarned: account.totalEarned,
          totalSpent: account.totalSpent,
          status: account.status,
          createdAt: account.createdAt.toISOString(),
          updatedAt: account.updatedAt.toISOString(),
        };

        // 如果需要包含交易历史
        if (query.includeTransactions) {
          const transactions = await creditService.getTransactionHistory({
            accountId: account.id,
            limit: query.transactionLimit,
          });

          (response as any).recentTransactions = transactions.data.map(tx => ({
            id: tx.id,
            type: tx.type,
            amount: tx.amount,
            reason: tx.reason,
            featureId: tx.featureId,
            createdAt: tx.createdAt.toISOString(),
          }));
        }

        logger.info("积分账户查询成功", {
          userId: targetUserId,
          organizationId: targetOrgId,
          accountId: account.id,
          balance,
        });

        return c.json(response);
      } catch (error) {
        logger.error("获取积分账户失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "获取积分账户信息失败" });
      }
    }
  )

  // 创建积分账户
  .post(
    "/",
    authMiddleware,
    validator("json", createAccountSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "创建积分账户",
      description: "为用户或组织创建新的积分账户",
      responses: {
        201: {
          description: "账户创建成功",
          content: {
            "application/json": {
              schema: resolver(creditAccountResponseSchema),
            },
          },
        },
        400: {
          description: "请求参数错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        409: {
          description: "账户已存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 权限检查：只有管理员可以为其他用户创建账户
        if (data.userId && data.userId !== user.id && user.role !== "admin") {
          throw new HTTPException(403, { message: "无权为其他用户创建积分账户" });
        }

        // 使用当前用户ID作为默认值
        const targetUserId = data.userId || user.id;

        const account = await creditService.createAccount({
          userId: targetUserId,
          organizationId: data.organizationId,
          initialBalance: data.initialBalance,
        });

        const response = {
          id: account.id,
          userId: account.userId,
          organizationId: account.organizationId,
          currentBalance: account.currentBalance,
          totalEarned: account.totalEarned,
          totalSpent: account.totalSpent,
          status: account.status,
          createdAt: account.createdAt.toISOString(),
          updatedAt: account.updatedAt.toISOString(),
        };

        logger.info("积分账户创建成功", {
          accountId: account.id,
          userId: targetUserId,
          organizationId: data.organizationId,
          initialBalance: data.initialBalance,
        });

        return c.json(response, 201);
      } catch (error) {
        logger.error("创建积分账户失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        // 检查是否是账户已存在的错误
        if (error instanceof Error && error.message.includes("already exists")) {
          throw new HTTPException(409, { message: "积分账户已存在" });
        }

        throw new HTTPException(500, { message: "创建积分账户失败" });
      }
    }
  )

  // 更新账户状态
  .put(
    "/status",
    authMiddleware,
    validator("json", updateAccountStatusSchema),
    describeRoute({
      tags: ["Credits"],
      summary: "更新账户状态",
      description: "更新积分账户的状态（激活、暂停、冻结）",
      responses: {
        200: {
          description: "状态更新成功",
          content: {
            "application/json": {
              schema: resolver(creditAccountResponseSchema),
            },
          },
        },
        403: {
          description: "权限不足",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        404: {
          description: "账户不存在",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
        500: {
          description: "服务器错误",
          content: {
            "application/json": {
              schema: resolver(errorResponseSchema),
            },
          },
        },
      },
    }),
    async (c) => {
      try {
        const user = c.get("user");
        const data = c.req.valid("json");

        // 只有管理员可以更新账户状态
        if (user.role !== "admin") {
          throw new HTTPException(403, { message: "只有管理员可以更新账户状态" });
        }

        // 获取用户的积分账户
        const account = await creditService.getOrCreateAccount({ userId: user.id });

        const updatedAccount = await creditService.updateAccountStatus(
          account.id,
          data.status
        );

        const response = {
          id: updatedAccount.id,
          userId: updatedAccount.userId,
          organizationId: updatedAccount.organizationId,
          currentBalance: updatedAccount.currentBalance,
          totalEarned: updatedAccount.totalEarned,
          totalSpent: updatedAccount.totalSpent,
          status: updatedAccount.status,
          createdAt: updatedAccount.createdAt.toISOString(),
          updatedAt: updatedAccount.updatedAt.toISOString(),
        };

        logger.info("积分账户状态更新成功", {
          accountId: account.id,
          oldStatus: account.status,
          newStatus: data.status,
          reason: data.reason,
          adminUserId: user.id,
        });

        return c.json(response);
      } catch (error) {
        logger.error("更新积分账户状态失败", {
          error: error instanceof Error ? error.message : String(error),
          userId: c.get("user")?.id,
        });

        if (error instanceof HTTPException) {
          throw error;
        }

        throw new HTTPException(500, { message: "更新账户状态失败" });
      }
    }
  );
