// ================================
// 积分系统错误类型定义
// ================================

export enum CreditErrorType {
  // 账户相关错误
  ACCOUNT_NOT_FOUND = 'ACCOUNT_NOT_FOUND',
  ACCOUNT_SUSPENDED = 'ACCOUNT_SUSPENDED',
  ACCOUNT_CLOSED = 'ACCOUNT_CLOSED',
  ACCOUNT_ALREADY_EXISTS = 'ACCOUNT_ALREADY_EXISTS',
  
  // 余额相关错误
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  NEGATIVE_BALANCE_NOT_ALLOWED = 'NEGATIVE_BALANCE_NOT_ALLOWED',
  
  // 交易相关错误
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',
  DUPLICATE_TRANSACTION = 'DUPLICATE_TRANSACTION',
  INVALID_TRANSACTION_TYPE = 'INVALID_TRANSACTION_TYPE',
  
  // 分配相关错误
  ALLOCATION_NOT_FOUND = 'ALLOCATION_NOT_FOUND',
  ALLOCATION_ALREADY_EXISTS = 'ALLOCATION_ALREADY_EXISTS',
  ALLOCATION_SUSPENDED = 'ALLOCATION_SUSPENDED',
  INVALID_ALLOCATION_PERIOD = 'INVALID_ALLOCATION_PERIOD',
  
  // 积分包相关错误
  PACKAGE_NOT_FOUND = 'PACKAGE_NOT_FOUND',
  PACKAGE_INACTIVE = 'PACKAGE_INACTIVE',
  PACKAGE_ARCHIVED = 'PACKAGE_ARCHIVED',
  INVALID_PACKAGE_CONFIG = 'INVALID_PACKAGE_CONFIG',
  
  // 转账相关错误
  TRANSFER_TO_SAME_ACCOUNT = 'TRANSFER_TO_SAME_ACCOUNT',
  TRANSFER_LIMIT_EXCEEDED = 'TRANSFER_LIMIT_EXCEEDED',
  TRANSFER_NOT_ALLOWED = 'TRANSFER_NOT_ALLOWED',
  
  // 过期相关错误
  CREDITS_EXPIRED = 'CREDITS_EXPIRED',
  INVALID_EXPIRATION_DATE = 'INVALID_EXPIRATION_DATE',
  
  // 权限相关错误
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  
  // 系统相关错误
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // 通用错误
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  OPERATION_NOT_SUPPORTED = 'OPERATION_NOT_SUPPORTED'
}

export class CreditError extends Error {
  public readonly type: CreditErrorType;
  public readonly code: string;
  public readonly details?: Record<string, any>;
  public readonly timestamp: Date;

  constructor(
    type: CreditErrorType,
    message: string,
    details?: Record<string, any>
  ) {
    super(message);
    this.name = 'CreditError';
    this.type = type;
    this.code = type;
    this.details = details;
    this.timestamp = new Date();
    
    // 确保错误堆栈正确显示
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CreditError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      type: this.type,
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

// ================================
// 具体错误类
// ================================

export class AccountNotFoundError extends CreditError {
  constructor(accountId: string) {
    super(
      CreditErrorType.ACCOUNT_NOT_FOUND,
      `Credit account not found: ${accountId}`,
      { accountId }
    );
  }
}

export class AccountSuspendedError extends CreditError {
  constructor(accountId: string) {
    super(
      CreditErrorType.ACCOUNT_SUSPENDED,
      `Credit account is suspended: ${accountId}`,
      { accountId }
    );
  }
}

export class AccountAlreadyExistsError extends CreditError {
  constructor(accountId: string) {
    super(
      CreditErrorType.ACCOUNT_ALREADY_EXISTS,
      `Credit account already exists: ${accountId}`,
      { accountId }
    );
  }
}

export class InsufficientBalanceError extends CreditError {
  constructor(accountId: string, required: number, available: number) {
    super(
      CreditErrorType.INSUFFICIENT_BALANCE,
      `Insufficient credit balance. Required: ${required}, Available: ${available}`,
      { accountId, required, available }
    );
  }
}

export class InvalidAmountError extends CreditError {
  constructor(amount: number, reason?: string) {
    super(
      CreditErrorType.INVALID_AMOUNT,
      `Invalid credit amount: ${amount}${reason ? ` (${reason})` : ''}`,
      { amount, reason }
    );
  }
}

export class TransactionFailedError extends CreditError {
  constructor(reason: string, details?: Record<string, any>) {
    super(
      CreditErrorType.TRANSACTION_FAILED,
      `Credit transaction failed: ${reason}`,
      details
    );
  }
}

export class PackageNotFoundError extends CreditError {
  constructor(packageId: string) {
    super(
      CreditErrorType.PACKAGE_NOT_FOUND,
      `Credit package not found: ${packageId}`,
      { packageId }
    );
  }
}

export class PackageInactiveError extends CreditError {
  constructor(packageId: string) {
    super(
      CreditErrorType.PACKAGE_INACTIVE,
      `Credit package is inactive: ${packageId}`,
      { packageId }
    );
  }
}

export class TransferToSameAccountError extends CreditError {
  constructor(accountId: string) {
    super(
      CreditErrorType.TRANSFER_TO_SAME_ACCOUNT,
      `Cannot transfer credits to the same account: ${accountId}`,
      { accountId }
    );
  }
}

export class AllocationNotFoundError extends CreditError {
  constructor(allocationId: string) {
    super(
      CreditErrorType.ALLOCATION_NOT_FOUND,
      `Credit allocation not found: ${allocationId}`,
      { allocationId }
    );
  }
}

export class UnauthorizedAccessError extends CreditError {
  constructor(operation: string, userId?: string) {
    super(
      CreditErrorType.UNAUTHORIZED_ACCESS,
      `Unauthorized access to credit operation: ${operation}`,
      { operation, userId }
    );
  }
}

export class ValidationError extends CreditError {
  constructor(field: string, value: any, reason: string) {
    super(
      CreditErrorType.VALIDATION_ERROR,
      `Validation failed for ${field}: ${reason}`,
      { field, value, reason }
    );
  }
}

export class DatabaseError extends CreditError {
  constructor(operation: string, originalError?: Error) {
    super(
      CreditErrorType.DATABASE_ERROR,
      `Database operation failed: ${operation}`,
      { 
        operation, 
        originalError: originalError?.message,
        originalStack: originalError?.stack 
      }
    );
  }
}

// ================================
// 错误工厂函数
// ================================

export const CreditErrors = {
  accountNotFound: (accountId: string) => new AccountNotFoundError(accountId),
  accountSuspended: (accountId: string) => new AccountSuspendedError(accountId),
  accountAlreadyExists: (accountId: string) => new AccountAlreadyExistsError(accountId),
  insufficientBalance: (accountId: string, required: number, available: number) =>
    new InsufficientBalanceError(accountId, required, available),
  invalidAmount: (amount: number, reason?: string) => new InvalidAmountError(amount, reason),
  transactionFailed: (reason: string, details?: Record<string, any>) =>
    new TransactionFailedError(reason, details),
  packageNotFound: (packageId: string) => new PackageNotFoundError(packageId),
  packageInactive: (packageId: string) => new PackageInactiveError(packageId),
  transferToSameAccount: (accountId: string) => new TransferToSameAccountError(accountId),
  allocationNotFound: (allocationId: string) => new AllocationNotFoundError(allocationId),
  unauthorizedAccess: (operation: string, userId?: string) =>
    new UnauthorizedAccessError(operation, userId),
  validation: (field: string, value: any, reason: string) =>
    new ValidationError(field, value, reason),
  database: (operation: string, originalError?: Error) =>
    new DatabaseError(operation, originalError)
};

// ================================
// 错误处理工具函数
// ================================

export function isCreditError(error: any): error is CreditError {
  return error instanceof CreditError;
}

export function getCreditErrorType(error: any): CreditErrorType | null {
  if (isCreditError(error)) {
    return error.type;
  }
  return null;
}

export function formatCreditError(error: CreditError): string {
  const details = error.details ? ` (${JSON.stringify(error.details)})` : '';
  return `[${error.type}] ${error.message}${details}`;
}

export function handleCreditError(error: any): CreditError {
  if (isCreditError(error)) {
    return error;
  }
  
  // 将其他类型的错误转换为CreditError
  if (error instanceof Error) {
    return new CreditError(
      CreditErrorType.UNKNOWN_ERROR,
      error.message,
      { originalError: error.name, originalStack: error.stack }
    );
  }
  
  return new CreditError(
    CreditErrorType.UNKNOWN_ERROR,
    'An unknown error occurred',
    { originalError: String(error) }
  );
}
