"use client";

import { Alert<PERSON>riangle, Coins, ShoppingCart } from "lucide-react";
import { ReactNode } from "react";
import { useCheckFeatureCredits, useCreditPackages } from "../hooks/useCredits";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Alert, AlertDescription } from "@ui/components/alert";
import { cn } from "@ui/lib";

interface CreditGuardProps {
  children: ReactNode;
  requiredCredits: number;
  featureName: string;
  featureDescription?: string;
  onInsufficientCredits?: () => void;
  showPurchaseOptions?: boolean;
  className?: string;
}

/**
 * 积分守卫组件 - 检查用户是否有足够积分使用功能
 */
export function CreditGuard({
  children,
  requiredCredits,
  featureName,
  featureDescription,
  onInsufficientCredits,
  showPurchaseOptions = true,
  className,
}: CreditGuardProps) {
  const { checkCredits, balance, isLoading } = useCheckFeatureCredits();
  const { data: packages } = useCreditPackages();

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <Card>
          <CardContent className="p-6 text-center">
            <Coins className="h-8 w-8 mx-auto mb-2 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">检查积分余额中...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const creditCheck = checkCredits(requiredCredits);

  if (creditCheck.canUse) {
    return <>{children}</>;
  }

  // 积分不足时显示的界面
  return (
    <div className={cn("space-y-4", className)}>
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <p className="font-medium">积分不足</p>
            <p className="text-sm">{creditCheck.reason}</p>
          </div>
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-primary" />
            {featureName}
          </CardTitle>
          {featureDescription && (
            <p className="text-sm text-muted-foreground">{featureDescription}</p>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div>
              <div className="font-medium">所需积分</div>
              <div className="text-sm text-muted-foreground">使用此功能需要消耗积分</div>
            </div>
            <Badge status="warning" className="text-lg px-3 py-1">
              {requiredCredits} 积分
            </Badge>
          </div>

          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div>
              <div className="font-medium">当前余额</div>
              <div className="text-sm text-muted-foreground">您的积分账户余额</div>
            </div>
            <Badge status={balance >= requiredCredits ? "success" : "error"} className="text-lg px-3 py-1">
              {balance} 积分
            </Badge>
          </div>

          {showPurchaseOptions && packages && packages.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                <span className="font-medium">购买积分包</span>
              </div>
              <div className="grid gap-3">
                {packages
                  .filter(pkg => pkg.status === "ACTIVE" && pkg.creditAmount >= requiredCredits - balance)
                  .slice(0, 3)
                  .map((pkg) => (
                    <CreditPackageOption
                      key={pkg.id}
                      package={pkg}
                      requiredCredits={requiredCredits}
                      currentBalance={balance}
                    />
                  ))}
              </div>
            </div>
          )}

          {onInsufficientCredits && (
            <Button
              onClick={onInsufficientCredits}
              className="w-full"
              variant="outline"
            >
              了解更多
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * 积分包选项组件
 */
interface CreditPackageOptionProps {
  package: any; // CreditPackage type
  requiredCredits: number;
  currentBalance: number;
}

function CreditPackageOption({
  package: pkg,
  requiredCredits,
  currentBalance,
}: CreditPackageOptionProps) {
  const handlePurchase = () => {
    // 跳转到积分包购买页面或打开购买弹窗
    window.location.href = `/credits/packages?package=${pkg.id}`;
  };

  const afterPurchaseBalance = currentBalance + pkg.creditAmount;
  const isEnough = afterPurchaseBalance >= requiredCredits;

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
      <div>
        <div className="font-medium">{pkg.name}</div>
        <div className="text-sm text-muted-foreground">
          +{pkg.creditAmount.toLocaleString()} 积分
        </div>
        <div className="text-xs text-muted-foreground">
          购买后余额: {afterPurchaseBalance.toLocaleString()} 积分
        </div>
      </div>
      <div className="text-right">
        <div className="font-semibold">¥{pkg.price.toFixed(2)}</div>
        <Button
          size="sm"
          onClick={handlePurchase}
          className="mt-1"
          variant={isEnough ? "default" : "outline"}
        >
          {isEnough ? "立即购买" : "不够用"}
        </Button>
      </div>
    </div>
  );
}

/**
 * 简单的积分检查Hook组件
 */
interface CreditCheckProps {
  requiredCredits: number;
  children: (params: {
    canUse: boolean;
    balance: number;
    reason?: string;
    isLoading: boolean;
  }) => ReactNode;
}

export function CreditCheck({ requiredCredits, children }: CreditCheckProps) {
  const { checkCredits, balance, isLoading } = useCheckFeatureCredits();
  
  const creditCheck = checkCredits(requiredCredits);
  
  return (
    <>
      {children({
        canUse: creditCheck.canUse,
        balance,
        reason: creditCheck.reason,
        isLoading,
      })}
    </>
  );
}

/**
 * 积分消耗确认组件
 */
interface CreditConfirmProps {
  requiredCredits: number;
  featureName: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  className?: string;
}

export function CreditConfirm({
  requiredCredits,
  featureName,
  onConfirm,
  onCancel,
  isLoading = false,
  className,
}: CreditConfirmProps) {
  const { balance } = useCheckFeatureCredits();

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Coins className="h-5 w-5 text-primary" />
          确认消耗积分
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>功能名称:</span>
            <span className="font-medium">{featureName}</span>
          </div>
          <div className="flex justify-between">
            <span>消耗积分:</span>
            <span className="font-medium text-orange-600">-{requiredCredits}</span>
          </div>
          <div className="flex justify-between">
            <span>当前余额:</span>
            <span className="font-medium">{balance}</span>
          </div>
          <div className="flex justify-between border-t pt-2">
            <span>使用后余额:</span>
            <span className="font-medium">{balance - requiredCredits}</span>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={onCancel}
            variant="outline"
            className="flex-1"
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            className="flex-1"
            disabled={isLoading}
          >
            {isLoading ? "处理中..." : "确认使用"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
