import type { CustomMetric, PerformanceMetric } from './types';

// 内存中的指标存储（简单实现）
class MetricsStore {
  private metrics: Map<string, CustomMetric[]> = new Map();
  private maxMetricsPerType = 1000; // 防止内存泄漏
  
  add(metric: CustomMetric) {
    const key = metric.name;
    const existing = this.metrics.get(key) || [];
    
    // 添加时间戳
    const enrichedMetric = {
      ...metric,
      timestamp: metric.timestamp || new Date(),
    };
    
    existing.push(enrichedMetric);
    
    // 保持最新的指标
    if (existing.length > this.maxMetricsPerType) {
      existing.shift();
    }
    
    this.metrics.set(key, existing);
  }
  
  get(name: string): CustomMetric[] {
    return this.metrics.get(name) || [];
  }
  
  getAll(): Record<string, CustomMetric[]> {
    const result: Record<string, CustomMetric[]> = {};
    this.metrics.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }
  
  clear(name?: string) {
    if (name) {
      this.metrics.delete(name);
    } else {
      this.metrics.clear();
    }
  }
}

const metricsStore = new MetricsStore();

// 记录自定义指标
export const recordMetric = (metric: CustomMetric) => {
  metricsStore.add(metric);
  
  // 如果是生产环境，可以发送到外部服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成到 DataDog、New Relic 等服务
    console.log('Metric recorded:', metric);
  }
};

// 记录性能指标
export const recordPerformanceMetric = (metric: PerformanceMetric) => {
  const customMetric: CustomMetric = {
    name: `performance.${metric.name}`,
    value: metric.duration,
    tags: {
      success: metric.success.toString(),
      ...metric.metadata,
    },
  };
  
  recordMetric(customMetric);
};

// 性能监控装饰器
export const withPerformanceTracking = <T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T => {
  return ((...args: Parameters<T>) => {
    const startTime = Date.now();
    let success = true;
    
    try {
      const result = fn(...args);
      
      // 处理异步函数
      if (result instanceof Promise) {
        return result
          .then((value) => {
            recordPerformanceMetric({
              name,
              duration: Date.now() - startTime,
              success: true,
            });
            return value;
          })
          .catch((error) => {
            recordPerformanceMetric({
              name,
              duration: Date.now() - startTime,
              success: false,
              metadata: { error: error.message },
            });
            throw error;
          });
      }
      
      // 同步函数
      recordPerformanceMetric({
        name,
        duration: Date.now() - startTime,
        success: true,
      });
      
      return result;
    } catch (error) {
      recordPerformanceMetric({
        name,
        duration: Date.now() - startTime,
        success: false,
        metadata: { error: (error as Error).message },
      });
      throw error;
    }
  }) as T;
};

// 数据库查询性能监控
export const trackDatabaseQuery = (query: string, duration: number, success: boolean) => {
  recordPerformanceMetric({
    name: 'database.query',
    duration,
    success,
    metadata: {
      query: query.substring(0, 100), // 截断长查询
    },
  });
};

// API 响应时间监控
export const trackApiResponse = (endpoint: string, method: string, statusCode: number, duration: number) => {
  recordPerformanceMetric({
    name: 'api.response',
    duration,
    success: statusCode < 400,
    metadata: {
      endpoint,
      method,
      statusCode: statusCode.toString(),
    },
  });
};

// 获取指标统计
export const getMetricsStats = (name: string) => {
  const metrics = metricsStore.get(name);
  if (metrics.length === 0) return null;
  
  const values = metrics.map(m => m.value);
  const sum = values.reduce((a, b) => a + b, 0);
  const avg = sum / values.length;
  const min = Math.min(...values);
  const max = Math.max(...values);
  
  return {
    count: values.length,
    sum,
    avg,
    min,
    max,
    latest: values[values.length - 1],
  };
};

// 导出指标数据（用于外部监控系统）
export const exportMetrics = () => {
  return metricsStore.getAll();
};

// 清理指标数据
export const clearMetrics = (name?: string) => {
  metricsStore.clear(name);
};
