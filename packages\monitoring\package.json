{"name": "@repo/monitoring", "version": "0.0.0", "private": true, "main": "./index.ts", "exports": {".": {"types": "./index.ts", "default": "./index.ts"}, "./sentry": "./src/sentry.ts", "./analytics": "./src/analytics.ts", "./metrics": "./src/metrics.ts", "./logger": "./src/logger.ts"}, "scripts": {"type-check": "tsc --noEmit"}, "dependencies": {"@sentry/nextjs": "^8.40.0", "@sentry/node": "^8.40.0", "@vercel/analytics": "^1.1.1", "@vercel/speed-insights": "^1.0.12", "@repo/config": "workspace:*", "@repo/logs": "workspace:*", "pino": "^9.5.0", "pino-pretty": "^13.0.0"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "@types/node": "^22.14.0", "typescript": "5.8.3"}}