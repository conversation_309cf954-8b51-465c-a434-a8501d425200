import { Hono } from "hono";
import { accountRouter } from "./account";
import { adminRouter } from "./admin";
import { allocationRouter } from "./allocation";
import { packageRouter } from "./package";
import { transactionRouter } from "./transaction";

/**
 * 积分系统主路由
 *
 * 路由结构:
 * /credits/account - 积分账户管理
 * /credits/transactions - 积分交易管理
 * /credits/allocations - 积分分配管理
 * /credits/packages - 积分包管理
 * /credits/admin - 管理员功能
 * /credits/config - 配置管理和维护任务
 */
export const creditsRouter = new Hono()
  .route("/account", accountRouter)
  .route("/transactions", transactionRouter)
  .route("/allocations", allocationRouter)
  .route("/packages", packageRouter)
  .route("/admin", adminRouter)
  .route("/config", configRouter);
