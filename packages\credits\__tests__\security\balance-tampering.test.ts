import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { TestFactories } from '../test-factories';
import { CreditError } from '../../src/types/credit-errors';

// Mock Prisma Client with enhanced security monitoring
const mockPrismaClient = {
  creditAccount: {
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    create: vi.fn(),
  },
  creditTransaction: {
    create: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
  },
  $transaction: vi.fn(),
  $executeRaw: vi.fn(),
  $queryRaw: vi.fn(),
} as any;

// Mock audit logger for security events
const mockAuditLogger = {
  logSecurityEvent: vi.fn(),
  logSuspiciousActivity: vi.fn(),
  logDataIntegrityCheck: vi.fn(),
};

describe('Security: Balance Tampering Protection', () => {
  let creditService: CreditService;
  let originalConsoleWarn: typeof console.warn;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient);
    
    // Mock console.warn to capture security warnings
    originalConsoleWarn = console.warn;
    console.warn = vi.fn();
  });

  afterEach(() => {
    console.warn = originalConsoleWarn;
  });

  describe('Direct Database Modification Detection', () => {
    it('should detect unauthorized balance modifications', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
        totalEarned: 1500,
        totalSpent: 500,
      });

      // Simulate a tampered account where balance doesn't match transaction history
      const tamperedAccount = {
        ...mockAccount,
        currentBalance: 5000, // Artificially inflated
        totalEarned: 1500,    // Original values
        totalSpent: 500,
      };

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(tamperedAccount);
      
      // Mock transaction history that doesn't match the balance
      const mockTransactions = [
        TestFactories.createCreditTransaction({
          type: 'EARNED_PURCHASE',
          amount: 1500,
          balanceAfter: 1500,
        }),
        TestFactories.createCreditTransaction({
          type: 'CONSUMED_FEATURE',
          amount: -500,
          balanceAfter: 1000,
        }),
      ];

      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(mockTransactions);

      // The service should detect the inconsistency
      await expect(async () => {
        await creditService.validateAccountIntegrity(tamperedAccount.id);
      }).rejects.toThrow('Account balance integrity violation detected');
    });

    it('should validate transaction chain integrity', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      // Create a broken transaction chain
      const brokenTransactions = [
        TestFactories.createCreditTransaction({
          balanceBefore: 0,
          balanceAfter: 100,
          amount: 100,
        }),
        TestFactories.createCreditTransaction({
          balanceBefore: 100,
          balanceAfter: 300, // Should be 150, not 300
          amount: 50,
        }),
      ];

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(brokenTransactions);

      await expect(async () => {
        await creditService.validateTransactionChain(mockAccount.id);
      }).rejects.toThrow('Transaction chain integrity violation');
    });

    it('should detect missing transactions for balance changes', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
        totalEarned: 2000,
        totalSpent: 1000,
      });

      // Mock incomplete transaction history (missing transactions)
      const incompleteTransactions = [
        TestFactories.createCreditTransaction({
          amount: 500,
          type: 'EARNED_PURCHASE',
        }),
        // Missing 1500 credits worth of transactions
      ];

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(incompleteTransactions);

      await expect(async () => {
        await creditService.validateAccountIntegrity(mockAccount.id);
      }).rejects.toThrow('Missing transactions detected');
    });
  });

  describe('API Parameter Tampering Protection', () => {
    it('should reject negative credit amounts', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: -100, // Negative amount
          reason: 'Malicious attempt',
        });
      }).rejects.toThrow('Invalid credit amount');
    });

    it('should reject zero credit amounts', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 0, // Zero amount
          reason: 'Invalid operation',
        });
      }).rejects.toThrow('Invalid credit amount');
    });

    it('should reject extremely large credit amounts', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const maxSafeAmount = Number.MAX_SAFE_INTEGER;

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: maxSafeAmount + 1, // Exceeds safe integer
          reason: 'Overflow attempt',
        });
      }).rejects.toThrow('Credit amount exceeds maximum allowed value');
    });

    it('should validate account ID format', async () => {
      await expect(async () => {
        await creditService.addCredits({
          accountId: 'invalid-id-format', // Invalid format
          amount: 100,
          reason: 'Test',
        });
      }).rejects.toThrow('Invalid account ID format');
    });

    it('should sanitize reason and description inputs', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(
        TestFactories.createCreditTransaction()
      );

      const maliciousReason = '<script>alert("xss")</script>';
      const sanitizedReason = 'alert("xss")'; // Expected sanitized version

      await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: maliciousReason,
      });

      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            reason: sanitizedReason,
          }),
        })
      );
    });
  });

  describe('Client-Side Data Validation', () => {
    it('should verify client-sent balance matches server calculation', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 1000,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      // Client sends incorrect balance
      const clientBalance = 2000;
      const serverBalance = 1000;

      const result = await creditService.verifyClientBalance({
        accountId: mockAccount.id,
        clientBalance,
      });

      expect(result.isValid).toBe(false);
      expect(result.serverBalance).toBe(serverBalance);
      expect(result.clientBalance).toBe(clientBalance);
      expect(result.discrepancy).toBe(clientBalance - serverBalance);
    });

    it('should log suspicious balance discrepancies', async () => {
      const mockAccount = TestFactories.createCreditAccount({
        currentBalance: 100,
      });

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      const suspiciousClientBalance = 10000; // 100x the actual balance

      await creditService.verifyClientBalance({
        accountId: mockAccount.id,
        clientBalance: suspiciousClientBalance,
      });

      // Should log security event for large discrepancy
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Suspicious balance discrepancy detected')
      );
    });
  });

  describe('Server-Side Data Validation', () => {
    it('should perform checksum validation on critical data', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      // Mock checksum calculation
      const expectedChecksum = 'abc123def456';
      const providedChecksum = 'xyz789uvw012'; // Different checksum

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      await expect(async () => {
        await creditService.createTransactionWithChecksum({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Test',
          checksum: providedChecksum,
        });
      }).rejects.toThrow('Data integrity checksum mismatch');
    });

    it('should validate transaction timestamps', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      // Transaction with future timestamp (suspicious)
      const futureTimestamp = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day in future

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      await expect(async () => {
        await creditService.addCredits({
          accountId: mockAccount.id,
          amount: 100,
          reason: 'Future transaction',
          timestamp: futureTimestamp,
        });
      }).rejects.toThrow('Invalid transaction timestamp');
    });

    it('should enforce rate limiting on credit operations', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(
        TestFactories.createCreditTransaction()
      );

      // Simulate rapid successive operations
      const promises = Array.from({ length: 10 }, (_, i) =>
        creditService.addCredits({
          accountId: mockAccount.id,
          amount: 10,
          reason: `Rapid operation ${i}`,
        })
      );

      // Should reject some operations due to rate limiting
      const results = await Promise.allSettled(promises);
      const rejectedCount = results.filter(r => r.status === 'rejected').length;
      
      expect(rejectedCount).toBeGreaterThan(0);
    });
  });

  describe('Audit Trail and Logging', () => {
    it('should log all balance modifications', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      const mockTransaction = TestFactories.createCreditTransaction();

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.create.mockResolvedValue(mockTransaction);

      await creditService.addCredits({
        accountId: mockAccount.id,
        amount: 100,
        reason: 'Test credit addition',
      });

      // Verify audit log entry was created
      expect(mockPrismaClient.creditTransaction.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            metadata: expect.objectContaining({
              auditTrail: expect.any(Object),
            }),
          }),
        })
      );
    });

    it('should create immutable audit records', async () => {
      const auditRecord = {
        id: 'audit-123',
        action: 'CREDIT_ADDED',
        accountId: 'account-123',
        amount: 100,
        timestamp: new Date(),
        userId: 'user-123',
        ipAddress: '***********',
        userAgent: 'Test Agent',
      };

      // Attempt to modify audit record should fail
      await expect(async () => {
        await creditService.modifyAuditRecord(auditRecord.id, {
          amount: 200, // Attempted modification
        });
      }).rejects.toThrow('Audit records are immutable');
    });

    it('should detect and log suspicious patterns', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);

      // Simulate suspicious pattern: multiple small transactions
      const suspiciousTransactions = Array.from({ length: 50 }, (_, i) => ({
        accountId: mockAccount.id,
        amount: 1, // Very small amounts
        reason: `Micro transaction ${i}`,
      }));

      for (const tx of suspiciousTransactions) {
        await creditService.addCredits(tx);
      }

      // Should detect and log the suspicious pattern
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Suspicious transaction pattern detected')
      );
    });
  });
});
