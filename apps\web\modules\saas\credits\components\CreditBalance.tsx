"use client";

import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { Coins, TrendingDown, TrendingUp } from "lucide-react";
import { useCreditBalance } from "../hooks/useCredits";

interface CreditBalanceProps {
  className?: string;
  showDetails?: boolean;
  variant?: "default" | "compact" | "detailed";
}

/**
 * 积分余额显示组件
 */
export function CreditBalance({ 
  className, 
  showDetails = false,
  variant = "default" 
}: CreditBalanceProps) {
  const { balance, totalEarned, totalConsumed, isLoading, error, hasAccount } = useCreditBalance();

  if (isLoading) {
    return <CreditBalanceSkeleton variant={variant} className={className} />;
  }

  if (error || !hasAccount) {
    return (
      <Card className={cn("border-destructive/20", className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-destructive">
            <Coins className="h-4 w-4" />
            <span className="text-sm">积分账户加载失败</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Coins className="h-4 w-4 text-primary" />
        <span className="font-medium">{balance.toLocaleString()}</span>
        <span className="text-sm text-muted-foreground">积分</span>
      </div>
    );
  }

  if (variant === "detailed") {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Coins className="h-5 w-5 text-primary" />
            积分账户
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 当前余额 */}
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">
              {balance.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">当前积分余额</div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-green-600">
                <TrendingUp className="h-4 w-4" />
                <span className="font-semibold">{totalEarned.toLocaleString()}</span>
              </div>
              <div className="text-xs text-muted-foreground">累计获得</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-orange-600">
                <TrendingDown className="h-4 w-4" />
                <span className="font-semibold">{totalConsumed.toLocaleString()}</span>
              </div>
              <div className="text-xs text-muted-foreground">累计消耗</div>
            </div>
          </div>

          {/* 余额状态 */}
          <div className="flex justify-center">
            <BalanceStatusBadge balance={balance} />
          </div>
        </CardContent>
      </Card>
    );
  }

  // 默认变体
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-primary" />
            <div>
              <div className="font-semibold">{balance.toLocaleString()} 积分</div>
              {showDetails && (
                <div className="text-xs text-muted-foreground">
                  获得 {totalEarned.toLocaleString()} · 消耗 {totalConsumed.toLocaleString()}
                </div>
              )}
            </div>
          </div>
          <BalanceStatusBadge balance={balance} />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 积分余额骨架屏
 */
function CreditBalanceSkeleton({ 
  variant = "default", 
  className 
}: { 
  variant?: "default" | "compact" | "detailed";
  className?: string;
}) {
  if (variant === "compact") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Skeleton className="h-4 w-4 rounded" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-8" />
      </div>
    );
  }

  if (variant === "detailed") {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <Skeleton className="h-5 w-24" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <Skeleton className="h-9 w-20 mx-auto mb-2" />
            <Skeleton className="h-4 w-16 mx-auto" />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <Skeleton className="h-5 w-12 mx-auto mb-1" />
              <Skeleton className="h-3 w-10 mx-auto" />
            </div>
            <div className="text-center">
              <Skeleton className="h-5 w-12 mx-auto mb-1" />
              <Skeleton className="h-3 w-10 mx-auto" />
            </div>
          </div>
          <div className="flex justify-center">
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded" />
            <div>
              <Skeleton className="h-5 w-20 mb-1" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
          <Skeleton className="h-5 w-12 rounded-full" />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * 余额状态徽章
 */
function BalanceStatusBadge({ balance }: { balance: number }) {
  if (balance >= 1000) {
    return <Badge status="success">充足</Badge>;
  }

  if (balance >= 100) {
    return <Badge status="info">正常</Badge>;
  }

  if (balance >= 10) {
    return <Badge status="warning">偏低</Badge>;
  }

  return <Badge status="error">不足</Badge>;
}

/**
 * 简单的积分显示组件（用于导航栏等）
 */
export function CreditBalanceSimple({ className }: { className?: string }) {
  return <CreditBalance variant="compact" className={className} />;
}

/**
 * 详细的积分显示组件（用于仪表板等）
 */
export function CreditBalanceDetailed({ className }: { className?: string }) {
  return <CreditBalance variant="detailed" showDetails className={className} />;
}
