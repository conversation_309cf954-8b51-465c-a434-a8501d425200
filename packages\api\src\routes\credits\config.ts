import { db } from "@repo/database";
import { ConfigurationService, SchedulerService } from "@repo/credits";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/auth";

// 创建服务实例
const configService = new ConfigurationService(db);
const schedulerService = new SchedulerService(db);

/**
 * 积分系统配置和维护管理路由
 */
export const configRouter = new Hono()
  // 验证配置
  .get(
    "/validate",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Config"],
      summary: "验证积分系统配置",
      description: "验证积分系统配置的完整性和正确性",
      responses: {
        200: {
          description: "配置验证结果",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  isValid: z.boolean(),
                  errors: z.array(z.string()),
                  warnings: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "验证失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await configService.validateConfiguration();
        return c.json(result);
      } catch (error) {
        logger.error("配置验证失败", { error });
        throw new HTTPException(500, {
          message: "配置验证失败",
        });
      }
    }
  )

  // 同步配置到数据库
  .post(
    "/sync",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Config"],
      summary: "同步配置到数据库",
      description: "将积分系统配置同步到数据库",
      responses: {
        200: {
          description: "同步结果",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  success: z.boolean(),
                  results: z.object({
                    packages: z.object({
                      created: z.number(),
                      updated: z.number(),
                      errors: z.array(z.string()),
                    }),
                  }),
                  errors: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "同步失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await configService.syncConfigurationToDatabase();
        return c.json(result);
      } catch (error) {
        logger.error("配置同步失败", { error });
        throw new HTTPException(500, {
          message: "配置同步失败",
        });
      }
    }
  )

  // 获取积分包配置
  .get(
    "/packages",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Config"],
      summary: "获取积分包配置",
      description: "获取所有积分包的配置信息",
      responses: {
        200: {
          description: "积分包配置列表",
          content: {
            "application/json": {
              schema: resolver(
                z.array(
                  z.object({
                    packageId: z.string(),
                    name: z.string(),
                    credits: z.number(),
                    price: z.number(),
                    currency: z.string(),
                    validityDays: z.number().nullable(),
                    featured: z.boolean(),
                    sortOrder: z.number(),
                    productIds: z.record(z.string()),
                  })
                )
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const packages = configService.getCreditPackageConfigs();
      return c.json(packages);
    }
  )

  // 获取功能消耗配置
  .get(
    "/features",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Config"],
      summary: "获取功能消耗配置",
      description: "获取所有功能的积分消耗配置",
      responses: {
        200: {
          description: "功能消耗配置列表",
          content: {
            "application/json": {
              schema: resolver(
                z.array(
                  z.object({
                    featureId: z.string(),
                    name: z.string(),
                    creditsPerUse: z.number(),
                    enabled: z.boolean(),
                    description: z.string().optional(),
                    category: z.string(),
                  })
                )
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const features = configService.getFeatureConsumptionConfigs();
      return c.json(features);
    }
  )

  // 获取订阅分配配置
  .get(
    "/subscriptions",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Config"],
      summary: "获取订阅分配配置",
      description: "获取所有订阅计划的积分分配配置",
      responses: {
        200: {
          description: "订阅分配配置列表",
          content: {
            "application/json": {
              schema: resolver(
                z.array(
                  z.object({
                    planId: z.string(),
                    allocation: z.object({
                      amount: z.number(),
                      period: z.string(),
                      intervalCount: z.number(),
                      validityDays: z.number().nullable(),
                      immediateAllocation: z.boolean(),
                      immediateAmount: z.number().optional(),
                    }),
                  })
                )
              ),
            },
          },
        },
      },
    }),
    async (c) => {
      const subscriptions = configService.getSubscriptionAllocationConfigs();
      return c.json(subscriptions);
    }
  )

  // 执行待处理的积分分配
  .post(
    "/execute-allocations",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Maintenance"],
      summary: "执行待处理的积分分配",
      description: "手动触发执行所有到期的积分分配",
      responses: {
        200: {
          description: "执行结果",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  processed: z.number(),
                  successful: z.number(),
                  failed: z.number(),
                  errors: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "执行失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await schedulerService.executePendingAllocations();
        return c.json(result);
      } catch (error) {
        logger.error("执行积分分配失败", { error });
        throw new HTTPException(500, {
          message: "执行积分分配失败",
        });
      }
    }
  )

  // 清理过期积分
  .post(
    "/cleanup-expired",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Maintenance"],
      summary: "清理过期积分",
      description: "清理所有过期的积分交易",
      responses: {
        200: {
          description: "清理结果",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  expiredTransactions: z.number(),
                  reclaimedCredits: z.number(),
                  errors: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "清理失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await schedulerService.cleanupExpiredCredits();
        return c.json(result);
      } catch (error) {
        logger.error("清理过期积分失败", { error });
        throw new HTTPException(500, {
          message: "清理过期积分失败",
        });
      }
    }
  )

  // 生成系统报告
  .get(
    "/report",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Maintenance"],
      summary: "生成积分系统报告",
      description: "生成积分系统的统计报告",
      responses: {
        200: {
          description: "系统报告",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  totalAccounts: z.number(),
                  totalCredits: z.number(),
                  dailyTransactions: z.number(),
                  activeAllocations: z.number(),
                  topConsumers: z.array(
                    z.object({
                      accountId: z.string(),
                      userId: z.string().optional(),
                      organizationId: z.string().optional(),
                      totalConsumed: z.number(),
                    })
                  ),
                  errors: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "生成报告失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await schedulerService.generateDailyReport();
        return c.json(result);
      } catch (error) {
        logger.error("生成系统报告失败", { error });
        throw new HTTPException(500, {
          message: "生成系统报告失败",
        });
      }
    }
  )

  // 执行完整维护任务
  .post(
    "/maintenance",
    adminMiddleware,
    describeRoute({
      tags: ["Credits Maintenance"],
      summary: "执行完整维护任务",
      description: "执行所有积分系统维护任务，包括分配执行、过期清理等",
      responses: {
        200: {
          description: "维护任务结果",
          content: {
            "application/json": {
              schema: resolver(
                z.object({
                  allocations: z.object({
                    processed: z.number(),
                    successful: z.number(),
                    failed: z.number(),
                    errors: z.array(z.string()),
                  }),
                  expiredCredits: z.object({
                    expiredTransactions: z.number(),
                    reclaimedCredits: z.number(),
                    errors: z.array(z.string()),
                  }),
                  expiredAllocations: z.object({
                    deactivated: z.number(),
                    errors: z.array(z.string()),
                  }),
                  report: z.object({
                    totalAccounts: z.number(),
                    totalCredits: z.number(),
                    dailyTransactions: z.number(),
                    activeAllocations: z.number(),
                    topConsumers: z.array(z.any()),
                    errors: z.array(z.string()),
                  }),
                  errors: z.array(z.string()),
                })
              ),
            },
          },
        },
        500: {
          description: "维护任务失败",
        },
      },
    }),
    async (c) => {
      try {
        const result = await schedulerService.runMaintenanceTasks();
        return c.json(result);
      } catch (error) {
        logger.error("执行维护任务失败", { error });
        throw new HTTPException(500, {
          message: "执行维护任务失败",
        });
      }
    }
  );
