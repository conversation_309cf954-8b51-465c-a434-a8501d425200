import { describe, it, expect } from 'vitest';
import {
  FEATURE_CREDIT_COSTS,
  SUBSCRIPTION_CREDIT_ALLOCATIONS,
  DEFAULT_CREDIT_CONFIG,
  getFeatureCreditCost,
  getSubscriptionCreditAllocation,
  isFeatureAvailable,
  getFeaturesByCategory,
  calculateNextAllocationDate,
  calculateExpirationDate,
  validateCreditAmount,
  validateTransferAmount,
  isLowBalance,
  isExpirationWarningNeeded
} from '../../src/config/credit-config';
import type { CreditPeriod, CreditExpirationPolicy } from '../../src/types/credit-types';
import { TestUtils } from '../test-factories';

describe('credit-config', () => {
  describe('FEATURE_CREDIT_COSTS', () => {
    it('should contain all expected features', () => {
      const expectedFeatures = [
        'AI_CHAT',
        'MAP_EXPORT',
        'VIDEO_GENERATION',
        'PREMIUM_TEMPLATE',
        'BATCH_OPERATION',
        'DATA_ANALYTICS',
        'CUSTOM_THEME',
        'ADVANCED_SEARCH',
        'DATA_BACKUP',
        'API_CALL'
      ];

      expectedFeatures.forEach(feature => {
        expect(FEATURE_CREDIT_COSTS[feature]).toBeDefined();
        expect(FEATURE_CREDIT_COSTS[feature].cost).toBeGreaterThan(0);
        expect(FEATURE_CREDIT_COSTS[feature].featureId).toBeTruthy();
        expect(FEATURE_CREDIT_COSTS[feature].name).toBeTruthy();
        expect(FEATURE_CREDIT_COSTS[feature].category).toBeTruthy();
      });
    });

    it('should have consistent structure for all features', () => {
      Object.values(FEATURE_CREDIT_COSTS).forEach(feature => {
        expect(feature).toHaveProperty('featureId');
        expect(feature).toHaveProperty('name');
        expect(feature).toHaveProperty('description');
        expect(feature).toHaveProperty('cost');
        expect(feature).toHaveProperty('category');
        expect(typeof feature.cost).toBe('number');
        expect(feature.cost).toBeGreaterThan(0);
      });
    });
  });

  describe('SUBSCRIPTION_CREDIT_ALLOCATIONS', () => {
    it('should contain all subscription plans', () => {
      const expectedPlans = ['FREE', 'BASIC', 'PRO', 'ENTERPRISE'];

      expectedPlans.forEach(plan => {
        expect(SUBSCRIPTION_CREDIT_ALLOCATIONS[plan]).toBeDefined();
        expect(SUBSCRIPTION_CREDIT_ALLOCATIONS[plan].creditsPerPeriod).toBeGreaterThan(0);
      });
    });

    it('should have increasing credit amounts for higher tiers', () => {
      const free = SUBSCRIPTION_CREDIT_ALLOCATIONS.FREE.creditsPerPeriod;
      const basic = SUBSCRIPTION_CREDIT_ALLOCATIONS.BASIC.creditsPerPeriod;
      const pro = SUBSCRIPTION_CREDIT_ALLOCATIONS.PRO.creditsPerPeriod;
      const enterprise = SUBSCRIPTION_CREDIT_ALLOCATIONS.ENTERPRISE.creditsPerPeriod;

      expect(basic).toBeGreaterThan(free);
      expect(pro).toBeGreaterThan(basic);
      expect(enterprise).toBeGreaterThan(pro);
    });

    it('should have appropriate expiration policies', () => {
      expect(SUBSCRIPTION_CREDIT_ALLOCATIONS.FREE.expirationPolicy).toBe('MONTHLY_RESET');
      expect(SUBSCRIPTION_CREDIT_ALLOCATIONS.BASIC.expirationPolicy).toBe('ROLLING_EXPIRATION');
      expect(SUBSCRIPTION_CREDIT_ALLOCATIONS.PRO.expirationPolicy).toBe('ROLLING_EXPIRATION');
      expect(SUBSCRIPTION_CREDIT_ALLOCATIONS.ENTERPRISE.expirationPolicy).toBe('NEVER_EXPIRE');
    });
  });

  describe('getFeatureCreditCost', () => {
    it('should return correct cost for existing features', () => {
      expect(getFeatureCreditCost('ai_chat')).toBe(5);
      expect(getFeatureCreditCost('map_export')).toBe(20);
      expect(getFeatureCreditCost('video_generation')).toBe(50);
    });

    it('should return 0 for non-existing features', () => {
      expect(getFeatureCreditCost('non_existing_feature')).toBe(0);
      expect(getFeatureCreditCost('')).toBe(0);
    });
  });

  describe('getSubscriptionCreditAllocation', () => {
    it('should return allocation for existing plans', () => {
      const basicAllocation = getSubscriptionCreditAllocation('basic');
      expect(basicAllocation).toBeDefined();
      expect(basicAllocation?.planId).toBe('basic');
      expect(basicAllocation?.creditsPerPeriod).toBe(200);
    });

    it('should be case insensitive', () => {
      const allocation1 = getSubscriptionCreditAllocation('BASIC');
      const allocation2 = getSubscriptionCreditAllocation('basic');
      const allocation3 = getSubscriptionCreditAllocation('Basic');

      expect(allocation1).toEqual(allocation2);
      expect(allocation2).toEqual(allocation3);
    });

    it('should return null for non-existing plans', () => {
      expect(getSubscriptionCreditAllocation('non_existing_plan')).toBeNull();
      expect(getSubscriptionCreditAllocation('')).toBeNull();
    });
  });

  describe('isFeatureAvailable', () => {
    it('should return true for existing features', () => {
      expect(isFeatureAvailable('ai_chat')).toBe(true);
      expect(isFeatureAvailable('map_export')).toBe(true);
    });

    it('should return false for non-existing features', () => {
      expect(isFeatureAvailable('non_existing_feature')).toBe(false);
      expect(isFeatureAvailable('')).toBe(false);
    });
  });

  describe('getFeaturesByCategory', () => {
    it('should return features for existing categories', () => {
      const aiFeatures = getFeaturesByCategory('ai');
      expect(aiFeatures.length).toBeGreaterThan(0);
      expect(aiFeatures.every(f => f.category === 'ai')).toBe(true);

      const exportFeatures = getFeaturesByCategory('export');
      expect(exportFeatures.length).toBeGreaterThan(0);
      expect(exportFeatures.every(f => f.category === 'export')).toBe(true);
    });

    it('should return empty array for non-existing categories', () => {
      const features = getFeaturesByCategory('non_existing_category');
      expect(features).toEqual([]);
    });
  });

  describe('calculateNextAllocationDate', () => {
    it('should calculate next allocation for different periods', () => {
      const baseDate = new Date('2024-01-15T10:00:00Z');

      const daily = calculateNextAllocationDate('DAILY', baseDate);
      expect(daily.getDate()).toBe(16);

      const weekly = calculateNextAllocationDate('WEEKLY', baseDate);
      expect(weekly.getDate()).toBe(22);

      const monthly = calculateNextAllocationDate('MONTHLY', baseDate);
      expect(monthly.getMonth()).toBe(1); // February

      const yearly = calculateNextAllocationDate('YEARLY', baseDate);
      expect(yearly.getFullYear()).toBe(2025);
    });

    it('should use current date when no base date provided', () => {
      const before = new Date();
      const result = calculateNextAllocationDate('DAILY');
      const after = new Date();

      expect(result.getTime()).toBeGreaterThan(before.getTime());
      expect(result.getTime()).toBeLessThan(after.getTime() + 24 * 60 * 60 * 1000);
    });
  });

  describe('calculateExpirationDate', () => {
    it('should return null for NEVER_EXPIRE policy', () => {
      const result = calculateExpirationDate('NEVER_EXPIRE');
      expect(result).toBeNull();
    });

    it('should calculate MONTHLY_RESET expiration', () => {
      const baseDate = new Date('2024-01-15T10:00:00Z');
      const result = calculateExpirationDate('MONTHLY_RESET', undefined, baseDate);

      expect(result?.getMonth()).toBe(1); // February
      expect(result?.getDate()).toBe(1);
      expect(result?.getHours()).toBe(0);
    });

    it('should calculate ROLLING_EXPIRATION with custom days', () => {
      const baseDate = new Date('2024-01-15T10:00:00Z');
      const result = calculateExpirationDate('ROLLING_EXPIRATION', 30, baseDate);

      expect(result?.getDate()).toBe(14); // 30 days later (February 14)
      expect(result?.getMonth()).toBe(1); // February
    });

    it('should use default expiration days when not specified', () => {
      const baseDate = new Date('2024-01-15T10:00:00Z');
      const result = calculateExpirationDate('ROLLING_EXPIRATION', undefined, baseDate);

      // Should use DEFAULT_CREDIT_CONFIG.defaultExpirationDays (365)
      expect(result?.getFullYear()).toBe(2025);
    });

    it('should calculate SUBSCRIPTION_BASED expiration', () => {
      const baseDate = new Date('2024-01-15T10:00:00Z');
      const result = calculateExpirationDate('SUBSCRIPTION_BASED', 90, baseDate);

      expect(result?.getDate()).toBe(14); // 90 days later
      expect(result?.getMonth()).toBe(3); // April
    });
  });

  describe('validation functions', () => {
    describe('validateCreditAmount', () => {
      it('should validate credit amounts correctly', () => {
        expect(validateCreditAmount(100)).toBe(true);
        expect(validateCreditAmount(1)).toBe(true);
        expect(validateCreditAmount(DEFAULT_CREDIT_CONFIG.maxTransactionAmount)).toBe(true);

        expect(validateCreditAmount(0)).toBe(false);
        expect(validateCreditAmount(-100)).toBe(false);
        expect(validateCreditAmount(1.5)).toBe(false);
        expect(validateCreditAmount(DEFAULT_CREDIT_CONFIG.maxTransactionAmount + 1)).toBe(false);
      });
    });

    describe('validateTransferAmount', () => {
      it('should validate transfer amounts correctly', () => {
        expect(validateTransferAmount(100)).toBe(true);
        expect(validateTransferAmount(DEFAULT_CREDIT_CONFIG.minTransferAmount)).toBe(true);
        expect(validateTransferAmount(DEFAULT_CREDIT_CONFIG.maxTransferAmount)).toBe(true);

        expect(validateTransferAmount(0)).toBe(false);
        expect(validateTransferAmount(DEFAULT_CREDIT_CONFIG.minTransferAmount - 1)).toBe(false);
        expect(validateTransferAmount(DEFAULT_CREDIT_CONFIG.maxTransferAmount + 1)).toBe(false);
      });
    });

    describe('isLowBalance', () => {
      it('should identify low balance correctly', () => {
        expect(isLowBalance(5)).toBe(true);
        expect(isLowBalance(DEFAULT_CREDIT_CONFIG.lowBalanceThreshold)).toBe(true);
        expect(isLowBalance(DEFAULT_CREDIT_CONFIG.lowBalanceThreshold + 1)).toBe(false);
        expect(isLowBalance(100)).toBe(false);
      });
    });

    describe('isExpirationWarningNeeded', () => {
      it('should identify when expiration warning is needed', () => {
        const warningDays = DEFAULT_CREDIT_CONFIG.expirationWarningDays;
        const soonExpiring = TestUtils.createFutureDate(warningDays - 1);
        const farExpiring = TestUtils.createFutureDate(warningDays + 1);
        const alreadyExpired = TestUtils.createPastDate(1);

        expect(isExpirationWarningNeeded(soonExpiring)).toBe(true);
        expect(isExpirationWarningNeeded(farExpiring)).toBe(false);
        expect(isExpirationWarningNeeded(alreadyExpired)).toBe(false);
      });

      it('should return false for null expiration date', () => {
        expect(isExpirationWarningNeeded(null as any)).toBe(false);
      });
    });
  });

  describe('DEFAULT_CREDIT_CONFIG', () => {
    it('should have all required configuration properties', () => {
      const requiredProps = [
        'defaultCurrency',
        'minTransferAmount',
        'maxTransferAmount',
        'maxDailyTransfers',
        'defaultExpirationDays',
        'expirationCheckInterval',
        'allocationGracePeriod',
        'maxPendingAllocations',
        'maxTransactionAmount',
        'suspiciousActivityThreshold',
        'balanceCacheTTL',
        'transactionCacheTTL',
        'lowBalanceThreshold',
        'expirationWarningDays'
      ];

      requiredProps.forEach(prop => {
        expect(DEFAULT_CREDIT_CONFIG).toHaveProperty(prop);
        expect(DEFAULT_CREDIT_CONFIG[prop as keyof typeof DEFAULT_CREDIT_CONFIG]).toBeDefined();
      });
    });

    it('should have reasonable default values', () => {
      expect(DEFAULT_CREDIT_CONFIG.defaultCurrency).toBe('USD');
      expect(DEFAULT_CREDIT_CONFIG.minTransferAmount).toBeGreaterThan(0);
      expect(DEFAULT_CREDIT_CONFIG.maxTransferAmount).toBeGreaterThan(DEFAULT_CREDIT_CONFIG.minTransferAmount);
      expect(DEFAULT_CREDIT_CONFIG.defaultExpirationDays).toBeGreaterThan(0);
      expect(DEFAULT_CREDIT_CONFIG.lowBalanceThreshold).toBeGreaterThan(0);
    });
  });
});
