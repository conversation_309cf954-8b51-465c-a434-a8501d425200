import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CreditService } from '../../src/services/CreditService';
import { CreditAllocationService } from '../../src/services/CreditAllocationService';
import { CreditPackageService } from '../../src/services/CreditPackageService';
import { TestFactories, TestUtils } from '../test-factories';

// Mock Prisma Client with more comprehensive mocking
const mockPrismaClient = {
  creditAccount: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn()
  },
  creditTransaction: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn()
  },
  creditAllocation: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    update: vi.fn(),
    updateMany: vi.fn()
  },
  creditPackage: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findUnique: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    update: vi.fn()
  },
  $transaction: vi.fn()
} as any;

describe('Credit System Integration Tests', () => {
  let creditService: CreditService;
  let allocationService: CreditAllocationService;
  let packageService: CreditPackageService;

  beforeEach(() => {
    vi.clearAllMocks();
    creditService = new CreditService(mockPrismaClient);
    allocationService = new CreditAllocationService(mockPrismaClient, creditService);
    packageService = new CreditPackageService(mockPrismaClient, creditService);
  });

  describe('Complete User Journey', () => {
    it('should handle complete user credit lifecycle', async () => {
      // Step 1: Create user account
      const mockAccount = TestFactories.createCreditAccount();
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAccount.create.mockResolvedValue(mockAccount);

      const account = await creditService.createAccount({
        userId: 'user-123',
        initialBalance: 0
      });

      expect(account).toEqual(mockAccount);

      // Step 2: Create subscription allocation
      const mockAllocation = TestFactories.createCreditAllocation();
      mockPrismaClient.creditAllocation.findFirst.mockResolvedValue(null);
      mockPrismaClient.creditAllocation.create.mockResolvedValue(mockAllocation);

      const allocation = await allocationService.createAllocationFromSubscription(
        account.id,
        'basic'
      );

      expect(allocation).toEqual(mockAllocation);

      // Step 3: Execute allocation (add subscription credits)
      const mockTransaction = TestFactories.createCreditTransaction({
        type: 'EARNED_SUBSCRIPTION',
        amount: 200
      });

      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue({
        ...mockAllocation,
        nextAllocationAt: TestUtils.createPastDate(1) // Due for allocation
      });
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      
      // Mock the transaction creation process
      mockPrismaClient.$transaction.mockImplementation(async (callback) => {
        return callback({
          creditAccount: {
            findUnique: vi.fn().mockResolvedValue(mockAccount),
            update: vi.fn().mockResolvedValue({ ...mockAccount, currentBalance: 200 })
          },
          creditTransaction: {
            create: vi.fn().mockResolvedValue(mockTransaction)
          }
        });
      });

      vi.spyOn(creditService, 'addCredits').mockResolvedValue(mockTransaction);
      mockPrismaClient.creditAllocation.update.mockResolvedValue(mockAllocation);

      const allocationResult = await allocationService.executeAllocation(allocation.id);

      expect(allocationResult.success).toBe(true);
      expect(allocationResult.transaction).toEqual(mockTransaction);

      // Step 4: Purchase credit package
      const mockPackage = TestFactories.createCreditPackage();
      const mockPurchaseTransaction = TestFactories.createCreditTransaction({
        type: 'EARNED_PURCHASE',
        amount: 500
      });

      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(mockPackage);
      vi.spyOn(creditService, 'getOrCreateAccount').mockResolvedValue(mockAccount);
      vi.spyOn(creditService, 'addCredits').mockResolvedValue(mockPurchaseTransaction);

      const purchaseResult = await packageService.processPurchase({
        packageId: mockPackage.id,
        userId: 'user-123',
        purchaseId: 'purchase-456',
        paymentProvider: 'stripe'
      });

      expect(purchaseResult).toEqual(mockPurchaseTransaction);

      // Step 5: Consume credits for feature usage
      const mockConsumptionTransaction = TestFactories.createCreditTransaction({
        type: 'SPENT_FEATURE',
        amount: -50
      });

      vi.spyOn(creditService, 'createTransaction').mockResolvedValue(mockConsumptionTransaction);

      const consumptionResult = await creditService.consumeCredits({
        accountId: account.id,
        amount: 50,
        reason: 'AI chat usage',
        featureId: 'ai_chat'
      });

      expect(consumptionResult).toEqual(mockConsumptionTransaction);

      // Step 6: Check final balance
      const finalAccount = TestFactories.createCreditAccount({ currentBalance: 650 }); // 200 + 500 - 50
      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(finalAccount);

      const finalBalance = await creditService.getBalance({ userId: 'user-123' });

      expect(finalBalance).toBe(650);
    });
  });

  describe('Credit Transfer Flow', () => {
    it('should handle credit transfer between accounts', async () => {
      const fromAccount = TestFactories.createCreditAccount({ 
        id: 'account-from',
        currentBalance: 1000 
      });
      const toAccount = TestFactories.createCreditAccount({ 
        id: 'account-to',
        currentBalance: 500 
      });

      // Mock transfer transactions
      const transferOutTransaction = TestFactories.createCreditTransaction({
        type: 'SPENT_TRANSFER',
        amount: -200,
        accountId: 'account-from'
      });
      const transferInTransaction = TestFactories.createCreditTransaction({
        type: 'RECEIVED_TRANSFER',
        amount: 200,
        accountId: 'account-to'
      });

      vi.spyOn(creditService, 'createTransaction')
        .mockResolvedValueOnce(transferOutTransaction)
        .mockResolvedValueOnce(transferInTransaction);

      const transferResult = await creditService.transferCredits({
        fromAccountId: 'account-from',
        toAccountId: 'account-to',
        amount: 200,
        reason: 'Gift transfer'
      });

      expect(transferResult.fromTransaction).toEqual(transferOutTransaction);
      expect(transferResult.toTransaction).toEqual(transferInTransaction);
    });
  });

  describe('Batch Allocation Processing', () => {
    it('should process multiple due allocations', async () => {
      const dueAllocations = [
        TestFactories.createCreditAllocation({
          id: 'allocation-1',
          accountId: 'account-1',
          nextAllocationAt: TestUtils.createPastDate(1)
        }),
        TestFactories.createCreditAllocation({
          id: 'allocation-2',
          accountId: 'account-2',
          nextAllocationAt: TestUtils.createPastDate(2)
        }),
        TestFactories.createCreditAllocation({
          id: 'allocation-3',
          accountId: 'account-3',
          nextAllocationAt: TestUtils.createPastDate(3)
        })
      ];

      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(dueAllocations);

      // Mock successful execution for all allocations
      vi.spyOn(allocationService, 'executeAllocation').mockImplementation(async (id) => {
        return {
          success: true,
          transaction: TestFactories.createCreditTransaction({ id: `tx-${id}` })
        };
      });

      const batchResult = await allocationService.executeAllDueAllocations();

      expect(batchResult.processed).toBe(3);
      expect(batchResult.successful).toBe(3);
      expect(batchResult.failed).toBe(0);
      expect(batchResult.errors).toHaveLength(0);
    });

    it('should handle partial failures in batch processing', async () => {
      const dueAllocations = [
        TestFactories.createCreditAllocation({ id: 'allocation-1' }),
        TestFactories.createCreditAllocation({ id: 'allocation-2' }),
        TestFactories.createCreditAllocation({ id: 'allocation-3' })
      ];

      mockPrismaClient.creditAllocation.findMany.mockResolvedValue(dueAllocations);

      // Mock mixed success/failure results
      vi.spyOn(allocationService, 'executeAllocation')
        .mockResolvedValueOnce({ 
          success: true, 
          transaction: TestFactories.createCreditTransaction() 
        })
        .mockRejectedValueOnce(new Error('Database error'))
        .mockResolvedValueOnce({ 
          success: true, 
          transaction: TestFactories.createCreditTransaction() 
        });

      const batchResult = await allocationService.executeAllDueAllocations();

      expect(batchResult.processed).toBe(3);
      expect(batchResult.successful).toBe(2);
      expect(batchResult.failed).toBe(1);
      expect(batchResult.errors).toHaveLength(1);
      expect(batchResult.errors[0]).toContain('Database error');
    });
  });

  describe('Package Purchase and Refund Flow', () => {
    it('should handle complete purchase and refund cycle', async () => {
      // Step 1: Purchase package
      const mockPackage = TestFactories.createCreditPackage();
      const mockAccount = TestFactories.createCreditAccount();
      const mockPurchaseTransaction = TestFactories.createCreditTransaction({
        type: 'EARNED_PURCHASE',
        amount: 500,
        purchaseId: 'purchase-456'
      });

      mockPrismaClient.creditPackage.findUnique.mockResolvedValue(mockPackage);
      vi.spyOn(creditService, 'getOrCreateAccount').mockResolvedValue(mockAccount);
      vi.spyOn(creditService, 'addCredits').mockResolvedValue(mockPurchaseTransaction);

      const purchaseResult = await packageService.processPurchase({
        packageId: mockPackage.id,
        userId: 'user-123',
        purchaseId: 'purchase-456',
        paymentProvider: 'stripe'
      });

      expect(purchaseResult).toEqual(mockPurchaseTransaction);

      // Step 2: Process refund
      const mockRefundTransaction = TestFactories.createCreditTransaction({
        type: 'REFUNDED',
        amount: -500
      });

      vi.spyOn(creditService, 'getTransaction').mockResolvedValue(mockPurchaseTransaction);
      vi.spyOn(creditService, 'consumeCredits').mockResolvedValue(mockRefundTransaction);

      const refundResult = await packageService.processRefund({
        purchaseId: 'purchase-456',
        reason: 'Customer request'
      });

      expect(refundResult).toEqual(mockRefundTransaction);
    });
  });

  describe('Account Suspension and Recovery', () => {
    it('should handle account suspension and recovery flow', async () => {
      const mockAccount = TestFactories.createCreditAccount({ status: 'ACTIVE' });

      // Step 1: Suspend account
      const suspendedAccount = { ...mockAccount, status: 'SUSPENDED' as const };
      mockPrismaClient.creditAccount.update.mockResolvedValue(suspendedAccount);

      const suspendResult = await creditService.updateAccountStatus(mockAccount.id, 'SUSPENDED');
      expect(suspendResult.status).toBe('SUSPENDED');

      // Step 2: Pause allocations
      mockPrismaClient.creditAllocation.updateMany.mockResolvedValue({ count: 2 });

      const pausedCount = await allocationService.pauseAccountAllocations(mockAccount.id);
      expect(pausedCount).toBe(2);

      // Step 3: Reactivate account
      const reactivatedAccount = { ...mockAccount, status: 'ACTIVE' as const };
      mockPrismaClient.creditAccount.update.mockResolvedValue(reactivatedAccount);

      const reactivateResult = await creditService.updateAccountStatus(mockAccount.id, 'ACTIVE');
      expect(reactivateResult.status).toBe('ACTIVE');

      // Step 4: Resume allocations
      const resumedCount = await allocationService.resumeAccountAllocations(mockAccount.id);
      expect(resumedCount).toBe(2);
    });
  });

  describe('Credit Expiration Handling', () => {
    it('should handle credit expiration scenarios', async () => {
      const mockAccount = TestFactories.createCreditAccount();
      
      // Create transactions with different expiration dates
      const transactions = [
        TestFactories.createCreditTransaction({ 
          amount: 100, 
          expiresAt: TestUtils.createFutureDate(30) // Valid
        }),
        TestFactories.createExpiredTransaction({ amount: 200 }), // Expired
        TestFactories.createCreditTransaction({ 
          amount: 300, 
          expiresAt: null // Never expires
        })
      ];

      mockPrismaClient.creditAccount.findFirst.mockResolvedValue(mockAccount);
      mockPrismaClient.creditTransaction.findMany.mockResolvedValue(transactions);

      // Calculate available balance (should exclude expired credits)
      const { calculateAvailableBalance } = await import('../../src/utils/credit-helpers');
      const availableBalance = calculateAvailableBalance(mockAccount, transactions);

      expect(availableBalance).toBe(400); // 100 + 300, excluding expired 200
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle database transaction failures gracefully', async () => {
      mockPrismaClient.$transaction.mockRejectedValue(new Error('Database connection lost'));

      await expect(creditService.createTransaction({
        accountId: 'account-123',
        type: 'EARNED_PURCHASE',
        amount: 100,
        reason: 'Test transaction'
      })).rejects.toThrow('Database operation failed');
    });

    it('should handle concurrent allocation execution', async () => {
      const mockAllocation = TestFactories.createCreditAllocation({
        nextAllocationAt: TestUtils.createPastDate(1)
      });

      mockPrismaClient.creditAllocation.findUnique.mockResolvedValue(mockAllocation);

      // Simulate concurrent execution attempts
      const promises = Array.from({ length: 3 }, () => 
        allocationService.executeAllocation(mockAllocation.id)
      );

      // At least one should succeed, others should handle gracefully
      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      
      expect(successful).toBeGreaterThan(0);
    });
  });
});
