import { describe, it, expect, beforeEach, vi } from "vitest";
import { testClient } from "hono/testing";
import { Hono } from "hono";
import { creditsRouter } from "../router";

// Mock dependencies
vi.mock("@repo/credits", () => ({
  CreditService: vi.fn().mockImplementation(() => ({
    getOrCreateAccount: vi.fn(),
    getBalance: vi.fn(),
    createAccount: vi.fn(),
    updateAccountStatus: vi.fn(),
    getTransactionHistory: vi.fn(),
    getTransaction: vi.fn(),
    consumeCredits: vi.fn(),
    addCredits: vi.fn(),
    transferCredits: vi.fn(),
    getAllAccounts: vi.fn(),
    getAccountStatistics: vi.fn(),
    getTransactionStatistics: vi.fn(),
    cleanupExpiredCredits: vi.fn(),
    recalculateAllBalances: vi.fn(),
  })),
  CreditAllocationService: vi.fn().mockImplementation(() => ({
    getAllocations: vi.fn(),
    createAllocationFromSubscription: vi.fn(),
    updateAllocation: vi.fn(),
    getAllocation: vi.fn(),
    executeAllocation: vi.fn(),
    executeAllDueAllocations: vi.fn(),
    getAllocationStatistics: vi.fn(),
  })),
  CreditPackageService: vi.fn().mockImplementation(() => ({
    getPackages: vi.fn(),
    getPackage: vi.fn(),
    createPackage: vi.fn(),
    updatePackage: vi.fn(),
    processPurchase: vi.fn(),
    processRefund: vi.fn(),
    getPackageStatistics: vi.fn(),
  })),
}));

vi.mock("@repo/database", () => ({
  db: {},
}));

vi.mock("@repo/logs", () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock auth middleware
vi.mock("../../middleware/auth", () => ({
  authMiddleware: vi.fn().mockImplementation((c, next) => {
    c.set("user", {
      id: "test-user-id",
      email: "<EMAIL>",
      role: "user",
      organizationId: "test-org-id",
    });
    return next();
  }),
}));

// Mock admin middleware
vi.mock("../../middleware/admin", () => ({
  adminMiddleware: vi.fn().mockImplementation((c, next) => {
    const user = c.get("user");
    if (user?.role !== "admin") {
      user.role = "admin"; // Force admin role for tests
    }
    return next();
  }),
}));

describe("Credits API Routes", () => {
  let app: Hono;
  let client: ReturnType<typeof testClient>;

  beforeEach(() => {
    app = new Hono().route("/credits", creditsRouter);
    client = testClient(app);
    vi.clearAllMocks();
  });

  describe("Account Routes", () => {
    it("should get account info", async () => {
      const mockAccount = {
        id: "account-1",
        userId: "test-user-id",
        organizationId: null,
        currentBalance: 100,
        totalEarned: 200,
        totalSpent: 100,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const { CreditService } = await import("@repo/credits");
      const mockService = new CreditService({} as any);
      vi.mocked(mockService.getOrCreateAccount).mockResolvedValue(mockAccount);
      vi.mocked(mockService.getBalance).mockResolvedValue(100);

      const response = await client.credits.account.$get();
      expect(response.status).toBe(200);
    });

    it("should create account", async () => {
      const mockAccount = {
        id: "account-1",
        userId: "test-user-id",
        organizationId: null,
        currentBalance: 50,
        totalEarned: 50,
        totalSpent: 0,
        status: "ACTIVE",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const { CreditService } = await import("@repo/credits");
      const mockService = new CreditService({} as any);
      vi.mocked(mockService.createAccount).mockResolvedValue(mockAccount);

      const response = await client.credits.account.$post({
        json: {
          initialBalance: 50,
        },
      });
      expect(response.status).toBe(201);
    });
  });

  describe("Transaction Routes", () => {
    it("should get transaction history", async () => {
      const mockTransactions = {
        data: [
          {
            id: "tx-1",
            accountId: "account-1",
            type: "CONSUME",
            amount: 10,
            reason: "Test transaction",
            featureId: "test_feature",
            purchaseId: null,
            expiresAt: null,
            metadata: {},
            createdAt: new Date(),
          },
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
        },
      };

      const { CreditService } = await import("@repo/credits");
      const mockService = new CreditService({} as any);
      vi.mocked(mockService.getOrCreateAccount).mockResolvedValue({
        id: "account-1",
      } as any);
      vi.mocked(mockService.getTransactionHistory).mockResolvedValue(mockTransactions);

      const response = await client.credits.transactions.$get();
      expect(response.status).toBe(200);
    });

    it("should consume credits", async () => {
      const mockTransaction = {
        id: "tx-1",
        accountId: "account-1",
        type: "CONSUME",
        amount: 10,
        reason: "Test consumption",
        featureId: "test_feature",
        purchaseId: null,
        expiresAt: null,
        metadata: {},
        createdAt: new Date(),
      };

      const { CreditService } = await import("@repo/credits");
      const mockService = new CreditService({} as any);
      vi.mocked(mockService.getOrCreateAccount).mockResolvedValue({
        id: "account-1",
      } as any);
      vi.mocked(mockService.consumeCredits).mockResolvedValue(mockTransaction);

      const response = await client.credits.transactions.consume.$post({
        json: {
          amount: 10,
          reason: "Test consumption",
          featureId: "test_feature",
        },
      });
      expect(response.status).toBe(200);
    });
  });

  describe("Package Routes", () => {
    it("should get package list", async () => {
      const mockPackages = {
        data: [
          {
            id: "pkg-1",
            name: "Basic Package",
            description: "Basic credit package",
            credits: 100,
            price: 10.00,
            currency: "USD",
            validityDays: 30,
            isActive: true,
            isFeatured: false,
            salesCount: 0,
            totalRevenue: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
        },
      };

      const { CreditPackageService } = await import("@repo/credits");
      const mockService = new CreditPackageService({} as any, {} as any);
      vi.mocked(mockService.getPackages).mockResolvedValue(mockPackages);

      const response = await client.credits.packages.$get();
      expect(response.status).toBe(200);
    });

    it("should purchase package", async () => {
      const mockTransaction = {
        id: "tx-1",
        accountId: "account-1",
        type: "PURCHASE",
        amount: 100,
        reason: "Package purchase",
        featureId: null,
        purchaseId: "purchase-1",
        expiresAt: null,
        metadata: {},
        createdAt: new Date(),
      };

      const mockPackage = {
        id: "pkg-1",
        name: "Basic Package",
        description: "Basic credit package",
        credits: 100,
        price: 10.00,
        currency: "USD",
        validityDays: 30,
        isActive: true,
        isFeatured: false,
        salesCount: 1,
        totalRevenue: 10.00,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const { CreditPackageService } = await import("@repo/credits");
      const mockService = new CreditPackageService({} as any, {} as any);
      vi.mocked(mockService.processPurchase).mockResolvedValue(mockTransaction);
      vi.mocked(mockService.getPackage).mockResolvedValue(mockPackage);

      const response = await client.credits.packages["pkg-1"].purchase.$post({
        json: {
          paymentProvider: "stripe",
          paymentMethodId: "pm_test_123",
        },
      });
      expect(response.status).toBe(200);
    });
  });

  describe("Admin Routes", () => {
    it("should get analytics data", async () => {
      const mockAnalytics = {
        overview: {
          totalAccounts: 100,
          activeAccounts: 95,
          totalCreditsIssued: 10000,
          totalCreditsConsumed: 5000,
          totalCreditsBalance: 5000,
        },
        transactions: {
          totalTransactions: 500,
          recentTransactions: 50,
          topConsumingFeatures: [],
        },
        packages: {
          totalPackages: 5,
          activePackages: 4,
          totalSales: 200,
          totalRevenue: 2000,
          topSellingPackages: [],
        },
        allocations: {
          totalAllocations: 50,
          activeAllocations: 45,
          totalAllocated: 3000,
          upcomingAllocations: 10,
        },
      };

      const { CreditService, CreditPackageService, CreditAllocationService } = await import("@repo/credits");
      const mockCreditService = new CreditService({} as any);
      const mockPackageService = new CreditPackageService({} as any, {} as any);
      const mockAllocationService = new CreditAllocationService({} as any, {} as any);

      vi.mocked(mockCreditService.getAccountStatistics).mockResolvedValue(mockAnalytics.overview);
      vi.mocked(mockCreditService.getTransactionStatistics).mockResolvedValue(mockAnalytics.transactions);
      vi.mocked(mockPackageService.getPackageStatistics).mockResolvedValue(mockAnalytics.packages);
      vi.mocked(mockAllocationService.getAllocationStatistics).mockResolvedValue(mockAnalytics.allocations);

      const response = await client.credits.admin.analytics.$get();
      expect(response.status).toBe(200);
    });
  });
});
