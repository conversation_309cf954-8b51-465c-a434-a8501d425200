import type { 
  CreditAccount, 
  CreditTransaction, 
  CreditTransactionType,
  CreditAnalyticsData,
  PaginatedResponse 
} from '../types/credit-types';

// ================================
// 积分计算工具函数
// ================================

/**
 * 计算账户的实际可用余额（排除已过期的积分）
 */
export function calculateAvailableBalance(
  account: CreditAccount,
  transactions: CreditTransaction[]
): number {
  const now = new Date();
  
  // 获取所有未过期的积分交易
  const validTransactions = transactions.filter(tx => {
    // 如果没有过期时间，则认为永不过期
    if (!tx.expiresAt) return true;
    // 如果已经标记为过期，则排除
    if (tx.expiredAt) return false;
    // 检查是否已过期
    return tx.expiresAt > now;
  });
  
  // 计算有效积分余额
  return validTransactions.reduce((balance, tx) => {
    return balance + tx.amount;
  }, 0);
}

/**
 * 检查账户是否有足够的积分进行消耗
 */
export function hasEnoughCredits(
  account: CreditAccount,
  requiredAmount: number,
  transactions?: CreditTransaction[]
): boolean {
  if (transactions) {
    const availableBalance = calculateAvailableBalance(account, transactions);
    return availableBalance >= requiredAmount;
  }
  
  return account.currentBalance >= requiredAmount;
}

/**
 * 计算积分交易后的新余额
 */
export function calculateNewBalance(
  currentBalance: number,
  transactionAmount: number
): number {
  const newBalance = currentBalance + transactionAmount;
  return Math.max(0, newBalance); // 确保余额不为负数
}

/**
 * 格式化积分数量显示
 */
export function formatCreditAmount(amount: number): string {
  if (amount === 0) return '0';
  if (amount < 1000) return amount.toString();
  if (amount < 1000000) return `${(amount / 1000).toFixed(1)}K`;
  return `${(amount / 1000000).toFixed(1)}M`;
}

/**
 * 格式化积分交易类型显示
 */
export function formatTransactionType(type: CreditTransactionType): string {
  const typeMap: Record<CreditTransactionType, string> = {
    EARNED_SUBSCRIPTION: '订阅分配',
    EARNED_PURCHASE: '购买获得',
    EARNED_BONUS: '奖励获得',
    EARNED_REFERRAL: '推荐奖励',
    EARNED_SIGNIN: '签到奖励',
    SPENT_FEATURE: '功能消耗',
    SPENT_TRANSFER: '转账支出',
    RECEIVED_TRANSFER: '转账收入',
    EXPIRED: '积分过期',
    REFUNDED: '退款返还',
    ADJUSTMENT: '管理员调整'
  };
  
  return typeMap[type] || type;
}

/**
 * 判断交易类型是否为收入类型
 */
export function isEarnedTransaction(type: CreditTransactionType): boolean {
  return type.startsWith('EARNED_') || type === 'RECEIVED_TRANSFER' || type === 'REFUNDED';
}

/**
 * 判断交易类型是否为支出类型
 */
export function isSpentTransaction(type: CreditTransactionType): boolean {
  return type.startsWith('SPENT_') || type === 'EXPIRED';
}

// ================================
// 时间和日期工具函数
// ================================

/**
 * 检查积分是否即将过期
 */
export function isExpiringSoon(expiresAt: Date | null, warningDays: number = 7): boolean {
  if (!expiresAt) return false;
  
  const now = new Date();
  const warningDate = new Date(expiresAt);
  warningDate.setDate(warningDate.getDate() - warningDays);
  
  return now >= warningDate && now < expiresAt;
}

/**
 * 计算积分剩余有效天数
 */
export function getDaysUntilExpiration(expiresAt: Date | null): number | null {
  if (!expiresAt) return null;
  
  const now = new Date();
  const diffTime = expiresAt.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return Math.max(0, diffDays);
}

/**
 * 格式化过期时间显示
 */
export function formatExpirationDate(expiresAt: Date | null): string {
  if (!expiresAt) return '永不过期';
  
  const days = getDaysUntilExpiration(expiresAt);
  if (days === null) return '永不过期';
  if (days === 0) return '今天过期';
  if (days === 1) return '明天过期';
  if (days <= 7) return `${days}天后过期`;
  if (days <= 30) return `${Math.ceil(days / 7)}周后过期`;
  
  return expiresAt.toLocaleDateString();
}

// ================================
// 数据分析工具函数
// ================================

/**
 * 按日期分组交易记录
 */
export function groupTransactionsByDate(
  transactions: CreditTransaction[],
  groupBy: 'day' | 'week' | 'month' = 'day'
): Record<string, CreditTransaction[]> {
  const groups: Record<string, CreditTransaction[]> = {};
  
  transactions.forEach(tx => {
    const date = new Date(tx.createdAt);
    let key: string;
    
    switch (groupBy) {
      case 'day':
        key = date.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
    }
    
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(tx);
  });
  
  return groups;
}

/**
 * 计算积分分析数据
 */
export function calculateCreditAnalytics(
  transactions: CreditTransaction[],
  groupBy: 'day' | 'week' | 'month' = 'day'
): CreditAnalyticsData[] {
  const groups = groupTransactionsByDate(transactions, groupBy);
  const analytics: CreditAnalyticsData[] = [];
  
  let runningBalance = 0;
  
  // 按日期排序
  const sortedDates = Object.keys(groups).sort();
  
  sortedDates.forEach(date => {
    const dayTransactions = groups[date];
    const earned = dayTransactions
      .filter(tx => isEarnedTransaction(tx.type))
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    const spent = dayTransactions
      .filter(tx => isSpentTransaction(tx.type))
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    runningBalance += earned - spent;
    
    analytics.push({
      date,
      earned,
      spent,
      balance: Math.max(0, runningBalance),
      transactions: dayTransactions.length
    });
  });
  
  return analytics;
}

/**
 * 计算积分使用趋势
 */
export function calculateUsageTrend(
  currentPeriod: CreditAnalyticsData[],
  previousPeriod: CreditAnalyticsData[]
): {
  earnedChange: number;
  spentChange: number;
  balanceChange: number;
  transactionChange: number;
} {
  const currentTotals = currentPeriod.reduce(
    (acc, data) => ({
      earned: acc.earned + data.earned,
      spent: acc.spent + data.spent,
      transactions: acc.transactions + data.transactions
    }),
    { earned: 0, spent: 0, transactions: 0 }
  );
  
  const previousTotals = previousPeriod.reduce(
    (acc, data) => ({
      earned: acc.earned + data.earned,
      spent: acc.spent + data.spent,
      transactions: acc.transactions + data.transactions
    }),
    { earned: 0, spent: 0, transactions: 0 }
  );
  
  const currentBalance = currentPeriod[currentPeriod.length - 1]?.balance || 0;
  const previousBalance = previousPeriod[previousPeriod.length - 1]?.balance || 0;
  
  return {
    earnedChange: calculatePercentageChange(previousTotals.earned, currentTotals.earned),
    spentChange: calculatePercentageChange(previousTotals.spent, currentTotals.spent),
    balanceChange: calculatePercentageChange(previousBalance, currentBalance),
    transactionChange: calculatePercentageChange(previousTotals.transactions, currentTotals.transactions)
  };
}

/**
 * 计算百分比变化
 */
export function calculatePercentageChange(oldValue: number, newValue: number): number {
  if (oldValue === 0) return newValue > 0 ? 100 : 0;
  return ((newValue - oldValue) / oldValue) * 100;
}

// ================================
// 分页工具函数
// ================================

/**
 * 创建分页响应
 */
export function createPaginatedResponse<T>(
  items: T[],
  total: number,
  page: number,
  limit: number
): PaginatedResponse<T> {
  return {
    items,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
}

/**
 * 计算分页偏移量
 */
export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

// ================================
// 验证工具函数
// ================================

/**
 * 验证账户ID格式
 */
export function isValidAccountId(accountId: string): boolean {
  return typeof accountId === 'string' && accountId.length > 0 && accountId.length <= 50;
}

/**
 * 验证积分数量
 */
export function isValidCreditAmount(amount: number): boolean {
  return Number.isInteger(amount) && amount > 0 && amount <= 1000000;
}

/**
 * 验证日期范围
 */
export function isValidDateRange(startDate: Date, endDate: Date): boolean {
  return startDate <= endDate && endDate <= new Date();
}

/**
 * 清理和验证元数据
 */
export function sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  Object.entries(metadata).forEach(([key, value]) => {
    // 只保留基本类型的值
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      sanitized[key] = value;
    } else if (value === null) {
      sanitized[key] = null;
    }
  });
  
  return sanitized;
}
