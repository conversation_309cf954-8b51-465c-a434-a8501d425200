'use client';

import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { useEffect } from 'react';
import { trackPageView } from '@repo/monitoring/analytics';

interface MonitoringProviderProps {
  children: React.ReactNode;
}

export function MonitoringProvider({ children }: MonitoringProviderProps) {
  useEffect(() => {
    // 页面加载完成后追踪页面访问
    trackPageView(window.location.pathname);
    
    // 监听路由变化（如果使用 Next.js App Router）
    const handleRouteChange = () => {
      trackPageView(window.location.pathname);
    };
    
    // 监听 popstate 事件（浏览器前进后退）
    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);
  
  return (
    <>
      {children}
      
      {/* Vercel Analytics */}
      <Analytics />
      
      {/* Vercel Speed Insights */}
      <SpeedInsights />
      
      {/* Umami Analytics Script (可选) */}
      {process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID && (
        <script
          async
          src={process.env.NEXT_PUBLIC_UMAMI_URL || 'https://umami.is/script.js'}
          data-website-id={process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID}
        />
      )}
    </>
  );
}
